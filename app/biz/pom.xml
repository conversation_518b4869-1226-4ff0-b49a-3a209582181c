<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
        <groupId>com.ly.travel.car.carowner</groupId>
        <artifactId>shared-mobility-carowner-core</artifactId>
        <version>1.0-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>shared-mobility-carowner-core-biz</artifactId>
    <packaging>jar</packaging>

    <name>LY shared-mobility-carowner-core-biz</name>
    <description>LY shared-mobility-carowner-core-biz</description>

    <dependencies>
        <!-- project depends -->
        <dependency>
            <groupId>com.ly.travel.car.carowner</groupId>
            <artifactId>shared-mobility-carowner-core-integration</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ly.travel.car.carowner</groupId>
            <artifactId>shared-mobility-carowner-core-dal</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ly.travel.car.carowner</groupId>
            <artifactId>shared-mobility-carowner-core-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ly.travel.car.carowner</groupId>
            <artifactId>shared-mobility-carowner-core-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ly.sof</groupId>
            <artifactId>sof-core</artifactId>
        </dependency>

        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
            <version>2.0.1.Final</version>
        </dependency>

        <!-- Test dependecies -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-all</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.easymock</groupId>
            <artifactId>easymock</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-easymock</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- excel -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>

        <!-- form basic -->
        <dependency>
            <artifactId>sof-batis-gen-dependency</artifactId>
            <groupId>com.ly.flight.toolkit</groupId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
        </dependency>

        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>

    </dependencies>
</project>
