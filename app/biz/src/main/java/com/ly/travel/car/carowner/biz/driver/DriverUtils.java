package com.ly.travel.car.carowner.biz.driver;

import com.ly.travel.car.carowner.common.enums.VerifyStatusEnum;

/**
 * <AUTHOR>
 * @version DriverUtils, 2025/9/10 01:04
 */
public class DriverUtils {

    public static VerifyStatusEnum getFinalStatus(Integer driverStatus, Integer vehicleStatus) {
        //        （1）人、车均通过验证：总状态-已认证；
        //        （2）人、车任一认证失败：总状态-认证失败；
        //        （3）人、车都未提交认证：总状态-未认证；
        //        （4）补充：人、车任一认证过期，且其他认证成功：总状态-认证过期；
        //        （5）其他：认证中
        if (VerifyStatusEnum.VERIFY_ADOPT.getCode() == driverStatus && VerifyStatusEnum.VERIFY_ADOPT.getCode() == vehicleStatus) {
            return VerifyStatusEnum.VERIFY_ADOPT;
        } else if (VerifyStatusEnum.VERIFY_REJECT.getCode() == driverStatus || VerifyStatusEnum.VERIFY_REJECT.getCode() == vehicleStatus) {
            return VerifyStatusEnum.VERIFY_REJECT;
        } else if (VerifyStatusEnum.NO_VERIFY.getCode() == driverStatus && VerifyStatusEnum.NO_VERIFY.getCode() == vehicleStatus) {
            return VerifyStatusEnum.NO_VERIFY;
        } else if (VerifyStatusEnum.VERIFY_TIMEOUT.getCode() == driverStatus || VerifyStatusEnum.VERIFY_TIMEOUT.getCode() == vehicleStatus) {
            return VerifyStatusEnum.VERIFY_TIMEOUT;
        } else {
            return VerifyStatusEnum.VERIFY_PERSON_ING;
        }
    }
}
