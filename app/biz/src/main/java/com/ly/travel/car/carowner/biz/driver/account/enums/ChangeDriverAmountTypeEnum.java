package com.ly.travel.car.carowner.biz.driver.account.enums;

import com.ly.travel.car.carowner.biz.driver.account.service.ChangeDriverAmountProcess;
import com.ly.travel.car.carowner.biz.driver.account.service.impl.*;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum ChangeDriverAmountTypeEnum {

    ORDER_COMPLETE(1, "完单收益", ChangeDriverAmount_OrderComplete.class),

    RE_FREEZE_BILL(2, "重新冻结", ChangeDriverAmount_REFreezeBill.class),

    UN_FREEZE_BILL(3, "解冻", ChangeDriverAmount_UnFreezeBill.class),

    ORDER_REFUND(4, "退款", ChangeDriverAmount_OrderRefund.class),

    WITHDRAWING(5, "提现中", ChangeDriverAmount_Withdrawing.class),

    FAILURE_BACK(6, "提现失败", ChangeDriverAmount_WithdrawalFail.class),

    WITHDRAWAL_SUCCESS(7, "提现成功", ChangeDriverAmount_WithdrawalSuccess.class),

    ACCOUNT_ADJUSTMENT(8, "调账", ChangeDriverAmount_AccountAdjustment.class),

    CARD_ORDER_COMPLETE(11, "免佣卡完单收益", ChangeDriverAmount_RewardOrderComplete.class),

    CARD_RE_FREEZE_BILL(12, "免佣卡重新冻结", ChangeDriverAmount_RewardREFreezeBill.class),

    CARD_UN_FREEZE_BILL(13, "免佣卡解冻", ChangeDriverAmount_RewardUnFreezeBill.class),

    CARD_ORDER_REFUND(14, "免佣卡退款", ChangeDriverAmount_RewardOrderRefund.class),


    ;

    private int code;

    private String desc;

    private Class<? extends ChangeDriverAmountProcess> aClass;

    public static ChangeDriverAmountTypeEnum getByCode(Integer value) {
        if (null == value) {
            return null;
        }
        ChangeDriverAmountTypeEnum[] elements = values();
        for (ChangeDriverAmountTypeEnum element : elements) {
            if (element.getCode() == value.intValue()) {
                return element;
            }
        }
        return null;
    }
}
