package com.ly.travel.car.carowner.biz.driver.account.service;

import com.alibaba.fastjson.JSON;
import com.ly.travel.car.carowner.biz.driver.account.enums.ChangeDriverAmountTypeEnum;
import com.ly.travel.car.carowner.common.cache.CacheUtils;
import com.ly.travel.car.carowner.common.enums.CacheKeyEnum;
import com.ly.travel.car.carowner.common.exception.DriverAccountException;
import com.ly.travel.car.carowner.common.lock.DistributedRedisLock;
import com.ly.travel.car.carowner.common.utils.ApplicationContextUtils;
import com.ly.travel.car.carowner.common.utils.CarownerConfigCenterUtils;
import com.ly.travel.car.carowner.common.utils.DateUtil;
import com.ly.travel.car.carowner.dal.dataobject.DriverAccountDO;
import com.ly.travel.car.carowner.dal.dataobject.DriverInfoDO;
import com.ly.travel.car.carowner.dal.mapper.DriverInfoMapper;
import com.ly.travel.car.carowner.integration.client.api.lbs.DistanceRsp;
import com.ly.travel.car.carowner.integration.wxwork.WxWorkAlertComponent;
import com.ly.travel.car.carowner.integration.wxwork.WxWorkTemplateEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;

@Slf4j
public abstract class ChangeDriverAmountProcess {

    @Autowired
    private DriverAccountService driverAccountService;

    @Autowired
    private WxWorkAlertComponent wxWorkAlertComponent;

    @Autowired
    private DriverInfoMapper driverInfoMapper;

    @Autowired
    private DistributedRedisLock distributedRedisLock;

    public static ChangeDriverAmountProcess getInstance(ChangeDriverAmountTypeEnum changeDriverAmountTypeEnum) {
        return ApplicationContextUtils.getContext().getBean(changeDriverAmountTypeEnum.getAClass());
    }

    //不防重复，谨慎使用
    public boolean changeAmount(long driverId, BigDecimal amount) {

        distributedRedisLock.lockWithUnlock("Driver_Amount:"+driverId,(lock)->{
            DriverAccountDO driverAccountDO = driverAccountService.queryDriverAccount(driverId);
            if (driverAccountDO == null) {
                log.error("司机账户不存在,", driverId);
                throw new DriverAccountException("司机账户不存在," + driverId);
            }
            log.info("修改司机账户余额,之前账户信息:{}", JSON.toJSONString(driverAccountDO));
            DriverAccountDO updaetDriverAccountDO = beforeChangeAmount(driverAccountDO, amount);
            log.info("修改司机账户余额,变动账户信息:{}", JSON.toJSONString(updaetDriverAccountDO));
            if (driverAccountService.updateDriverAccount(updaetDriverAccountDO) <= 0) {
                log.info("修改司机账户余额,失败,{}", updaetDriverAccountDO.getDriverId());
            }

            // 账户可用余额小于0机器人通知
            accountBalanceNotice(driverId);

            afterChangeAmount(updaetDriverAccountDO, amount);
        });
        return true;
    }

    //防重复
    public synchronized boolean changeAmount_sync(long driverId, BigDecimal amount,String voucherNo) {
        log.info("修改司机账户余额,{},{},{}", driverId,amount,voucherNo);

        if(!CacheUtils.lockKey(CacheKeyEnum.CHANGE_DRIVER_AMOUNT.format(voucherNo,this.getClass().getSimpleName()),"1", CacheKeyEnum.CHANGE_DRIVER_AMOUNT.getExpire())){
            log.warn("账单操作账户重复类型放,{},{},{}", driverId,amount,voucherNo);
            return false;
        }
        distributedRedisLock.lockWithUnlock("Driver_Amount:"+driverId,(lock)-> {

            DriverAccountDO driverAccountDO = driverAccountService.queryDriverAccount(driverId);
            if (driverAccountDO == null) {
                log.error("司机账户不存在,{}", driverId);
                throw new DriverAccountException("司机账户不存在," + driverId);
            }
            log.info("修改司机账户余额,之前账户信息:{}", JSON.toJSONString(driverAccountDO));
            DriverAccountDO updaetDriverAccountDO = beforeChangeAmount(driverAccountDO, amount);
            log.info("修改司机账户余额,变动账户信息:{}", JSON.toJSONString(updaetDriverAccountDO));
            if (driverAccountService.updateDriverAccount(updaetDriverAccountDO) <= 0) {
                log.info("修改司机账户余额,失败,{}", updaetDriverAccountDO.getDriverId());
            }

            // 账户可用余额小于0机器人通知
            accountBalanceNotice(driverId);

            afterChangeAmount(updaetDriverAccountDO, amount);
        });
        return true;
    }

    public abstract DriverAccountDO beforeChangeAmount(DriverAccountDO driverAccount, BigDecimal amount);

    public abstract void afterChangeAmount(DriverAccountDO driverAccount, BigDecimal amount);

    protected void accountBalanceNotice(Long driverId) {
        DriverAccountDO driverAccountDO = driverAccountService.queryDriverAccount(driverId);
        BigDecimal availableAmount = driverAccountDO.getAvailableAmount();
        if (availableAmount != null && availableAmount.compareTo(BigDecimal.ZERO) < 0) {
            DriverInfoDO driverInfoDO = driverInfoMapper.selectById(driverId);
            wxWorkAlertComponent.alert(
                    WxWorkTemplateEnum.AMOUNT_NO_THAN_ZERO_NOTICE,
                    Arrays.asList(CarownerConfigCenterUtils.noticeUserConfig.split(",")),
                    driverInfoDO.getName(),
                    availableAmount,
                    DateUtil.date2String(new Date())
            );
            log.info("司机账户余额小于0通知成功: driverId={}，driverAccount={}", driverId, availableAmount);
        }
    }
}
