package com.ly.travel.car.carowner.biz.driver.account.service;

import com.ly.travel.car.carowner.dal.dataobject.DriverAccountDO;
import com.ly.travel.car.carowner.facade.request.FreezeDriverAccountRequest;
import com.ly.travel.car.carowner.facade.request.UnfreezeDriverAccountRequest;
import com.ly.travel.car.carowner.facade.response.CarOwnerResponseDTO;

public interface DriverAccountService {

    /**
     * 订单完成修改司机账户
     */
    int updateDriverAccount(DriverAccountDO driverAccountDO);

    DriverAccountDO queryDriverAccount(long driverId);

    CarOwnerResponseDTO freezeDriverAccount(FreezeDriverAccountRequest request);

    CarOwnerResponseDTO unfreezeDriverAccount(UnfreezeDriverAccountRequest request);
}
