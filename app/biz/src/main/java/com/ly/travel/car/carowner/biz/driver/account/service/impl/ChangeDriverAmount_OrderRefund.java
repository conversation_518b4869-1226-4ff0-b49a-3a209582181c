package com.ly.travel.car.carowner.biz.driver.account.service.impl;

import com.ly.travel.car.carowner.biz.driver.account.service.ChangeDriverAmountProcess;
import com.ly.travel.car.carowner.dal.dataobject.DriverAccountDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;

// 退款
@Component
@Slf4j
public class ChangeDriverAmount_OrderRefund extends ChangeDriverAmountProcess {

    @Override
    public DriverAccountDO beforeChangeAmount(DriverAccountDO driverAccount, BigDecimal amount) {
        // amount为负
        DriverAccountDO updateDriverAccountDO = new DriverAccountDO();
        updateDriverAccountDO.setDriverId(driverAccount.getDriverId());
        updateDriverAccountDO.setTotalAmount(amount);
        updateDriverAccountDO.setAvailableAmount(amount);  //TODO 分两种情况，订单还在冻结中直接可以扣冻结金额，  订单如果已解冻则扣可提现金额
        updateDriverAccountDO.setUpdateTime(new Date());
        updateDriverAccountDO.setUpdateUser("订单退款");
        return updateDriverAccountDO;
    }

    @Override
    public void afterChangeAmount(DriverAccountDO updateDriverAccountDO, BigDecimal amount) {

    }
}
