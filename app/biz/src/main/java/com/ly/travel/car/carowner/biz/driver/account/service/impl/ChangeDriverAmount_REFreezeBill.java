package com.ly.travel.car.carowner.biz.driver.account.service.impl;

import com.ly.travel.car.carowner.biz.driver.account.service.ChangeDriverAmountProcess;
import com.ly.travel.car.carowner.dal.dataobject.DriverAccountDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;

// 重新冻结
@Component
@Slf4j
public class ChangeDriverAmount_REFreezeBill extends ChangeDriverAmountProcess {

    @Override
    public DriverAccountDO beforeChangeAmount(DriverAccountDO driverAccount, BigDecimal amount) {
        DriverAccountDO updateDriverAccountDO=new DriverAccountDO();
        updateDriverAccountDO.setDriverId(driverAccount.getDriverId());
        updateDriverAccountDO.setFreezeAmount(amount);
        updateDriverAccountDO.setAvailableAmount(amount.negate());
        updateDriverAccountDO.setUpdateUser("重新解冻");
        updateDriverAccountDO.setUpdateTime(new Date());
        return updateDriverAccountDO;
    }

    @Override
    public void afterChangeAmount(DriverAccountDO updateDriverAccountDO, BigDecimal amount) {

    }
}
