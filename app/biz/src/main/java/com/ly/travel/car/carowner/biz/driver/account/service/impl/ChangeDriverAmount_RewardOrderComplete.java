package com.ly.travel.car.carowner.biz.driver.account.service.impl;

import com.ly.travel.car.carowner.biz.driver.account.service.ChangeDriverAmountProcess;
import com.ly.travel.car.carowner.dal.dataobject.DriverAccountDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;

// 完单
@Component
@Slf4j
public class ChangeDriverAmount_RewardOrderComplete extends ChangeDriverAmountProcess {

    @Override
    public DriverAccountDO beforeChangeAmount(DriverAccountDO driverAccount, BigDecimal amount) {
        DriverAccountDO updateDriverAccountDO = new DriverAccountDO();
        updateDriverAccountDO.setDriverId(driverAccount.getDriverId());
        updateDriverAccountDO.setTotalAmount(amount);
        updateDriverAccountDO.setFreezeAmount(amount);
        updateDriverAccountDO.setUpdateTime(new Date());
        updateDriverAccountDO.setUpdateUser("免佣卡订单完单");

        return updateDriverAccountDO;
    }

    @Override
    public void afterChangeAmount(DriverAccountDO updateDriverAccountDO, BigDecimal amount) {

    }
}
