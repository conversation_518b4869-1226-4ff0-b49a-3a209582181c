package com.ly.travel.car.carowner.biz.driver.account.service.impl;

import com.ly.travel.car.carowner.biz.driver.account.service.ChangeDriverAmountProcess;
import com.ly.travel.car.carowner.dal.dataobject.DriverAccountDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;

// 退款
@Component
@Slf4j
public class ChangeDriverAmount_RewardOrderRefund extends ChangeDriverAmountProcess {

    @Override
    public DriverAccountDO beforeChangeAmount(DriverAccountDO driverAccount, BigDecimal amount) {
        // amount为负
        DriverAccountDO updateDriverAccountDO = new DriverAccountDO();
        updateDriverAccountDO.setDriverId(driverAccount.getDriverId());
        updateDriverAccountDO.setTotalAmount(amount);
        updateDriverAccountDO.setAvailableAmount(amount);
        updateDriverAccountDO.setUpdateTime(new Date());
        updateDriverAccountDO.setUpdateUser("订单退款");
        updateDriverAccountDO.setRewardAmount(amount); //奖励金
        return updateDriverAccountDO;
    }

    @Override
    public void afterChangeAmount(DriverAccountDO updateDriverAccountDO, BigDecimal amount) {

    }
}
