package com.ly.travel.car.carowner.biz.driver.account.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.biz.driver.account.service.DriverAccountService;
import com.ly.travel.car.carowner.common.enums.IsFreezeEnum;
import com.ly.travel.car.carowner.common.enums.ResultEnum;
import com.ly.travel.car.carowner.common.exception.BusinessException;
import com.ly.travel.car.carowner.common.utils.DateUtil;
import com.ly.travel.car.carowner.dal.dataobject.DriverAccountDO;
import com.ly.travel.car.carowner.dal.mapper.DriverAccountMapper;
import com.ly.travel.car.carowner.facade.request.FreezeDriverAccountRequest;
import com.ly.travel.car.carowner.facade.request.UnfreezeDriverAccountRequest;
import com.ly.travel.car.carowner.facade.response.CarOwnerResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Service
@Slf4j
public class DriverAccountServiceImpl implements DriverAccountService {
    @Resource
    private DriverAccountMapper driverAccountMapper;

    @Override
    public int updateDriverAccount(DriverAccountDO driverAccountDO) {
        return driverAccountMapper.updateAmountByDriverId(driverAccountDO);
    }

    @Override
    public DriverAccountDO queryDriverAccount(long driverId) {
        DriverAccountDO driverAccountDO = driverAccountMapper.selectOne(new LambdaQueryWrapper<DriverAccountDO>().eq(DriverAccountDO::getDriverId, driverId));
        return driverAccountDO;
    }

    @Override
    public CarOwnerResponseDTO freezeDriverAccount(FreezeDriverAccountRequest request) {
        try {
            // 1、入参验证
            String validate = request.validate();
            if (StringUtils.isNotBlank(validate)) {
                log.warn(validate);
                throw new BusinessException(validate);
            }

            // 2、冻结账户
            freezeAccount(request);

        } catch (BusinessException e) {
            return CarOwnerResponseDTO.fail(e.getMessage());
        } catch (Exception e) {
            log.error("司机账户冻结失败: request={}", FastJsonUtils.toJSONString(request), e);
            return CarOwnerResponseDTO.fail(ResultEnum.INTERNAL_SERVER_ERROR.getDesc());
        }
        return CarOwnerResponseDTO.succeed(null);
    }

    private void freezeAccount(FreezeDriverAccountRequest request) {
        DriverAccountDO driverAccountDO = queryDriverAccount(request.getDriverId());
        if (driverAccountDO == null) {
            log.warn("账户不存在: driverId={}", request.getDriverId());
            throw new BusinessException("账户不存在");
        }

        DriverAccountDO updateEntity = new DriverAccountDO();
        updateEntity.setId(driverAccountDO.getId());
        updateEntity.setFreezeTime(new Date());
        updateEntity.setIsFreeze(IsFreezeEnum.CORRECT.getCode());
        updateEntity.setUpdateTime(new Date());
        updateEntity.setUpdateUser(request.getOperator());
        if (driverAccountMapper.updateById(updateEntity) <= 0) {
            log.warn("司机账户冻结失败: updateEntity={}", FastJsonUtils.toJSONString(updateEntity));
        }
    }

    @Override
    public CarOwnerResponseDTO unfreezeDriverAccount(UnfreezeDriverAccountRequest request) {
        try {
            // 验证入参
            String validate = request.validate();
            if (StringUtils.isNotBlank(validate)) {
                log.warn(validate);
                throw new BusinessException(validate);
            }

            // 解冻账户
            unfreezeAccount(request);

        } catch (BusinessException e) {
            return CarOwnerResponseDTO.fail(e.getMessage());
        } catch (Exception e) {
            log.error("司机账户解冻失败: request={}", FastJsonUtils.toJSONString(request), e);
            return CarOwnerResponseDTO.fail(ResultEnum.INTERNAL_SERVER_ERROR.getDesc());
        }
        return CarOwnerResponseDTO.succeed(null);
    }

    private void unfreezeAccount(UnfreezeDriverAccountRequest request) {
        DriverAccountDO driverAccountDO = queryDriverAccount(request.getDriverId());
        if (driverAccountDO == null) {
            log.warn("账户不存在: driverId={}", request.getDriverId());
            throw new BusinessException("账户不存在");
        }

        DriverAccountDO updateEntity = new DriverAccountDO();
        updateEntity.setId(driverAccountDO.getId());
        updateEntity.setFreezeTime(DateUtil.getMinDate());
        updateEntity.setIsFreeze(IsFreezeEnum.DENY.getCode());
        updateEntity.setUnfreezeTime(DateUtil.getMinDate());
        updateEntity.setUpdateTime(new Date());
        updateEntity.setUpdateUser(request.getOperator());
        if (driverAccountMapper.updateById(updateEntity) <= 0) {
            log.error("司机账户解冻失败: updateEntity={}", FastJsonUtils.toJSONString(updateEntity));
        }
    }
}
