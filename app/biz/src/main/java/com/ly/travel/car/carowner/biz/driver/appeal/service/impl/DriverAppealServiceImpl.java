package com.ly.travel.car.carowner.biz.driver.appeal.service.impl;

import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.biz.driver.account.enums.ChangeDriverAmountTypeEnum;
import com.ly.travel.car.carowner.biz.driver.account.service.ChangeDriverAmountProcess;
import com.ly.travel.car.carowner.biz.driver.appeal.service.DriverAppealService;
import com.ly.travel.car.carowner.biz.order.model.RideOrderInfoVO;
import com.ly.travel.car.carowner.biz.order.service.RideOrderInfoService;
import com.ly.travel.car.carowner.common.enums.*;
import com.ly.travel.car.carowner.common.exception.BusinessException;
import com.ly.travel.car.carowner.common.utils.DateUtil;
import com.ly.travel.car.carowner.common.utils.EnvUtil;
import com.ly.travel.car.carowner.common.utils.IdGeneratorUtil;
import com.ly.travel.car.carowner.dal.dataobject.DriverAppealDO;
import com.ly.travel.car.carowner.dal.dataobject.DriverBillDO;
import com.ly.travel.car.carowner.dal.mapper.DriverAppealMapper;
import com.ly.travel.car.carowner.dal.mapper.DriverBillMapper;
import com.ly.travel.car.carowner.facade.request.DriverAdjustsBillRequest;
import com.ly.travel.car.carowner.facade.response.CarOwnerResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

@Service
@Slf4j
public class DriverAppealServiceImpl implements DriverAppealService {
    @Resource
    private DriverAppealMapper driverAppealMapper;
    @Resource
    private RideOrderInfoService rideOrderInfoService;
    @Resource
    private DriverBillMapper driverBillMapper;

    @Override
    public CarOwnerResponseDTO accountAdjustment(DriverAdjustsBillRequest request) {
        try {
            // 1. 参数校验
            RideOrderInfoVO rideOrderInfoVO = validateRequest(request);

            // 2. 创建申诉信息
            addDriverAppealInfo(request, rideOrderInfoVO);

            // 3. 创建账单
            createBill(request, rideOrderInfoVO);

            // 4. 更改账户
            updateDriverAccount(request, rideOrderInfoVO);

        } catch (BusinessException e) {
            return CarOwnerResponseDTO.fail(e.getMessage());
        } catch (Exception e) {
            log.error("司机调账失败: request={}", FastJsonUtils.toJSONString(request), e);
            return CarOwnerResponseDTO.fail(ResultEnum.INTERNAL_SERVER_ERROR.getDesc());
        }
        return CarOwnerResponseDTO.succeed(null);
    }

    private RideOrderInfoVO validateRequest(DriverAdjustsBillRequest request) {
        if (request == null) {
            log.warn("入参不能为空");
            throw new BusinessException("入参不能为空");
        }

        String validateMessage = request.validate();
        if (StringUtils.isNotBlank(validateMessage)) {
            log.warn(validateMessage);
            throw new BusinessException(validateMessage);
        }

        RideOrderInfoVO rideOrderInfoVO = rideOrderInfoService.queryOrderInfo(request.getOrderNo(), "");
        if (rideOrderInfoVO == null) {
            log.warn("订单不存在: orderNo={}", request.getOrderNo());
            throw new BusinessException("订单不存在");
        }

        return rideOrderInfoVO;
    }

    private void addDriverAppealInfo(DriverAdjustsBillRequest request, RideOrderInfoVO rideOrderInfoVO) {
        DriverAppealDO driverAppeal = new DriverAppealDO();
        driverAppeal.setDriverId(rideOrderInfoVO.getDriverId());
        driverAppeal.setEnv(EnvUtil.getEnv().getValue());
        driverAppeal.setArbitrationTime(DateUtil.string2Date(request.getArbitrationTime()));
        driverAppeal.setAppealContent(request.getRemark());
        driverAppeal.setAppealNo(IdGeneratorUtil.create("S", "S"));
        driverAppeal.setOrderNo(rideOrderInfoVO.getOrderNo());
        driverAppeal.setCreateTime(new Date());
        driverAppeal.setRemark(request.getRemark());
        driverAppeal.setCreateUser(request.getOperator());
        driverAppeal.setAppealAmount(calculateAmount(request));

        if (driverAppealMapper.insert(driverAppeal) <= 0) {
            log.warn("司机申诉信息新增失败: driverAppeal={}", FastJsonUtils.toJSONString(driverAppeal));
            throw new BusinessException("司机申诉信息新增失败");
        }
    }

    private void updateDriverAccount(DriverAdjustsBillRequest request, RideOrderInfoVO rideOrderInfoVO) {
        ChangeDriverAmountProcess.getInstance(ChangeDriverAmountTypeEnum.ACCOUNT_ADJUSTMENT).changeAmount(rideOrderInfoVO.getDriverId(), calculateAmount(request));
    }

    private void createBill(DriverAdjustsBillRequest request, RideOrderInfoVO rideOrderInfoVO) {
        DriverBillDO driverBill = new DriverBillDO();
        driverBill.setBillNo(IdGeneratorUtil.create("L", "S"));
        driverBill.setIsFreeze(IsFreezeEnum.DENY.getCode());
        driverBill.setBillType(determineBillType(request.getType()));
        driverBill.setAmount(calculateAmount(request));
        driverBill.setRemark(request.getRemark());
        driverBill.setVoucherNo(rideOrderInfoVO.getOrderNo());
        driverBill.setVoucherType(VoucherTypeEnum.DRIVER_APPEAL.getType());
        driverBill.setWithdrawNo("");
        driverBill.setDriverId(rideOrderInfoVO.getDriverId());
        driverBill.setCreateTime(new Date());
        driverBill.setEnv(EnvUtil.getEnv().getValue());
        driverBill.setCreateUser(request.getOperator());
        driverBill.setUpdateUser(request.getOperator());

        if (driverBillMapper.insert(driverBill) <= 0) {
            log.warn("调账创建账单失败: driverBill={}", FastJsonUtils.toJSONString(driverBill));
            throw new BusinessException("调账创建账单失败");
        }
    }

    private BigDecimal calculateAmount(DriverAdjustsBillRequest request) {
        if (request.getType() == AdjustsBillTypeEnum.RECHARGE.getType()) {
            return request.getAppealAmount();
        } else if (request.getType() == AdjustsBillTypeEnum.DEDUCTION.getType()) {
            return request.getAppealAmount().negate();
        }
        return BigDecimal.ZERO;
    }

    private int determineBillType(int adjustType) {
        BillTypeEnum billTypeEnum = BillTypeEnum.getByType(adjustType);
        if (billTypeEnum == null) {
            log.warn("非法调账类型: billType={}", adjustType);
            throw new BusinessException("非法调账类型");
        }
        return billTypeEnum.getType();
    }
}