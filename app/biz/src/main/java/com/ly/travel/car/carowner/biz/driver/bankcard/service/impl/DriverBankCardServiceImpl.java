package com.ly.travel.car.carowner.biz.driver.bankcard.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.travel.car.carowner.biz.driver.bankcard.service.DriverBankCardService;
import com.ly.travel.car.carowner.common.exception.BusinessException;
import com.ly.travel.car.carowner.dal.dataobject.DriverBankCardDO;
import com.ly.travel.car.carowner.dal.mapper.DriverBankCardMapper;
import com.ly.travel.car.carowner.facade.request.DeleteBankCardRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @className: DriverBankCardServiceImpl
 * @author: zmj
 * @date: 2025-7-9 11:01
 * @Version: 1.0
 * @description:
 */

@Service
@Slf4j
public class DriverBankCardServiceImpl implements DriverBankCardService {

    @Resource
    private DriverBankCardMapper driverBankCardMapper;

    @Override
    public void batchDeleteBankCard(DeleteBankCardRequest request) {
        if(CollectionUtils.isEmpty(request.getDriverId())){
            throw new BusinessException("参数错误");
        }
       List<DriverBankCardDO> bankCardDOList =  driverBankCardMapper.selectList(new LambdaQueryWrapper<DriverBankCardDO>()
               .in(DriverBankCardDO::getDriverId, request.getDriverId()));
        if(CollectionUtils.isEmpty(bankCardDOList)){
            throw new BusinessException("不存在绑卡数据");
        }
        log.info("删除司机银行卡信息 司机id:{}", bankCardDOList.stream().map(e->e.getDriverId()).collect(Collectors.toList()));
        driverBankCardMapper.deleteBatchIds(bankCardDOList);
    }
}
                        