/**
* RideOrderInfoMapping
* This file is generated by <tt>toolkit(sof-batis-gen)</tt>, a DAL (Data Access Layer)
* @version Id : RideOrderInfoMapping, v 0.1
*/
package com.ly.travel.car.carowner.biz.driver.bill.mapper;

import com.ly.flight.toolkit.converter.BooleanConverter;
import com.ly.flight.toolkit.converter.FullDateConverter;
import com.ly.travel.car.carowner.biz.driver.bill.model.DriverBillVO;
import com.ly.travel.car.carowner.dal.dataobject.DriverBillDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.NullValueMappingStrategy;

import java.util.Collection;
import java.util.List;

@Mapper(componentModel = "spring", nullValueMappingStrategy = NullValueMappingStrategy.RETURN_NULL, uses = { FullDateConverter.class, BooleanConverter.class })
public interface DriverBillMapping {


    @Mappings(value = {
            @Mapping(target = "voucherType", ignore = true),
            @Mapping(target = "billType", ignore = true),
            @Mapping(target = "isFreeze", ignore = true)
    })
    DriverBillDO v2d(DriverBillVO v);

    @Mappings(value = {
            @Mapping(target = "voucherType", ignore = true),
            @Mapping(target = "billType", ignore = true),
            @Mapping(target = "isFreeze", ignore = true)
    })
DriverBillVO d2v(DriverBillDO d);

}
