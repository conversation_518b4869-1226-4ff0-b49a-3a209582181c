package com.ly.travel.car.carowner.biz.driver.bill.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ly.travel.car.carowner.common.enums.BillTypeEnum;
import com.ly.travel.car.carowner.common.enums.IsFreezeEnum;
import com.ly.travel.car.carowner.common.enums.VoucherTypeEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
@Data
public class DriverBillVO {
    /**
     * 流水号        db_column: bill_no
     */

    private String billNo;

    /**
     * 司机账户ID        db_column: driver_id
     */

    private Long driverId;
    /**
     * 业务单据号        db_column: voucher_no
     */

    private String voucherNo;
    /**
     * 业务凭证单据类型枚举 1-订单 2-提现申请 3-调账 4-提现失败回冲        db_column: voucher_type
     */

    private VoucherTypeEnum voucherType;
    /**
     * 明细大类 1-收入 2-支出        db_column: bill_type
     */

    private BillTypeEnum billType;
    /**
     * 金额        db_column: amount
     */

    private BigDecimal amount;
    /**
     * 是否冻结 0-否 1-是        db_column: is_freeze
     */

    private IsFreezeEnum isFreeze;
    /**
     * 备注        db_column: remark
     */
    private String remark;

    private Date unfreezeTime;

    private String withdrawNo;

}
