package com.ly.travel.car.carowner.biz.driver.bill.service;

import com.ly.travel.car.carowner.biz.driver.bill.model.DriverBillVO;
import com.ly.travel.car.carowner.biz.order.model.RideOrderInfoVO;
import com.ly.travel.car.carowner.biz.order.model.RideOrderSettlementVO;
import com.ly.travel.car.carowner.common.enums.FreezeTypeEnum;
import com.ly.travel.car.carowner.dal.dataobject.DriverBillDO;
import com.ly.travel.car.carowner.facade.request.FreezeDriverBillRequest;
import com.ly.travel.car.carowner.facade.request.UnfreezeDriverBillRequest;
import com.ly.travel.car.carowner.facade.response.CarOwnerResponseDTO;

import java.util.Date;
import java.util.List;

public interface DriverBillService {

    CarOwnerResponseDTO freezeDriverBill(FreezeDriverBillRequest request);

    CarOwnerResponseDTO unfreezeDriverBill(UnfreezeDriverBillRequest request);

    String createDriverBill(DriverBillVO driverBillVO);

    List<DriverBillDO> findDriverBillByBillNo(String billNo);

    List<DriverBillDO> findDriverBillByOrderNo(String orderNo);

    List<DriverBillDO> findDriverBillByBillNos(List<String> billNos);

    void insertOrUpdateDriverBillFreeze(DriverBillDO bill, FreezeTypeEnum freezeTypeEnum, Date newUnfreezeTime, String operator);

    List<DriverBillDO> findDriverBillByDriverId(Long driverId);

    List<DriverBillDO> findDriverBillByWithdrawNo(String withdrawNo);

    boolean validateSettlementAmount(RideOrderSettlementVO orderSettlementVO, RideOrderInfoVO rideOrderInfoVO);
}
