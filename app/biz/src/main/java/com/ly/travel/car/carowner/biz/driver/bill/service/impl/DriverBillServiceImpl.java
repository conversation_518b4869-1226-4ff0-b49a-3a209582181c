package com.ly.travel.car.carowner.biz.driver.bill.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.biz.driver.account.enums.ChangeDriverAmountTypeEnum;
import com.ly.travel.car.carowner.biz.driver.account.service.ChangeDriverAmountProcess;
import com.ly.travel.car.carowner.biz.driver.account.service.DriverAccountService;
import com.ly.travel.car.carowner.biz.driver.bill.model.DriverBillVO;
import com.ly.travel.car.carowner.biz.driver.bill.service.DriverBillService;
import com.ly.travel.car.carowner.biz.order.model.RideOrderInfoVO;
import com.ly.travel.car.carowner.biz.order.model.RideOrderSettlementVO;
import com.ly.travel.car.carowner.biz.order.service.RideOrderInfoService;
import com.ly.travel.car.carowner.biz.risk.OrderRiskService;
import com.ly.travel.car.carowner.biz.settlement.service.RideOrderSettlementService;
import com.ly.travel.car.carowner.biz.sys.service.SysConfigService;
import com.ly.travel.car.carowner.biz.turbomq.producer.DriverBillUnFreezeProducer;
import com.ly.travel.car.carowner.common.cache.CacheUtils;
import com.ly.travel.car.carowner.common.enums.*;
import com.ly.travel.car.carowner.common.exception.BusinessException;
import com.ly.travel.car.carowner.common.lock.DistributedRedisLock;
import com.ly.travel.car.carowner.common.utils.CarownerConfigCenterUtils;
import com.ly.travel.car.carowner.common.utils.DateUtil;
import com.ly.travel.car.carowner.common.utils.EnvUtil;
import com.ly.travel.car.carowner.common.utils.IdGeneratorUtil;
import com.ly.travel.car.carowner.dal.dataobject.DriverAccountDO;
import com.ly.travel.car.carowner.dal.dataobject.DriverBillDO;
import com.ly.travel.car.carowner.dal.dataobject.DriverBillFreezeDO;
import com.ly.travel.car.carowner.dal.dataobject.RideOrderRiskInfoDO;
import com.ly.travel.car.carowner.dal.mapper.DriverBillFreezeMapper;
import com.ly.travel.car.carowner.dal.mapper.DriverBillMapper;
import com.ly.travel.car.carowner.dal.mapper.RideOrderRiskInfoMapper;
import com.ly.travel.car.carowner.facade.request.FreezeDriverBillRequest;
import com.ly.travel.car.carowner.facade.request.UnfreezeDriverBillRequest;
import com.ly.travel.car.carowner.facade.response.CarOwnerResponseDTO;
import com.ly.travel.car.carowner.integration.client.api.platform.PlatformOrderApi;
import com.ly.travel.car.carowner.integration.client.api.platform.request.QueryOrderAmountRequest;
import com.ly.travel.car.carowner.integration.client.api.platform.response.QueryOrderAmountResponse;
import com.ly.travel.car.carowner.integration.wxwork.WxWorkAlertComponent;
import com.ly.travel.car.carowner.integration.wxwork.WxWorkTemplateEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DriverBillServiceImpl implements DriverBillService {
    @Resource
    private DriverBillMapper driverBillMapper;
    @Resource
    private DriverBillFreezeMapper driverBillFreezeMapper;
    @Resource
    private DriverAccountService driverAccountService;
    @Resource
    private DriverBillUnFreezeProducer turboMqProducer;

    @Value("${driver.bill.unfreeze.topic}")
    private String driverBillUnfreezeTopic;
    @Resource
    private SysConfigService sysConfigService;
    @Resource
    private RideOrderSettlementService orderSettlementService;
    @Resource
    private RideOrderInfoService rideOrderInfoService;
    @Resource
    private PlatformOrderApi platformOrderApi;
    @Resource
    private WxWorkAlertComponent wxWorkAlertComponent;
    @Resource
    private DistributedRedisLock redisLock;
    @Resource
    private RideOrderRiskInfoMapper rideOrderRiskInfoMapper;
    @Resource
    private OrderRiskService orderRiskService;

    @Override
    public CarOwnerResponseDTO freezeDriverBill(FreezeDriverBillRequest request) {
        try {

            // 1、验证入参
            validateRequest(request);

            // 2、验证是否能冻结
            List<DriverBillDO> driverBillDOS = validateIsFreeze(request);

            // 3、账单冻结
            handleBillFreeze(driverBillDOS, request);

        } catch (BusinessException e) {
            return CarOwnerResponseDTO.fail(e.getMessage());
        } catch (Exception e) {
            log.error("司机账单冻结失败: request={}", FastJsonUtils.toJSONString(request), e);
            return CarOwnerResponseDTO.fail(ResultEnum.INTERNAL_SERVER_ERROR.getDesc());
        }
        return CarOwnerResponseDTO.succeed(null);
    }

    private List<DriverBillDO> validateIsFreeze(FreezeDriverBillRequest request) {
        DriverAccountDO driverAccountDO = driverAccountService.queryDriverAccount(request.getDriverId());
        if (driverAccountDO == null) {
            log.warn("司机账户不存在: orderNo={},driverId={}", request.getOrderNo(), request.getDriverId());
            throw new BusinessException("司机账户不存在");
        }

        // 冻结可用账单
        List<DriverBillDO> driverBillDOS = getDriverBills(request);

        // 冻结类型校验 TODO 结算验证不通过冻结不拦截 解冻时多次验证不通过会调用多次
        if (Objects.equals(FreezeTypeEnum.NORMAL_FREEZE.getType(), request.getFreezeType()) ||
                Objects.equals(FreezeTypeEnum.CUSTOMER_SERVICE_FREEZE.getType(), request.getFreezeType())) {
            driverBillDOS.forEach(driverBillDO -> {
                List<DriverBillFreezeDO> driverBillFreezeDOS = findDriverBillFreezeByBillNo(driverBillDO.getBillNo());
                if (CollectionUtils.isNotEmpty(driverBillFreezeDOS)) {
                    validateFreezeRecords(driverBillFreezeDOS, request.getFreezeType(), driverBillDO.getBillNo());
                }
            });
        }

        return driverBillDOS;
    }

    private void validateRequest(Object request) {
        if (request == null) {
            log.warn("入参不能为空");
            throw new BusinessException("入参不能为空");
        }

        String validateMessage = "";
        if (request instanceof FreezeDriverBillRequest) {
            validateMessage = ((FreezeDriverBillRequest) request).validate();
        } else if (request instanceof UnfreezeDriverBillRequest) {
            validateMessage = ((UnfreezeDriverBillRequest) request).validate();
        }

        if (StringUtils.isNotBlank(validateMessage)) {
            log.warn(validateMessage);
            throw new BusinessException(validateMessage);
        }
    }

    private void validateFreezeRecords(List<DriverBillFreezeDO> freezeRecords, Integer freezeType, String billNo) {
        boolean hasConflict = freezeRecords.stream().anyMatch(freezeRecord ->
                Objects.equals(freezeRecord.getFreezeType(), freezeType) &&
                        Objects.equals(freezeRecord.getUnfreezeFlag(), UnFreezeFlagEnum.UNFREEZE.getCode())
        );

        if (hasConflict) {
            FreezeTypeEnum freezeTypeEnum = FreezeTypeEnum.convertEnum(freezeType);
            String format = String.format("账单号%s已存在%s，无法再次冻结", billNo, freezeTypeEnum.getName());
            log.warn(format);
            throw new BusinessException(format);
        }
    }

    private List<DriverBillDO> getDriverBills(FreezeDriverBillRequest request) {
        List<DriverBillDO> driverBillDOS = new ArrayList<>();

        // 根据账单号查询
        if (request.getOrderNo().startsWith(BusinessNoPrefix.BILL_NO_PREFIX.getPrefix())) {
            driverBillDOS = findDriverBillByBillNo(request.getOrderNo());
        }

        // 根据订单号查询
        if (request.getOrderNo().startsWith(BusinessNoPrefix.ORDER_NO_Prefix.getPrefix())) {
            driverBillDOS = findDriverBillByOrderNo(request.getOrderNo());
        }

        if (CollectionUtils.isEmpty(driverBillDOS)) {
            log.warn("冻结账单无关联流水: orderNo={}", request.getOrderNo());
            throw new BusinessException("冻结账单无关联流水");
        }

        // 筛选未提现流水
        List<DriverBillDO> billDOS = driverBillDOS.stream().filter(bill -> StringUtils.isBlank(bill.getWithdrawNo())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(billDOS)) {
            log.warn("冻结账单流水已提现无可冻结流水: orderNo={}", request.getOrderNo());
            throw new BusinessException("冻结账单流水已提现无可冻结流水");
        }

        return billDOS;
    }

    private void handleBillFreeze(List<DriverBillDO> freezeBills, FreezeDriverBillRequest request) {
        log.info("账单冻结流水: freezeBills={}", FastJsonUtils.toJSONString(freezeBills));
        Date newUnfreezeTime = request.getFreezeHour() == null && request.getFreezeSecond() == null ? DateUtil.MIN_DATE :
                request.getFreezeHour() != null ? DateUtils.addHours(new Date(), request.getFreezeHour()) : DateUtils.addSeconds(new Date(), request.getFreezeSecond());
        FreezeTypeEnum freezeTypeEnum = FreezeTypeEnum.convertEnum(request.getFreezeType());
        if (freezeTypeEnum == null) {
            log.warn("冻结类型不存在: freezeType={}", request.getFreezeType());
            return;
        }

        for (DriverBillDO bill : freezeBills) {
            // 账单金额小于0
            if (bill.getAmount().compareTo(BigDecimal.ZERO) > 0) {
                RLock lock = null;
                String lockKey = "";
                try {
                    lockKey = CacheKeyEnum.DRIVER_BILL.format(bill.getBillNo());
                    lock = redisLock.tryAcquire(lockKey, FastJsonUtils.toJSONString(request), 20, 30, TimeUnit.SECONDS);
                    if (lock == null) {
                        log.warn("账单冻结获取并发锁失败: lockKey={}", lockKey);
                        throw new BusinessException("账单冻结获取并发锁失败");
                    }

                    boolean isFreeze = findDriveBillIsFreeze(bill.getBillNo(), freezeTypeEnum);
                    if (isFreeze) {
                        log.warn("账单已存在该类型冻结记录，跳过本次冻结: billNo={},freezeType={}", bill.getBillNo(), freezeTypeEnum.getType());
                        continue;
                    }

                    // 更新司机账户金额
                    updateDriverAccountAmount(bill);

                    // 新增或更新账单冻结信息
                    insertOrUpdateDriverBillFreeze(bill, freezeTypeEnum, newUnfreezeTime, request.getOperator());

                    // 更新账单信息
                    updateDriverBill(bill, newUnfreezeTime, request);

                    // 发送延迟解冻mq
                    sendUnfreezeDelayMQ(bill, request);

                } finally {
                    if (lock != null) {
                        redisLock.release(lock, lockKey, "");
                    }
                }
            }
        }
    }

    private boolean findDriveBillIsFreeze(String billNo, FreezeTypeEnum freezeTypeEnum) {
        boolean isFreeze = false;
        List<DriverBillFreezeDO> driverBillFreezeDOS = findDriverBillFreezeByBillNo(billNo);
        if (CollectionUtils.isEmpty(driverBillFreezeDOS)) {
            return false;
        }

        for (DriverBillFreezeDO driverBillFreezeDO : driverBillFreezeDOS) {
            if (freezeTypeEnum.getType() == driverBillFreezeDO.getFreezeType() && driverBillFreezeDO.getUnfreezeFlag() == UnFreezeFlagEnum.UNFREEZE.getCode()) {
                isFreeze = true;
                break;
            }
        }
        return isFreeze;
    }

    public void insertOrUpdateDriverBillFreeze(DriverBillDO bill, FreezeTypeEnum freezeTypeEnum, Date newUnfreezeTime, String operator) {
        DriverBillFreezeDO driverBillFreezeDO = findDriverBillFreezeByBillNoAndFreezeType(bill.getBillNo(), freezeTypeEnum.getType());
        boolean isCustomerServiceFreeze = Objects.equals(freezeTypeEnum.getType(), FreezeTypeEnum.CUSTOMER_SERVICE_FREEZE.getType());
        if (driverBillFreezeDO == null) {
            driverBillFreezeDO = new DriverBillFreezeDO();
            driverBillFreezeDO.setBillNo(bill.getBillNo());
            driverBillFreezeDO.setFreezeType(freezeTypeEnum.getType());
            driverBillFreezeDO.setFreezeTime(new Date());
            driverBillFreezeDO.setCreateUser(StringUtils.isBlank(operator) ? "system" : operator);
            driverBillFreezeDO.setEnv(EnvUtil.getEnv().getValue());
            driverBillFreezeDO.setUnfreezeFlag(UnFreezeFlagEnum.UNFREEZE.getCode());
            driverBillFreezeDO.setUnfreezeTime(isCustomerServiceFreeze ? DateUtil.string2Date("2099-01-01 00:00:00") : newUnfreezeTime);
            driverBillFreezeDO.setDriverId(bill.getDriverId());
            driverBillFreezeDO.setCreateTime(new Date());
            if (driverBillFreezeMapper.insert(driverBillFreezeDO) <= 0) {
                log.warn("新增账单冻结类型信息失败: driverBillFreezeDO={}", FastJsonUtils.toJSONString(driverBillFreezeDO));
            }
        } else {
            DriverBillFreezeDO updateEntity = new DriverBillFreezeDO();
            updateEntity.setId(driverBillFreezeDO.getId());
            updateEntity.setFreezeType(freezeTypeEnum.getType());
            updateEntity.setFreezeTime(new Date());
            updateEntity.setUnfreezeFlag(UnFreezeFlagEnum.UNFREEZE.getCode());
            updateEntity.setUpdateTime(new Date());
            updateEntity.setUnfreezeTime(isCustomerServiceFreeze ? DateUtil.string2Date("2099-01-01 00:00:00") : newUnfreezeTime);
            updateEntity.setUpdateUser(StringUtils.isBlank(operator) ? "system" : operator);
            if (driverBillFreezeMapper.updateById(updateEntity) <= 0) {
                log.warn("更新账单冻结类型信息失败: updateEntity={}", FastJsonUtils.toJSONString(updateEntity));
            }
        }
    }

    private void updateDriverBill(DriverBillDO bill, Date newUnfreezeTime, FreezeDriverBillRequest request) {
        DriverBillDO updateEntity = new DriverBillDO();
        updateEntity.setId(bill.getId());
        updateEntity.setIsFreeze(IsFreezeEnum.CORRECT.getCode());
        updateEntity.setUpdateTime(new Date());
        updateEntity.setUpdateUser(StringUtils.defaultIfBlank(request.getOperator(), bill.getUpdateUser()));
        if ((request.getFreezeHour() != null && request.getFreezeHour() != 0) || (request.getFreezeSecond() != null && request.getFreezeSecond() != 0)) {
            updateEntity.setUnfreezeTime(newUnfreezeTime);
        }
        if (driverBillMapper.updateById(updateEntity) <= 0) {
            log.warn("更新账单流水失败: updateEntity={}", FastJsonUtils.toJSONString(updateEntity));
        }
    }

    private void updateDriverAccountAmount(DriverBillDO bill) {
        if (Objects.equals(bill.getIsFreeze(), IsFreezeEnum.CORRECT.getCode())) {
            log.info("当前账单已被冻结不更新账户金额: billNo={}", bill.getBillNo());
            return;
        }

        if (Objects.equals(bill.getVoucherType(), VoucherTypeEnum.DRIVER_ORDER_FREE_COMMISSION.getType())) {
            ChangeDriverAmountProcess.getInstance(ChangeDriverAmountTypeEnum.CARD_RE_FREEZE_BILL).changeAmount(bill.getDriverId(), bill.getAmount());
            return;
        }

        ChangeDriverAmountProcess.getInstance(ChangeDriverAmountTypeEnum.RE_FREEZE_BILL).changeAmount(bill.getDriverId(), bill.getAmount());
    }

    private void sendUnfreezeDelayMQ(DriverBillDO driverBillDO, FreezeDriverBillRequest request) {
        if (Objects.equals(FreezeTypeEnum.NORMAL_FREEZE.getType(), request.getFreezeType())
                || Objects.equals(FreezeTypeEnum.SETTLEMENT_INCORRECT_FREEZE.getType(), request.getFreezeType()) || Objects.equals(FreezeTypeEnum.RISK_FREEZE.getType(), request.getFreezeType())) {
            UnfreezeDriverBillRequest unfreezeDriverBillRequest = new UnfreezeDriverBillRequest();
            unfreezeDriverBillRequest.setTraceId(UUID.randomUUID().toString());
            unfreezeDriverBillRequest.setBillNos(Collections.singletonList(driverBillDO.getBillNo()));
            unfreezeDriverBillRequest.setUnFreezeTypes(Collections.singletonList(request.getFreezeType()));
            unfreezeDriverBillRequest.setOperator("系统定时");
            try {
                if (request.getFreezeHour() != null) {
                    turboMqProducer.sendDelayMsg(driverBillUnfreezeTopic, FastJsonUtils.toJSONString(unfreezeDriverBillRequest), request.getFreezeHour(), TimeUnit.HOURS);
                }
                if (request.getFreezeSecond() != null) {
                    turboMqProducer.sendDelayMsg(driverBillUnfreezeTopic, FastJsonUtils.toJSONString(unfreezeDriverBillRequest), request.getFreezeSecond(), TimeUnit.SECONDS);
                }
            } catch (Exception e) {
                log.warn("账单延迟解冻消息投递失败: unfreezeDriverBillRequest={}", FastJsonUtils.toJSONString(unfreezeDriverBillRequest));
            }
        }
    }

    @Override
    public CarOwnerResponseDTO unfreezeDriverBill(UnfreezeDriverBillRequest request) {
        try {
            // 1、验证请求参数
            validateRequest(request);

            // 2、验证解冻类型
            List<DriverBillDO> driverBillDOS = validateUnFreezeType(request);

            // 3、执行账单解冻操作
            unfreezeBill(request, driverBillDOS);

            // 4、验证订单
            validateOrder(driverBillDOS);

        } catch (BusinessException e) {
            return CarOwnerResponseDTO.fail(e.getMessage());
        } catch (Exception e) {
            log.error("司机账单解冻失败: request={}", FastJsonUtils.toJSONString(request), e);
            return CarOwnerResponseDTO.fail(ResultEnum.INTERNAL_SERVER_ERROR.getDesc());
        }
        return CarOwnerResponseDTO.succeed(null);
    }

    private List<DriverBillDO> validateUnFreezeType(UnfreezeDriverBillRequest request) {
        List<DriverBillDO> driverBillDOS = filterAndDeduplicateBills(fetchDriverBills(request));

        int count = 0;
        for (DriverBillDO driverBillDO : driverBillDOS) {
            List<DriverBillFreezeDO> driverBillFreezeDOS = findDriverBillFreezeByBillNo(driverBillDO.getBillNo());
            if (CollectionUtils.isEmpty(driverBillFreezeDOS)) {
                count++;
            }
        }

        if (count == driverBillDOS.size()) {
            log.warn("当前订单或账单无要解冻的类型");
            throw new BusinessException("当前订单或账单无要解冻的类型");
        }

        return driverBillDOS;
    }

    /**
     * 验证订单结算金额&风控
     */
    private void validateOrder(List<DriverBillDO> driverBillDOS) {
        List<String> orderNos = driverBillDOS.stream().map(DriverBillDO::getVoucherNo).distinct().collect(Collectors.toList());
        for (String orderNo : orderNos) {
            RideOrderInfoVO rideOrderInfoVO = rideOrderInfoService.queryOrderInfo(orderNo, "");
            if (rideOrderInfoVO == null) {
                log.warn("订单不存在: orderNo={}", orderNo);
                continue;
            }

            // 验证解冻风控
            orderRiskService.orderRisk(rideOrderInfoVO, RiskSceneEnum.ORDER_UNFREEZE);

            RideOrderSettlementVO orderSettlementVO = orderSettlementService.queryByOrderNo(orderNo);
            if (orderSettlementVO == null) {
                log.warn("订单无结算信息: orderNo={},driverId={}", orderNo, rideOrderInfoVO.getDriverId());
                continue;
            }

            // 验证结算金额
            validateSettlementAmount(orderSettlementVO, rideOrderInfoVO);
        }
    }

    /**
     * 验证结算金额是否符合要求
     *
     * @param orderSettlementVO 订单结算信息
     * @param rideOrderInfoVO   订单详情
     */
    @Override
    public boolean validateSettlementAmount(RideOrderSettlementVO orderSettlementVO, RideOrderInfoVO rideOrderInfoVO) {
        try {
            // 结算金额差值阈值
            BigDecimal differenceConfigAmount = getDifferenceConfigAmount();

            // 查询C端订单结算金额
            QueryOrderAmountResponse response = fetchCOrderSettlementInfo(rideOrderInfoVO);
            if (response == null || !StringUtils.equals(response.getCode(), ResultEnum.SUCCESS.getCode())) {
                log.warn("查询C端订单结算信息失败: driverId={}", rideOrderInfoVO.getDriverId());
                // 冻结订单
                settlementIncorrectFreeze(rideOrderInfoVO);
                return true;
            }

            // 获取C端订单结算金额
            BigDecimal cSettlementAmount = StringUtils.isNotBlank(response.getAmount()) ? new BigDecimal(response.getAmount()) : BigDecimal.ZERO;

            // 计算车主端订单结算金额（司机结算价 + 供应商结算价）
            BigDecimal bSettlementAmount = orderSettlementVO.getDriverSettlementPrice().add(orderSettlementVO.getSupplierSettlementPrice());

            // 计算结算金额差值
            BigDecimal differenceAmount = cSettlementAmount.subtract(bSettlementAmount).abs();

            log.info("结算金额验证: cSettlementAmount={},bSettlementAmount={},differenceAmount={}", cSettlementAmount, bSettlementAmount, differenceAmount);

            // 如果差值超过配置值;订单支付状态为后付;是否提现标识为空或不可提现 >>> 冻结订单
            if (differenceAmount.compareTo(differenceConfigAmount) > 0 || Objects.equals(response.getPayStatus(), 0) || response.getOwnerCanWithdrawal() == null || !response.getOwnerCanWithdrawal()) {
                settlementIncorrectFreeze(rideOrderInfoVO);
                return true;
            }
        } catch (Exception e) {
            log.error("订单验价处理异常: orderNo={}", rideOrderInfoVO.getOrderNo(), e);
        }
        return false;
    }

    /**
     * 获取结算金额差值阈值配置
     *
     * @return 配置的结算金额差值
     */
    private BigDecimal getDifferenceConfigAmount() {
        Double amount = sysConfigService.getDoubleCachedCfgValue(SysConfigEnum.ORDER_SETTLEMENT_DIFFERENCE_AMOUNT);
        log.info("结算验证金额差值阈值: amount={}", amount);
        return new BigDecimal(amount);
    }

    /**
     * 查询C端订单结算金额
     *
     * @param rideOrderInfoVO 订单详情
     * @return C端订单结算信息响应
     */
    private QueryOrderAmountResponse fetchCOrderSettlementInfo(RideOrderInfoVO rideOrderInfoVO) {
        QueryOrderAmountRequest queryRequest = QueryOrderAmountRequest.builder()
                .tcSerialNo(rideOrderInfoVO.getDistributorMainOrderNo())
                .supplierOrderNo(rideOrderInfoVO.getOrderNo())
                .timestamp(System.currentTimeMillis() / 1000)
                .traceId(UUID.randomUUID().toString())
                .checkWithdraw(true)
                .build();

        return platformOrderApi.findByOrderAmount(queryRequest);
    }

    private void settlementIncorrectFreeze(RideOrderInfoVO rideOrderInfoVO) {
        // 结算验证不通过通知
        wxWorkAlertComponent.alert(
                WxWorkTemplateEnum.SETTLEMENT_INCORRECT_FREEZE_NOTICE,
                Arrays.asList(CarownerConfigCenterUtils.noticeUserConfig.split(",")),
                rideOrderInfoVO.getOrderNo(),
                DateUtil.date2String(new Date())
        );

        // 结算验证不通过冻结
        FreezeDriverBillRequest request = new FreezeDriverBillRequest();
        request.setOrderNo(rideOrderInfoVO.getOrderNo());
        request.setDriverId(rideOrderInfoVO.getDriverId());
        request.setFreezeType(FreezeTypeEnum.SETTLEMENT_INCORRECT_FREEZE.getType());
        request.setFreezeHour(CarownerConfigCenterUtils.settlementFreezeHour);
        log.info("结算验证不通过开始调用订单冻结: request={}", FastJsonUtils.toJSONString(request));
        freezeDriverBill(request);
    }

    private void unfreezeBill(UnfreezeDriverBillRequest request, List<DriverBillDO> driverBillDOS) {
        // 获取解冻类型
        List<FreezeTypeEnum> unFreezeTypeEnums = request.getUnFreezeTypes().stream()
                .map(FreezeTypeEnum::convertEnum)
                .collect(Collectors.toList());

        // 执行账单解冻操作
        driverBillDOS.forEach(driverBillDO -> processUnfreeze(driverBillDO, request, unFreezeTypeEnums));

        // 更新完单风控
        updateOrderCompleteRisk(unFreezeTypeEnums, driverBillDOS);
    }

    private void updateOrderCompleteRisk(List<FreezeTypeEnum> unFreezeTypeEnums, List<DriverBillDO> driverBillDOList) {
        if (!unFreezeTypeEnums.contains(FreezeTypeEnum.RISK_FREEZE)) {
            log.info("解冻类型不包含风控类型");
            return;
        }

        List<String> orderNos = driverBillDOList.stream().map(DriverBillDO::getVoucherNo).distinct().collect(Collectors.toList());
        for (String orderNo : orderNos) {
            List<DriverBillDO> driverBillDOS = findDriverBillByOrderNo(orderNo);
            for (DriverBillDO driverBillDO : driverBillDOS) {
                // 完单风控冻结记录都被解冻
                List<DriverBillFreezeDO> driverBillFreezeDOS = findDriverBillFreezeByBillNo(driverBillDO.getBillNo());
                List<DriverBillFreezeDO> driverBillFreezeDOList = driverBillFreezeDOS.stream().filter(driverBillFreezeDO -> Objects.equals(driverBillFreezeDO.getFreezeType(), FreezeTypeEnum.RISK_FREEZE.getType())
                        && Objects.equals(driverBillFreezeDO.getUnfreezeFlag(), UnFreezeFlagEnum.UNFREEZE.getCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(driverBillFreezeDOList)) {
                    log.info("订单号{}存在风控未解冻类型，不更新订单风控信息", orderNo);
                    break;
                }
            }

            RideOrderRiskInfoDO rideOrderRiskInfoDO = rideOrderRiskInfoMapper.selectOne(new LambdaQueryWrapper<RideOrderRiskInfoDO>().eq(RideOrderRiskInfoDO::getOrderNo, orderNo), false);
            if (rideOrderRiskInfoDO == null) {
                log.info("订单号{}不存在风控信息，不更新订单风控信息", orderNo);
                continue;
            }

            RideOrderRiskInfoDO updateEntity = new RideOrderRiskInfoDO();
            updateEntity.setId(rideOrderRiskInfoDO.getId());
            updateEntity.setIsFreeze(IsFreezeEnum.DENY.getCode());
            updateEntity.setUpdateTime(new Date());
            updateEntity.setUnfreezeTime(null);
            if (rideOrderRiskInfoMapper.updateById(updateEntity) <= 0) {
                log.warn("解冻更新订单风控信息失败: updateEntity={}", FastJsonUtils.toJSONString(updateEntity));
            }
        }
    }

    private List<DriverBillDO> fetchDriverBills(UnfreezeDriverBillRequest request) {
        List<DriverBillDO> driverBillDOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(request.getBillNos())) {
            driverBillDOS.addAll(findDriverBillByBillNos(request.getBillNos()));
        }

        if (StringUtils.isNotBlank(request.getOrderNo())) {
            driverBillDOS.addAll(findDriverBillByOrderNo(request.getOrderNo()));
        }

        if (CollectionUtils.isEmpty(driverBillDOS)) {
            log.warn("账单或订单解冻未查询到账单信息: request={}", FastJsonUtils.toJSONString(request));
            throw new BusinessException("账单或订单解冻未查询到账单信息");
        }

        return driverBillDOS;
    }

    private List<DriverBillDO> filterAndDeduplicateBills(List<DriverBillDO> driverBillDOS) {
        return driverBillDOS.stream()
                .filter(Objects::nonNull)
                .filter(bill -> StringUtils.isBlank(bill.getWithdrawNo()))
                .collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(DriverBillDO::getBillNo))),
                        ArrayList::new
                ));
    }

    private void processUnfreeze(DriverBillDO driverBillDO, UnfreezeDriverBillRequest request, List<FreezeTypeEnum> unFreezeTypeEnums) {
        String lockKey = CacheKeyEnum.DRIVER_BILL.format(driverBillDO.getBillNo());
        RLock lock = null;
        try {
            lock = redisLock.tryAcquire(lockKey, FastJsonUtils.toJSONString(request), 10, 30, TimeUnit.SECONDS);
            if (lock == null) {
                log.warn("账单解冻获取并发锁失败: lockKey={}", lockKey);
                throw new BusinessException(ResultEnum.SUBMIT_ERROR);
            }

            // 查询冻结记录
            List<DriverBillFreezeDO> driverBillFreezeDOS = findDriverBillFreezeByBillNo(driverBillDO.getBillNo());
            if (CollectionUtils.isEmpty(driverBillFreezeDOS)) {
                log.warn("处理解冻账单冻结信息为空: billNo={}", driverBillDO.getBillNo());
                return;
            }

            // 筛选当前账单所有冻结记录
            List<DriverBillFreezeDO> eligibleFreezes = driverBillFreezeDOS.stream()
                    .filter(freeze -> Objects.equals(freeze.getUnfreezeFlag(), UnFreezeFlagEnum.UNFREEZE.getCode()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(eligibleFreezes)) {
                log.warn("无符合解冻条件的冻结记录: billNo={}", driverBillDO.getBillNo());
                return;
            }

            unFreezeTypeEnums.stream().forEach(unFreezeTypeEnum -> {
                // 筛选出当前解冻类型对应的冻结记录
                List<DriverBillFreezeDO> filteredFreezes = eligibleFreezes.stream()
                        .filter(driverBillFreezeDO -> Objects.equals(unFreezeTypeEnum.getType(), driverBillFreezeDO.getFreezeType()))
                        .collect(Collectors.toList());

                for (DriverBillFreezeDO driverBillFreezeDO : filteredFreezes) {
                    // 更新冻结记录为解冻状态
                    updateDriverBillFreeze(driverBillFreezeDO, request);
                }
            });

            // 如果所有冻结记录都被解冻，则更新账单和账户信息
            boolean isUnfreezeFlag = findDriverBillFreezeByBillNo(driverBillDO.getBillNo()).stream()
                    .allMatch(freezeRecord -> Objects.equals(freezeRecord.getUnfreezeFlag(), UnFreezeFlagEnum.THAW.getCode()));
            if (isUnfreezeFlag) {
                /*String cacheKey = CacheKeyEnum.DRIVER_BILL_THROTTLE.format(driverBillDO.getBillNo());
                String cacheValue = CacheUtils.getCacheValue(cacheKey);
                if (StringUtils.isNotBlank(cacheValue)) {
                    log.info("账单已被解冻，金额已释放，不做处理");
                    return;
                }*/
                updateBillAndAccount(driverBillDO, request);
                // CacheUtils.setCacheValue(cacheKey, "1", 3);
            }

        } finally {
            if (lock != null) {
                redisLock.release(lock, lockKey, "");
            }
        }
    }

    private void updateDriverBillFreeze(DriverBillFreezeDO driverBillFreezeDO, UnfreezeDriverBillRequest request) {
        DriverBillFreezeDO updateEntity = new DriverBillFreezeDO();
        updateEntity.setId(driverBillFreezeDO.getId());
        updateEntity.setUpdateTime(new Date());
        updateEntity.setUpdateUser(StringUtils.isNotBlank(request.getOperator()) ? request.getOperator() : driverBillFreezeDO.getUpdateUser());
        updateEntity.setUnfreezeFlag(UnFreezeFlagEnum.THAW.getCode());
        boolean isCustomerServiceFreeze = Objects.equals(driverBillFreezeDO.getFreezeType(), FreezeTypeEnum.CUSTOMER_SERVICE_FREEZE.getType());
        updateEntity.setUnfreezeTime(isCustomerServiceFreeze ? null : new Date());
        if (driverBillFreezeMapper.updateById(updateEntity) <= 0) {
            log.warn("账单冻结类型信息解冻更新失败: updateEntity={}", FastJsonUtils.toJSONString(updateEntity));
        }
    }

    private void updateBillAndAccount(DriverBillDO driverBillDO, UnfreezeDriverBillRequest request) {
        // 更新账单信息
        DriverBillDO updateEntity = new DriverBillDO();
        updateEntity.setId(driverBillDO.getId());
        updateEntity.setIsFreeze(IsFreezeEnum.DENY.getCode());
        updateEntity.setUpdateTime(new Date());
        updateEntity.setUpdateUser(StringUtils.isNotBlank(request.getOperator()) ? request.getOperator() : driverBillDO.getUpdateUser());
        updateEntity.setUnfreezeTime(new Date());
        if (driverBillMapper.updateById(updateEntity) <= 0) {
            log.warn("解冻账单更新账单信息失败: updateEntity={}", FastJsonUtils.toJSONString(updateEntity));
        } else {
            log.info("解冻账单更新账单信息成功: updateEntity={}", FastJsonUtils.toJSONString(updateEntity));
        }

        // 更新账户余额
        if (Objects.equals(driverBillDO.getVoucherType(), VoucherTypeEnum.DRIVER_ORDER_FREE_COMMISSION.getType())) {
            log.info("免佣卡类型解冻开始: billNo={},amount={}", driverBillDO.getBillNo(), driverBillDO.getAmount());
            ChangeDriverAmountProcess.getInstance(ChangeDriverAmountTypeEnum.CARD_UN_FREEZE_BILL).changeAmount(driverBillDO.getDriverId(), driverBillDO.getAmount());
            return;
        }

        log.info("正常订单类型解冻开始: billNo={},amount={}", driverBillDO.getBillNo(), driverBillDO.getAmount());
        ChangeDriverAmountProcess.getInstance(ChangeDriverAmountTypeEnum.UN_FREEZE_BILL).changeAmount(driverBillDO.getDriverId(), driverBillDO.getAmount());
    }

    @Override
    public String createDriverBill(DriverBillVO driverBillVO) {
        DriverBillDO driverBillDO = new DriverBillDO();
        driverBillDO.setBillNo(IdGeneratorUtil.create("L", "S"));
        driverBillDO.setDriverId(driverBillVO.getDriverId());
        driverBillDO.setVoucherNo(driverBillVO.getVoucherNo());
        driverBillDO.setVoucherType(driverBillVO.getVoucherType().getType());
        driverBillDO.setBillType(driverBillVO.getBillType().getType());
        driverBillDO.setAmount(driverBillVO.getAmount());
        driverBillDO.setRemark(driverBillVO.getRemark());
        driverBillDO.setCreateTime(new Date());
        driverBillDO.setIsFreeze(driverBillVO.getIsFreeze().getCode());
        driverBillDO.setUnfreezeTime(driverBillVO.getUnfreezeTime());
        driverBillDO.setFreezeType(0);
        driverBillDO.setEnv(EnvUtil.getEnv().getValue());
        driverBillVO.setBillNo(driverBillDO.getBillNo());
        try {
            driverBillMapper.insert(driverBillDO);
            log.info("创建流水成功: driverBillDO={}", JSON.toJSONString(driverBillDO));
            return driverBillDO.getBillNo();
        } catch (Exception ex) {
            log.info("创建流水失败: driverBillDO={}", JSON.toJSONString(driverBillDO));
        }
        return null;
    }

    private List<DriverBillFreezeDO> findDriverBillFreezeByBillNo(String billNo) {
        return driverBillFreezeMapper.selectList(new LambdaQueryWrapper<DriverBillFreezeDO>()
                .eq(DriverBillFreezeDO::getBillNo, billNo)
                .eq(DriverBillFreezeDO::getEnv, EnvUtil.getEnv().getValue()));
    }

    private DriverBillFreezeDO findDriverBillFreezeByBillNoAndFreezeType(String billNo, Integer freezeType) {
        return driverBillFreezeMapper.selectOne(new LambdaQueryWrapper<DriverBillFreezeDO>()
                .eq(DriverBillFreezeDO::getBillNo, billNo)
                .eq(DriverBillFreezeDO::getFreezeType, freezeType)
                .eq(DriverBillFreezeDO::getEnv, EnvUtil.getEnv().getValue()), false);
    }

    @Override
    public List<DriverBillDO> findDriverBillByBillNo(String orderNo) {
        return driverBillMapper.selectList(new LambdaQueryWrapper<DriverBillDO>()
                .eq(DriverBillDO::getBillNo, orderNo)
                .eq(DriverBillDO::getEnv, EnvUtil.getEnv().getValue()));
    }

    @Override
    public List<DriverBillDO> findDriverBillByOrderNo(String orderNo) {
        return driverBillMapper.selectList(new LambdaQueryWrapper<DriverBillDO>()
                .eq(DriverBillDO::getVoucherNo, orderNo)
                .in(DriverBillDO::getVoucherType, Arrays.asList(VoucherTypeEnum.DRIVER_ORDER_SETTLEMENT.getType(), VoucherTypeEnum.DRIVER_ORDER_FREE_COMMISSION.getType()))
                .eq(DriverBillDO::getEnv, EnvUtil.getEnv().getValue()));
    }

    @Override
    public List<DriverBillDO> findDriverBillByBillNos(List<String> billNos) {
        return driverBillMapper.selectList(new LambdaQueryWrapper<DriverBillDO>()
                .in(DriverBillDO::getBillNo, billNos)
                .eq(DriverBillDO::getEnv, EnvUtil.getEnv().getValue()));
    }

    @Override
    public List<DriverBillDO> findDriverBillByDriverId(Long driverId) {
        return driverBillMapper.selectList(new LambdaQueryWrapper<DriverBillDO>()
                .eq(DriverBillDO::getDriverId, driverId)
                .eq(DriverBillDO::getEnv, EnvUtil.getEnv().getValue())
                .eq(DriverBillDO::getIsFreeze, IsFreezeEnum.DENY.getCode())
                .eq(DriverBillDO::getWithdrawNo, ""));
    }

    @Override
    public List<DriverBillDO> findDriverBillByWithdrawNo(String withdrawNo) {
        return driverBillMapper.selectList(new LambdaQueryWrapper<DriverBillDO>()
                .eq(DriverBillDO::getWithdrawNo, withdrawNo)
                .eq(DriverBillDO::getEnv, EnvUtil.getEnv().getValue())
                .eq(DriverBillDO::getIsFreeze, IsFreezeEnum.DENY.getCode()));
    }
}
