package com.ly.travel.car.carowner.biz.driver.coupon.enums;

import java.util.HashMap;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CouponPayloadEventType {

                               CANCEL_ORDER("cancelOrder", "取消"),

                               FINISH_ORDER("finishOrder", "完单"),

    ;

    /**
     * code
     */
    private final String                                code;

    /**
     * 描述
     */
    private final String                                desc;

    private static final Map<String, CouponPayloadEventType> ENUMS = new HashMap<>();

    static {
        for (CouponPayloadEventType value : CouponPayloadEventType.values()) {
            ENUMS.put(value.code, value);
        }
    }

    /**
     * getByCode
     */
    public static CouponPayloadEventType getByCode(String code) {
        return ENUMS.get(code);
    }
}