package com.ly.travel.car.carowner.biz.driver.coupon.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum CouponStatusEnum {

                              /**
                               * 0-待使用
                               */
                              INIT(0, "待使用"),

                              /**
                               * 1-已占用
                               */
                              FREEZE(1, "已占用"),

                              /**
                               * 2-已核销
                               */
                              USED(2, "已核销"),

                              /**
                               * 3-已过期
                               */
                              EXPIRED(3, "已过期"),;

    private final int    code;

    private final String desc;

    public static CouponStatusEnum getByCode(Integer value) {
        if (null == value) {
            return null;
        }
        CouponStatusEnum[] elements = values();
        for (CouponStatusEnum element : elements) {
            if (element.getCode() == value) {
                return element;
            }
        }
        return null;
    }
}
