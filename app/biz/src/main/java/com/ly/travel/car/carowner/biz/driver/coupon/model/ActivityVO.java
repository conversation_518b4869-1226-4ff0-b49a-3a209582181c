package com.ly.travel.car.carowner.biz.driver.coupon.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 司机免佣卡活动配置类
 * <p>{@link com.ly.travel.car.carowner.common.enums.SysConfigEnum#DRIVER_ACTIVITY_COUPON_CONFIG}
 */
@Data
public class ActivityVO implements Serializable {

    /**
     * 活动详情
     */
    private List<Task> task;
    /**
     * 开关
     */
    private boolean    switchFlag;
    /**
     * 头部图片
     */
    private String     headImg;
    /**
     * 头部图片开始时间
     */
    private Date       headImgStartTime;
    /**
     * 头部图片结束时间
     */
    private Date       headImgEndTime;
    /**
     * 悬浮ico图片
     */
    private String     iconImg;
    /**
     * 悬浮ico开始时间
     */
    private Date       iconImgStartTime;
    /**
     * 悬浮ico结束时间
     */
    private Date       iconImgEndTime;
    /**
     * 详情地址
     */
    private String     jumpUrl;
    /**
     * 弹屏图片
     */
    private String     screenImg;
    /**
     * 弹屏图片开始时间
     */
    private Date       screenImgStartTime;
    /**
     * 弹屏图片结束时间
     */
    private Date       screenImgEndTime;

    @Data
    public static class Task implements Serializable {

        /**
         * 活动id
         */
        private String activityId;
        /**
         * 活动名称
         */
        private String activityName;
        /**
         * 任务批次号
         */
        private String batchNo;
        /**
         * 任务类型 1-车主认证 2-车主完单
         */
        private int    taskType;
        /**
         * 奖励条件 车主认证场景时：认证通过、车主完单场景时：完单数量
         */
        private String awardCondition;
        /**
         * 奖励类型 1-免佣卡
         */
        private int    awardType;

        /**
         * 门槛
         */
        private int    sill;
        /**
         * 奖励数量
         */
        private int    awardCnt;
        /**
         * 有效小时数
         */
        private int    effectiveHours;
        /**
         * 状态 1-有效
         */
        private int    status;
        /**
         * 开始时间
         */
        private Date   startTime;
        /**
         * 结束时间
         */
        private Date   endTime;

        /**
         * 详情地址
         */
        private String jumpUrl;
    }
}
