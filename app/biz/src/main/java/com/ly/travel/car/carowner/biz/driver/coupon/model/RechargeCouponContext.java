package com.ly.travel.car.carowner.biz.driver.coupon.model;

import com.ly.travel.car.carowner.biz.turbomq.model.CouponRechargePayload;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version RechargeCouponContext, 2025/9/9 23:51
 */
@Getter
public class RechargeCouponContext {

    /**
     * 司机Id
     */
    private Long                  driverId;

    /**
     * 发券参数
     */
    private CouponRechargePayload request;

    /**
     * 券任务(基本信息、规则信息)
     */
    private ActivityVO.Task       taskInfo;

    public RechargeCouponContext(CouponRechargePayload request, ActivityVO.Task taskInfo) {
        this.driverId = request.getDriverId();
        this.request = request;
        this.taskInfo = taskInfo;
    }

    public static RechargeCouponContext of(CouponRechargePayload payload, ActivityVO.Task taskInfo) {
        return new RechargeCouponContext(payload, taskInfo);
    }

}
