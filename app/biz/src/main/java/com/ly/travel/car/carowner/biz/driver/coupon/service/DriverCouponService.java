package com.ly.travel.car.carowner.biz.driver.coupon.service;

import com.ly.travel.car.carowner.biz.turbomq.model.CouponRechargePayload;
import com.ly.travel.car.carowner.biz.turbomq.model.DriverCouponPayload;
import com.ly.travel.car.carowner.biz.turbomq.model.JobCouponExpiredPayload;
import com.ly.travel.car.carowner.common.exception.BusinessException;
import com.ly.travel.car.carowner.facade.response.CarOwnerResponseDTO;

public interface DriverCouponService {

    CarOwnerResponseDTO<Boolean> freeze(String orderNo, Long driverId);

    CarOwnerResponseDTO<Boolean> used(DriverCouponPayload payload);

    CarOwnerResponseDTO<Boolean> unfreeze(DriverCouponPayload payload);

    /**
     * 发券
     * @param request
     * @return
     * @throws BusinessException
     */
    CarOwnerResponseDTO recharge(CouponRechargePayload request) throws BusinessException;

    /**
     * 完单发券
     * @param orderNo
     */
    void sendFinishOrderRecharge(String orderNo);

    /**
     * 作废
     * @param payload
     * @return
     */
    CarOwnerResponseDTO<Boolean> expired(JobCouponExpiredPayload payload);
}