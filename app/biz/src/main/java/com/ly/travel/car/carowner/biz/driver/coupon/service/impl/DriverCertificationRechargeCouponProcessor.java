package com.ly.travel.car.carowner.biz.driver.coupon.service.impl;

import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.biz.driver.DriverUtils;
import com.ly.travel.car.carowner.biz.driver.coupon.enums.CouponStatusEnum;
import com.ly.travel.car.carowner.biz.driver.coupon.model.CouponInfoExtVO;
import com.ly.travel.car.carowner.biz.driver.coupon.model.RechargeCouponContext;
import com.ly.travel.car.carowner.biz.driver.info.DriverInfoService;
import com.ly.travel.car.carowner.biz.driver.info.model.DriverVO;
import com.ly.travel.car.carowner.biz.driver.info.model.VehicleInfoVO;
import com.ly.travel.car.carowner.common.constant.Constants;
import com.ly.travel.car.carowner.common.enums.CouponTaskEnum;
import com.ly.travel.car.carowner.common.enums.CouponTypeEnum;
import com.ly.travel.car.carowner.common.enums.VerifyStatusEnum;
import com.ly.travel.car.carowner.common.utils.DateUtil;
import com.ly.travel.car.carowner.common.utils.EnvUtil;
import com.ly.travel.car.carowner.ext.dal.daointerface.DriverCouponDAO;
import com.ly.travel.car.carowner.ext.dal.dataobject.DriverCouponDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version driverCertificationRechargeCouponProcessor, 2025/9/9 23:55
 */
@Slf4j
@Service("driverCertificationRechargeCouponProcessor")
public class DriverCertificationRechargeCouponProcessor implements RechargeCouponProcessor {

    @Resource
    private DriverInfoService driverInfoService;
    @Resource
    private DriverCouponDAO   driverCouponDAO;

    @Override
    public void recharge(RechargeCouponContext context) {
        // 验证发券规则
        boolean validate = this.validate(context);
        if (!validate) {
            log.warn("[DriverCertificationRechargeCouponProcessor][recharge] 司机认证发券校验:未通过");
            return;
        }
        //发券
        for (int i = 0; i < context.getTaskInfo().getAwardCnt(); i++) {
            DriverCouponDO coupon = buildDriverCouponDO(context);
            boolean rechargeSuccess = driverCouponDAO.saveDriverCoupon(buildDriverCouponDO(context));
            log.info("[DriverCertificationRechargeCouponProcessor][recharge] 司机认证发券结果:{},{},{}", context.getDriverId(), coupon.getCouponNo(), rechargeSuccess);
        }
    }

    @Override
    public boolean validate(RechargeCouponContext context) {
        // 查询司机信息
        DriverVO driver = driverInfoService.queryDriverInfo(context.getDriverId());
        // 查询司机车辆信息
        VehicleInfoVO vehicleInfo = driverInfoService.queryVehicleInfo(context.getDriverId());

        Integer vehicleStatus = VerifyStatusEnum.NO_VERIFY.getCode();
        if (Objects.nonNull(vehicleInfo)) {
            vehicleStatus = vehicleInfo.getStatus();
        }
        VerifyStatusEnum verifyStatus = DriverUtils.getFinalStatus(driver.getStatus(), vehicleStatus);
        log.info("[DriverCertificationRechargeCouponProcessor][validate] 司机状态={}，车辆状态={}，审核状态={}", driver.getStatus(), vehicleStatus, verifyStatus.getCode() + verifyStatus.getMsg());
        if (!verifyStatus.equals(VerifyStatusEnum.VERIFY_ADOPT)) {
            return false;
        }

        // 仅发送一次
        long driverCouponCount = driverCouponDAO.queryDriverCouponCount(context.getDriverId(), context.getTaskInfo().getBatchNo(), CouponTypeEnum.COMMISSION_FREE.getCode(),
            CouponTaskEnum.DRIVER_CERTIFICATION.getDesc());
        log.info("[DriverCertificationRechargeCouponProcessor][validate] driverId={},batchNo={},couponType={},scene={}", driver.getId(), context.getTaskInfo().getBatchNo(),
            CouponTypeEnum.COMMISSION_FREE.getCode(), CouponTaskEnum.DRIVER_CERTIFICATION.getDesc());
        if (driverCouponCount > 0) {
            log.warn("[DriverCertificationRechargeCouponProcessor][validate] 车主认证发券仅能发送一次,{}", driver.getId());
            return false;
        }
        return true;
    }

    private DriverCouponDO buildDriverCouponDO(RechargeCouponContext context) {
        DriverCouponDO entity = new DriverCouponDO();
        entity.setDriverId(context.getDriverId());
        entity.setBatchNo(context.getTaskInfo().getBatchNo());
        entity.setCouponNo(UUID.randomUUID().toString().replace("-", ""));
        entity.setName(CouponTypeEnum.of(context.getTaskInfo().getAwardType()).getDesc());
        entity.setCouponType(context.getTaskInfo().getAwardType());
        entity.setSill(BigDecimal.ZERO);
        entity.setGmtCouponBegin(context.getRequest().getGmtReceived());
        entity.setGmtCouponEnd(DateUtil.addHour(context.getRequest().getGmtReceived(), context.getTaskInfo().getEffectiveHours()));
        entity.setGmtReceived(context.getRequest().getGmtReceived());
        entity.setGmtRefund(Constants.DEFAULT_DATE);
        entity.setGmtUsed(Constants.DEFAULT_DATE);
        entity.setOrderNo(StringUtils.EMPTY);
        entity.setScene(CouponTaskEnum.of(context.getRequest().getSceneType()).getDesc());
        entity.setExt(FastJsonUtils.toJSONString(buildCouponInfoExtVO(context)));
        entity.setStatus(CouponStatusEnum.INIT.getCode());
        entity.setEnv(EnvUtil.getEnv().getValue());
        entity.setCreateUser(Constants.SYSTEM);
        entity.setCreateTime(new Date());
        entity.setUpdateUser(Constants.SYSTEM);
        entity.setUpdateTime(new Date());
        return entity;
    }

    private CouponInfoExtVO buildCouponInfoExtVO(RechargeCouponContext context) {
        CouponInfoExtVO extVO = new CouponInfoExtVO();
        extVO.setActivityName(context.getTaskInfo().getActivityName());
        extVO.setJumpUrl(context.getTaskInfo().getJumpUrl());
        extVO.setBizOrderSerialNo(StringUtils.EMPTY);
        return extVO;
    }
}
