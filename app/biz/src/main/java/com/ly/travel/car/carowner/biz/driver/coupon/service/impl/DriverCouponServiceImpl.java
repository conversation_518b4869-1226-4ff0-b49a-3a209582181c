package com.ly.travel.car.carowner.biz.driver.coupon.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ly.mom.netty.util.internal.ThrowableUtil;
import com.ly.sof.utils.log.LogContextUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.biz.driver.coupon.enums.CouponStatusEnum;
import com.ly.travel.car.carowner.biz.driver.coupon.model.ActivityVO;
import com.ly.travel.car.carowner.biz.driver.coupon.model.CouponRechargeResponseDTO;
import com.ly.travel.car.carowner.biz.driver.coupon.model.RechargeCouponContext;
import com.ly.travel.car.carowner.biz.driver.coupon.service.DriverCouponService;
import com.ly.travel.car.carowner.biz.order.model.RideOrderInfoVO;
import com.ly.travel.car.carowner.biz.order.model.RideOrderSettlementVO;
import com.ly.travel.car.carowner.biz.order.service.RideOrderInfoService;
import com.ly.travel.car.carowner.biz.order.service.RideOrderLogService;
import com.ly.travel.car.carowner.biz.settlement.service.RideOrderSettlementService;
import com.ly.travel.car.carowner.biz.sys.service.SysConfigService;
import com.ly.travel.car.carowner.biz.turbomq.model.CouponRechargePayload;
import com.ly.travel.car.carowner.biz.turbomq.model.DriverCouponPayload;
import com.ly.travel.car.carowner.biz.turbomq.model.JobCouponExpiredPayload;
import com.ly.travel.car.carowner.biz.turbomq.producer.RechargeCouponProducer;
import com.ly.travel.car.carowner.common.enums.*;
import com.ly.travel.car.carowner.common.cache.CacheUtils;
import com.ly.travel.car.carowner.common.exception.BusinessException;
import com.ly.travel.car.carowner.common.lock.DistributedRedisLock;
import com.ly.travel.car.carowner.common.utils.CarownerConfigCenterUtils;
import com.ly.travel.car.carowner.common.utils.EnvUtil;
import com.ly.travel.car.carowner.ext.dal.daointerface.DriverCouponDAO;
import com.ly.travel.car.carowner.ext.dal.dataobject.DriverCouponDO;
import com.ly.travel.car.carowner.ext.dal.mapper.DriverCouponMapper;
import com.ly.travel.car.carowner.facade.response.CarOwnerResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import com.ly.travel.car.carowner.common.constant.Constants;

@Slf4j
@Service
public class DriverCouponServiceImpl implements DriverCouponService {

    @Resource
    protected DriverCouponDAO          driverCouponDAO;

    @Resource
    private SysConfigService           sysConfigService;

    @Resource(name = "orderFinishRechargeCouponProcessor")
    private RechargeCouponProcessor    orderFinishRechargeCouponProcessor;

    @Resource(name = "driverCertificationRechargeCouponProcessor")
    private RechargeCouponProcessor    driverCertificationRechargeCouponProcessor;

    @Resource
    private RechargeCouponProducer     rechargeCouponProducer;

    @Resource
    private RideOrderLogService        rideOrderLogService;

    @Resource
    private DistributedRedisLock       redisLock;
    @Resource
    private DriverCouponMapper         driverCouponMapper;
    @Resource
    private RideOrderInfoService       rideOrderInfoService;
    @Resource
    private RideOrderSettlementService rideOrderSettlementService;

    @Override
    public CarOwnerResponseDTO<Boolean> freeze(String orderNo, Long driverId) {
        RLock lock = null;
        String lockKey = "";
        try {
            LogContextUtils.setFilter2(orderNo);
            String freezeCloseSwitch = CarownerConfigCenterUtils.driverCouponFreezeCloseSwitch;
            log.info("司机卡券冻结关闭的开关：{}", freezeCloseSwitch);
            if ("true".equalsIgnoreCase(freezeCloseSwitch)) {
                return CarOwnerResponseDTO.succeed(false);
            }
            lockKey = CacheKeyEnum.DRIVER_COUPON_FREEZE.format(driverId);
            lock = redisLock.tryAcquire(lockKey, orderNo);
            if (lock == null) {
                log.warn("司机卡券冻结获取并发锁失败: lockKey={}", lockKey);
                throw new BusinessException("司机卡券冻结，获取并发锁失败");
            }
            // 接单冻结
            if (driverCouponDAO.queryByOrderNo(driverId, orderNo, EnvUtil.getEnv().getValue()) != null) {
                log.warn("订单已冻结: orderNo={},driverId={}", orderNo, driverId);
                throw new BusinessException("不可重复冻结卡券");
            }
            Date now = new Date();
            DriverCouponDO item = Optional
                .ofNullable(driverCouponDAO.queryByDriverId(driverId, EnvUtil.getEnv().getValue(), CouponStatusEnum.INIT.getCode()))
                .filter(CollectionUtils::isNotEmpty).orElse(Collections.emptyList()).stream()
                .filter(coupon -> !now.before(coupon.getGmtCouponBegin()))
                .filter(coupon -> !now.after(coupon.getGmtCouponEnd()))
                .min(Comparator.comparing(DriverCouponDO::getGmtCouponEnd)).orElse(null);
            if (item == null) {
                log.warn("未查询到可冻结卡券: driverId={}", driverId);
                throw new BusinessException("未查询到可冻结卡券");
            }
            Date beginTime = item.getGmtCouponBegin();
            Date endTime = item.getGmtCouponEnd();
            if (now.after(beginTime) && now.before(endTime)) {
                log.info("当前时间在有效期内，冻结卡券，司机：{}，订单：{}", driverId, orderNo);
            } else {
                throw new BusinessException("卡券状态异常，当前时间不在有效期内");
            }
            if (CouponStatusEnum.INIT.getCode() != item.getStatus()) {
                throw new BusinessException("卡券状态异常，占用失败！");
            }
            rideOrderLogService.addLog(orderNo, OrderLogTypeEnum.FREEZE_COUPON, OrderLogTypeEnum.FREEZE_COUPON.getRemark(), "系统");
            boolean updated = driverCouponDAO.updateFreezeState(item.getId(), orderNo, CouponStatusEnum.FREEZE.getCode());
            return CarOwnerResponseDTO.succeed(updated);
        } catch (Exception ignore) {
            // ignore
            log.warn("冻结卡券异常:{}", ThrowableUtil.stackTraceToString(ignore), ignore);
            return CarOwnerResponseDTO.succeed(false);
        } finally {
            if (lock != null) {
                redisLock.release(lock, lockKey, "");
            }
        }
    }

    @Override
    public CarOwnerResponseDTO<Boolean> used(DriverCouponPayload payload) {
        try {
            LogContextUtils.setFilter2(payload.getOrderNo());
            // 完单核销
            DriverCouponDO item = driverCouponDAO.queryByOrderNo(payload.getDriverId(), payload.getOrderNo(), EnvUtil.getEnv().getValue());
            if (item == null) {
                throw new BusinessException("未查询到已冻结卡券");
            }
            if (CouponStatusEnum.FREEZE.getCode() != item.getStatus()) {
                throw new BusinessException("卡券状态异常，核销失败！");
            }
            if (new Date().after(item.getGmtCouponEnd())) {
                log.warn("卡券已过期，核销卡券，司机：{}，订单：{}", payload.getDriverId(), payload.getOrderNo());
            }
            RideOrderSettlementVO settlementVO = rideOrderSettlementService.queryByOrderNo(payload.getOrderNo());
            if (settlementVO == null) {
                throw new BusinessException("未查询到结算信息");
            }
            rideOrderLogService.addLog(payload.getOrderNo(), OrderLogTypeEnum.USED_COUPON,
                OrderLogTypeEnum.USED_COUPON.formatRemark(settlementVO.getDriverExtraSettlementPrice()), "系统");
            boolean updated = driverCouponDAO.updateUsedState(item.getId(), CouponStatusEnum.USED.getCode());
            return CarOwnerResponseDTO.succeed(updated);
        } catch (Exception ignore) {
            //  ignore
            log.warn("核销卡券异常:{}", ThrowableUtil.stackTraceToString(ignore), ignore);
            return CarOwnerResponseDTO.succeed(false);
        }
    }

    @Override
    public CarOwnerResponseDTO<Boolean> unfreeze(DriverCouponPayload payload) {
        try {
            LogContextUtils.setFilter2(payload.getOrderNo());
            // 取消解冻
            DriverCouponDO item = driverCouponDAO.queryByOrderNo(payload.getDriverId(), payload.getOrderNo(), EnvUtil.getEnv().getValue());
            if (item == null) {
                throw new BusinessException("未查询到可退回卡券");
            }
            if (CouponStatusEnum.FREEZE.getCode() != item.getStatus()) {
                throw new BusinessException("卡券状态异常，退回失败！");
            }
            if (new Date().after(item.getGmtCouponEnd())) {
                log.warn("卡券已过期，退回卡券，司机：{}，订单：{}", payload.getDriverId(), payload.getOrderNo());
            }
            rideOrderLogService.addLog(payload.getOrderNo(), OrderLogTypeEnum.UNFREEZE_COUPON, OrderLogTypeEnum.UNFREEZE_COUPON.getRemark(),
                "系统");
            boolean updated = driverCouponDAO.updateUnfreezeState(item.getId(), CouponStatusEnum.INIT.getCode());
            return CarOwnerResponseDTO.succeed(updated);
        } catch (Exception ignore) {
            // ignore
            log.warn("取消退回卡券异常:{}", ThrowableUtil.stackTraceToString(ignore), ignore);
            return CarOwnerResponseDTO.succeed(false);
        }
    }

    @Override
    public CarOwnerResponseDTO recharge(CouponRechargePayload request) {
        try {
            if (CouponTypeEnum.of(request.getCouponType()).equals(CouponTypeEnum.COMMISSION_FREE)) {
                String cfgValue = sysConfigService.getStringCachedCfgValue(SysConfigEnum.DRIVER_ACTIVITY_COUPON_CONFIG);
                if (StringUtils.isBlank(cfgValue)) {
                    log.warn("[recharge] 未查询到活动配置");
                    return CarOwnerResponseDTO.fail("未查询到活动配置");
                }
                ActivityVO activityVO = FastJsonUtils.fromJSONString(cfgValue, ActivityVO.class);
                if (Objects.isNull(activityVO)) {
                    log.warn("[recharge] 未查询到活动配置，json序列化失败");
                    return CarOwnerResponseDTO.fail("未查询到活动配置，json序列化失败");
                }
                if (!activityVO.isSwitchFlag()) {
                    log.warn("[recharge] 活动未开启");
                    return CarOwnerResponseDTO.fail("活动未开启");
                }
                if (CollectionUtils.isEmpty(activityVO.getTask())) {
                    log.warn("[recharge] 活动任务未配置");
                    return CarOwnerResponseDTO.fail("活动任务未配置");
                }

                // 获取有效的活动任务
                List<ActivityVO.Task> taskList = activityVO.getTask().stream()
                    // 过滤
                    .filter(
                        // 任务类型
                        t -> t.getTaskType() == request.getSceneType()
                             // 有效性
                             && Constants.TRUE.equals(t.getStatus())
                             // 有效期
                             && request.getGmtReceived().after(t.getStartTime()) && t.getEndTime().after(request.getGmtReceived()))
                    .collect(Collectors.toList());
                log.info("[recharge] taskList={}", FastJsonUtils.toJSONString(taskList));

                for (ActivityVO.Task task : taskList) {
                    // 构建发券参数上下文
                    RechargeCouponContext rechargeCouponContext = RechargeCouponContext.of(request, task);
                    // 任务类型
                    CouponTaskEnum couponTaskType = CouponTaskEnum.of(task.getTaskType());
                    if (CouponTaskEnum.DRIVER_CERTIFICATION.equals(couponTaskType)) {
                        // 司机认证发券
                        driverCertificationRechargeCouponProcessor.recharge(rechargeCouponContext);
                    } else if (CouponTaskEnum.ORDER_FINISH.equals(couponTaskType)) {
                        // 订单完单发券
                        orderFinishRechargeCouponProcessor.recharge(rechargeCouponContext);
                    }
                }
            }
            CouponRechargeResponseDTO response = new CouponRechargeResponseDTO();
            return CarOwnerResponseDTO.succeed(response);
        } catch (Exception e) {
            log.error("[recharge] 发券系统异常：{}", e.getMessage(), e);
            return CarOwnerResponseDTO.fail("发券系统异常");
        }
    }

    @Override
    public void sendFinishOrderRecharge(String orderNo) {
        boolean holdLock = false;
        try {
            String lockKey = CacheKeyEnum.FINISH_ORDER_RECHARGE_COUPON.format(orderNo);
            log.info("[sendFinishOrderRecharge][] 锁KEY={}", lockKey);
            holdLock = CacheUtils.setnx(lockKey, orderNo, CacheKeyEnum.FINISH_ORDER_RECHARGE_COUPON.getExpire());
            if (!holdLock) {
                log.warn("[sendFinishOrderRecharge][] 获取锁失败,{}", lockKey);
                return;
            }
            RideOrderInfoVO orderInfoVO = rideOrderInfoService.queryOrderInfo(orderNo, null);
            if (Objects.isNull(orderInfoVO)) {
                log.warn("[sendFinishOrderRecharge][] 订单未查到,{}", orderNo);
            }
            rechargeCouponProducer.send(buildCouponRechargePayload(orderNo, orderInfoVO.getDriverId()));
        } catch (Exception e) {
            log.error("[sendFinishOrderRecharge][] 完单发券异常,{},{}", orderNo, e.getMessage(), e);
        } finally {
            if (holdLock) {
                CacheUtils.delKey(CacheKeyEnum.FINISH_ORDER_RECHARGE_COUPON.format(orderNo));
            }
        }
    }

    @Override
    public CarOwnerResponseDTO<Boolean> expired(JobCouponExpiredPayload payload) {
        LambdaUpdateWrapper<DriverCouponDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(DriverCouponDO::getId, payload.getCouponId());
        updateWrapper.eq(DriverCouponDO::getStatus, CouponStatusEnum.INIT.getCode());
        updateWrapper.lt(DriverCouponDO::getGmtCouponEnd, new Date());
        updateWrapper.set(DriverCouponDO::getStatus, CouponStatusEnum.EXPIRED.getCode());
        updateWrapper.set(DriverCouponDO::getUpdateUser, Constants.SYSTEM);
        updateWrapper.set(DriverCouponDO::getUpdateTime, new Date());
        int cc = driverCouponMapper.update(updateWrapper);
        if (cc > 0) {
            log.info("[expired] 作废券成功,id={}", payload.getCouponId());
            return CarOwnerResponseDTO.succeed(true);
        } else {
            log.info("[expired] 作废券失败,id={}", payload.getCouponId());
            return CarOwnerResponseDTO.succeed(false);
        }
    }

    private CouponRechargePayload buildCouponRechargePayload(String orderNo, Long driverId) {
        CouponRechargePayload payload = new CouponRechargePayload();
        payload.setDriverId(driverId);
        payload.setCouponType(CouponTypeEnum.COMMISSION_FREE.getCode());
        payload.setTraceId(UUID.randomUUID().toString().replace("-", ""));
        payload.setSceneType(CouponTaskEnum.ORDER_FINISH.getCode());
        payload.setGmtReceived(new Date());
        payload.setOrderSerialNo(orderNo);
        return payload;
    }
}
