package com.ly.travel.car.carowner.biz.driver.coupon.service.impl;

import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.biz.driver.coupon.enums.CouponStatusEnum;
import com.ly.travel.car.carowner.biz.driver.coupon.model.CouponInfoExtVO;
import com.ly.travel.car.carowner.biz.driver.coupon.model.RechargeCouponContext;
import com.ly.travel.car.carowner.biz.order.model.RideOrderInfoVO;
import com.ly.travel.car.carowner.biz.order.service.RideOrderInfoService;
import com.ly.travel.car.carowner.common.constant.Constants;
import com.ly.travel.car.carowner.common.enums.CouponTaskEnum;
import com.ly.travel.car.carowner.common.enums.CouponTypeEnum;
import com.ly.travel.car.carowner.common.enums.OrderStatusEnum;
import com.ly.travel.car.carowner.common.utils.DateUtil;
import com.ly.travel.car.carowner.common.utils.EnvUtil;
import com.ly.travel.car.carowner.ext.dal.daointerface.DriverCouponDAO;
import com.ly.travel.car.carowner.ext.dal.dataobject.DriverCouponDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version orderFinishRechargeCouponProcessor, 2025/9/9 23:53
 */
@Slf4j
@Service("orderFinishRechargeCouponProcessor")
public class OrderFinishRechargeCouponProcessor implements RechargeCouponProcessor {

    @Resource
    private DriverCouponDAO      driverCouponDAO;
    @Resource
    private RideOrderInfoService rideOrderInfoService;

    @Override
    public void recharge(RechargeCouponContext context) {
        // 验证发券规则
        boolean validate = this.validate(context);
        if (!validate) {
            log.warn("[orderFinishRechargeCouponProcessor][recharge] 司机认证发券校验:未通过");
            return;
        }
        //发券
        for (int i = 0; i < context.getTaskInfo().getAwardCnt(); i++) {
            DriverCouponDO coupon = buildDriverCouponDO(context);
            boolean rechargeSuccess = driverCouponDAO.saveDriverCoupon(buildDriverCouponDO(context));
            log.info("[orderFinishRechargeCouponProcessor][recharge] 司机认证发券结果:{},{},{}", context.getDriverId(), coupon.getCouponNo(), rechargeSuccess);
        }
    }

    @Override
    public boolean validate(RechargeCouponContext context) {
        String orderSerialNo = context.getRequest().getOrderSerialNo();
        RideOrderInfoVO orderInfoVO = rideOrderInfoService.queryOrderInfo(orderSerialNo, "");
        if (Objects.isNull(orderInfoVO)) {
            log.warn("[orderFinishRechargeCouponProcessor][validate] 车主完单发券校验未通过:订单未查询到,{}", orderSerialNo);
            return false;
        }
        if (!orderInfoVO.getStatus().equals(OrderStatusEnum.COMPLETED.getStatus())) {
            log.warn("[orderFinishRechargeCouponProcessor][validate] 车主完单发券校验未通过：订单未完结,{}", orderSerialNo);
            return false;
        }
        long driverCouponCount = driverCouponDAO.queryDriverCouponCount(context.getDriverId(), context.getTaskInfo().getBatchNo(), CouponTypeEnum.COMMISSION_FREE.getCode(),
            CouponTaskEnum.ORDER_FINISH.getDesc());
        if (driverCouponCount > 0) {
            log.warn("[orderFinishRechargeCouponProcessor][validate] 车主完单发券仅能发送一次,driverId={},orderSerialNo={}", context.getDriverId(), orderSerialNo);
            return false;
        }
        long finishOrderCount = rideOrderInfoService.queryFinishOrderCount(context.getDriverId(), context.getTaskInfo().getStartTime(), context.getTaskInfo().getEndTime());
        log.info("[orderFinishRechargeCouponProcessor][validate] 车主完单发券:driverId={},完单数量,{}", context.getDriverId(), finishOrderCount, context.getTaskInfo().getSill());
        if (finishOrderCount < context.getTaskInfo().getSill()) {
            log.warn("[orderFinishRechargeCouponProcessor][validate] 车主完单发券校验未通过:门槛未达标,driverId={}", context.getDriverId(), finishOrderCount, context.getTaskInfo().getSill());
            return false;
        }
        return true;
    }

    private DriverCouponDO buildDriverCouponDO(RechargeCouponContext context) {
        DriverCouponDO entity = new DriverCouponDO();
        entity.setDriverId(context.getDriverId());
        entity.setBatchNo(context.getTaskInfo().getBatchNo());
        entity.setCouponNo(UUID.randomUUID().toString().replace("-", ""));
        entity.setName(CouponTypeEnum.of(context.getTaskInfo().getAwardType()).getDesc());
        entity.setCouponType(context.getTaskInfo().getAwardType());
        entity.setSill(BigDecimal.ZERO);
        entity.setGmtCouponBegin(context.getRequest().getGmtReceived());
        entity.setGmtCouponEnd(DateUtil.addHour(context.getRequest().getGmtReceived(), context.getTaskInfo().getEffectiveHours()));
        entity.setGmtReceived(context.getRequest().getGmtReceived());
        entity.setGmtRefund(Constants.DEFAULT_DATE);
        entity.setGmtUsed(Constants.DEFAULT_DATE);
        entity.setOrderNo(StringUtils.EMPTY);
        entity.setScene(CouponTaskEnum.of(context.getRequest().getSceneType()).getDesc());
        entity.setExt(FastJsonUtils.toJSONString(buildCouponInfoExtVO(context)));
        entity.setStatus(CouponStatusEnum.INIT.getCode());
        entity.setEnv(EnvUtil.getEnv().getValue());
        entity.setCreateUser(Constants.SYSTEM);
        entity.setCreateTime(new Date());
        entity.setUpdateUser(Constants.SYSTEM);
        entity.setUpdateTime(new Date());
        return entity;
    }

    private CouponInfoExtVO buildCouponInfoExtVO(RechargeCouponContext context) {
        CouponInfoExtVO extVO = new CouponInfoExtVO();
        extVO.setActivityName(context.getTaskInfo().getActivityName());
        extVO.setJumpUrl(context.getTaskInfo().getJumpUrl());
        extVO.setBizOrderSerialNo(context.getRequest().getOrderSerialNo());
        return extVO;
    }
}
