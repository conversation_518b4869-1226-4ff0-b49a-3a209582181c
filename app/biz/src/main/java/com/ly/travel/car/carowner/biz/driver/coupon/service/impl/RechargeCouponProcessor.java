package com.ly.travel.car.carowner.biz.driver.coupon.service.impl;

import com.ly.travel.car.carowner.biz.driver.coupon.model.RechargeCouponContext;

/**
 * <AUTHOR>
 * @version RechargeCouponProcessor, 2025/9/9 23:49
 */
public interface RechargeCouponProcessor {

    /**
     * 发券.
     *
     * @param context the context
     * @return the append ret
     */
    void recharge(RechargeCouponContext context);

    /**
     * 发券校验
     * @param context
     * @return
     */
    boolean validate(RechargeCouponContext context);
}
