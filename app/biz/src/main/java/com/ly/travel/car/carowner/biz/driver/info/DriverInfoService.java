package com.ly.travel.car.carowner.biz.driver.info;

import com.ly.travel.car.carowner.biz.driver.info.model.DriverVO;
import com.ly.travel.car.carowner.biz.driver.info.model.VehicleInfoVO;

/**
 * <AUTHOR>
 * @version DriverInfoService, 2025/9/10 00:31
 */
public interface DriverInfoService {

    /**
     * 查询司机信息
     * @param driverId
     * @return
     */
    DriverVO queryDriverInfo(Long driverId);

    /**
     * 查询司机车辆信息
     * @param driverId
     * @return
     */
    VehicleInfoVO queryVehicleInfo(Long driverId);
}
