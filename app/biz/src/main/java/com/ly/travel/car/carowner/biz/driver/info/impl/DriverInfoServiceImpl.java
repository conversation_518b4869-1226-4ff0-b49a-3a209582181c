package com.ly.travel.car.carowner.biz.driver.info.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.travel.car.carowner.biz.driver.info.DriverInfoService;
import com.ly.travel.car.carowner.biz.driver.info.mapper.DriverInfoMapping;
import com.ly.travel.car.carowner.biz.driver.info.mapper.VehicleInfoMapping;
import com.ly.travel.car.carowner.biz.driver.info.model.DriverVO;
import com.ly.travel.car.carowner.biz.driver.info.model.VehicleInfoVO;
import com.ly.travel.car.carowner.common.exception.BusinessException;
import com.ly.travel.car.carowner.common.utils.EnvUtil;
import com.ly.travel.car.carowner.dal.dataobject.DriverInfoDO;
import com.ly.travel.car.carowner.dal.dataobject.VehicleInfoDO;
import com.ly.travel.car.carowner.dal.mapper.DriverInfoMapper;
import com.ly.travel.car.carowner.dal.mapper.VehicleInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version DriverInfoServiceImpl, 2025/9/10 00:44
 */
@Slf4j
@Service("driverInfoService")
public class DriverInfoServiceImpl implements DriverInfoService {

    @Resource
    private DriverInfoMapper   driverInfoMapper;
    @Resource
    private DriverInfoMapping  driverInfoMapping;

    @Resource
    private VehicleInfoMapper  vehicleInfoMapper;
    @Resource
    private VehicleInfoMapping vehicleInfoMapping;

    @Override
    public DriverVO queryDriverInfo(Long driverId) {
        DriverInfoDO driverInfo = driverInfoMapper.selectById(driverId);
        if (Objects.isNull(driverInfo)) {
            throw new BusinessException("司机信息不存在");
        }
        return driverInfoMapping.d2v(driverInfo);
    }

    @Override
    public VehicleInfoVO queryVehicleInfo(Long driverId) {
        VehicleInfoDO vehicleInfoDO = vehicleInfoMapper
            .selectOne(new LambdaQueryWrapper<VehicleInfoDO>().eq(VehicleInfoDO::getDriverId, driverId),false);
        if (Objects.nonNull(vehicleInfoDO)) {
            return vehicleInfoMapping.d2v(vehicleInfoDO);
        }
        return null;
    }
}
