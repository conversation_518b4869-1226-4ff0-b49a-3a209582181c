package com.ly.travel.car.carowner.biz.driver.info.mapper;

import com.ly.flight.toolkit.converter.BooleanConverter;
import com.ly.flight.toolkit.converter.FullDateConverter;
import com.ly.travel.car.carowner.biz.driver.info.model.DriverVO;
import com.ly.travel.car.carowner.dal.dataobject.DriverInfoDO;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueMappingStrategy;

/**
 * <AUTHOR>
 * @version DriverInfoMapping, 2025/9/10 00:49
 */
@Mapper(componentModel = "spring", nullValueMappingStrategy = NullValueMappingStrategy.RETURN_NULL, uses = { FullDateConverter.class, BooleanConverter.class })
public interface DriverInfoMapping {

    /**
     * d2v
     * @param d
     * @return
     */
    DriverVO d2v(DriverInfoDO d);
}
