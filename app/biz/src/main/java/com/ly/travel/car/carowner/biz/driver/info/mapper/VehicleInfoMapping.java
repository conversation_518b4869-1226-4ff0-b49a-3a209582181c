package com.ly.travel.car.carowner.biz.driver.info.mapper;

import com.ly.flight.toolkit.converter.BooleanConverter;
import com.ly.flight.toolkit.converter.FullDateConverter;
import com.ly.travel.car.carowner.biz.driver.info.model.VehicleInfoVO;
import com.ly.travel.car.carowner.dal.dataobject.VehicleInfoDO;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueMappingStrategy;

/**
 * <AUTHOR>
 * @version VehicleInfoMapping, 2025/9/10 00:57
 */
@Mapper(componentModel = "spring", nullValueMappingStrategy = NullValueMappingStrategy.RETURN_NULL, uses = { FullDateConverter.class, BooleanConverter.class })
public interface VehicleInfoMapping {

    VehicleInfoVO d2v(VehicleInfoDO d);
}
