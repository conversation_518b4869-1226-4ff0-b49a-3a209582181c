package com.ly.travel.car.carowner.biz.driver.info.model;

import com.ly.travel.car.carowner.common.enums.DriverStatusEnum;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version DriverVO, 2025/9/10 00:32
 */
@Data
public class DriverVO {

    private Long    id;

    /**
     * 环境        db_column: env
     */

    private String  env;
    /**
     * 联系电话        db_column: mobile
     */

    private String  mobile;
    /**
     * 手机号对应openId        db_column: open_id
     */

    private String  openId;
    /**
     * 姓名        db_column: name
     */

    private String  name;
    /**
     * 性别 男 女        db_column: sex
     */

    private String  sex;
    /**
     * 民族        db_column: nation
     */

    private String  nation;
    /**
     * 出生日期        db_column: birth_day
     */

    private String  birthDay;
    /**
     * 身份证号        db_column: id_card
     */

    private String  idCard;
    /**
     * 身份证有效期起        db_column: id_card_start_date
     */

    private String  idCardStartDate;
    /**
     * 身份证有效期止        db_column: id_card_end_date
     */

    private String  idCardEndDate;
    /**
     * 身份证签发机关        db_column: id_card_sign_organ
     */

    private String  idCardSignOrgan;
    /**
     * 身份证地址        db_column: id_card_address
     */

    private String  idCardAddress;
    /**
     * 身份证正面图片        db_column: id_card_front_picture
     */

    private String  idCardFrontPicture;
    /**
     * 身份证背面图片        db_column: id_card_back_picture
     */

    private String  idCardBackPicture;
    /**
     * 驾驶证初次领证日期        db_column: drive_first_issue_date
     */

    private String  driveFirstIssueDate;
    /**
     * 驾驶证号        db_column: drive_id_card
     */

    private String  driveIdCard;
    /**
     * 驾驶证姓名        db_column: drive_name
     */

    private String  driveName;
    /**
     * 准驾车型 C1 C2 C3 C4 B1 B2 A1 A2 A3        db_column: drive_model
     */

    private String  driveModel;
    /**
     * 驾驶证有效期起        db_column: drive_start_date
     */

    private String  driveStartDate;
    /**
     * 驾驶证有效期止        db_column: drive_end_date
     */

    private String  driveEndDate;
    /**
     * 驾驶证图片        db_column: drive_license_picture
     */

    private String  driveLicensePicture;
    /**
     * 最近完单时间        db_column: latest_complete_date
     */

    private Date latestCompleteDate;

    /**
     * 车牌号
     */
    private String  vehicleNo;
    /**
     * 状态 1.未认证 2.待系统认证 3.待人工认证 4.认证通过 5.认证不通过        db_column: status
     */

    private Integer status;
    /**
     * 审核不通过原因 1.证件照片不清晰 2.证件照片作假 3.驾驶证已过期 4.准驾车型不符 5.驾龄不满足要求 6.驾驶证状态异常 7.其他        db_column: unaudit_reason
     */

    private Integer unauditReason;
    /**
     * 审核不通过其他原因        db_column: unaudit_remark
     */

    private String  unauditRemark;
    /**
     * 封禁状态 0.未封禁 1.已封禁 与status合并使用        db_column: ban_status
     */

    private Integer banStatus;
    /**
     * 封禁原因        db_column: ban_reason
     */

    private String  banReason;
    /**
     * 人脸识别状态 0.待识别 1.识别通过        db_column: face_status
     */

    private Integer faceStatus;
    /**
     * 人脸识别时间        db_column: face_date
     */

    private Date    faceDate;
    /**
     * 人脸识别有效时长        db_column: face_valid_period
     */
    private Integer faceValidPeriod;

    /**
     * 跨城订单不超过
     */
    private int     crossCityLimit;
    /**
     * 市内订单不超过
     */
    private int     cityLimit;
    /**
     * 总订单不超过
     */
    private int     totalLimit;
    /**
     * 每日取消不超过
     */
    private int     erverdayCancelShold;
    /**
     * 每日不可接单时间(结束)
     */
    private String  everdayDown;
    /**
     * 每日不可接单时间(开始)
     */
    private String  everdayUp;

    /**
     * 是否未认证
     * true 未认证
     * @return
     */
    public boolean isUnCertified() {
        return this.getStatus().equals(DriverStatusEnum.UN_CERTIFIED.getCode());
    }

}
