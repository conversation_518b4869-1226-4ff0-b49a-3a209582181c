package com.ly.travel.car.carowner.biz.driver.info.model;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version VehicleInfoVO, 2025/9/10 00:43
 */
@Data
public class VehicleInfoVO {

    /**
     * 自增id        db_column: id
     */
    private long id;
    /**
     * 环境        db_column: env
     */
    private String env;
    /**
     * 车牌        db_column: vehicle_no
     */
    private String vehicleNo;
    /**
     * 司机，默认为0        db_column: driver_id
     */
    private long driverId;
    /**
     * 车牌号城市        db_column: vehicle_no_city
     */
    private String vehicleNoCity;
    /**
     * 车辆性质 02小型汽车、52新能源小型车        db_column: vehicle_type
     */
    private String vehicleType;
    /**
     * 车辆所有人        db_column: vehicle_owner
     */
    private String vehicleOwner;
    /**
     * 车辆使用性质 非营运、营转非、出租转非、预约出租转非        db_column: use_character
     */
    private String useCharacter;
    /**
     * 品牌编号        db_column: car_brand
     */
    private String carBrand;
    /**
     * 车型编号        db_column: car_model
     */

    @TableField(value = "car_model")
    private String carModel;
    /**
     * 车辆识别代号        db_column: vin
     */
    private String vin;
    /**
     * 颜色        db_column: color
     */
    private String color;
    /**
     * 注册日期        db_column: register_date
     */
    private String registerDate;
    /**
     * 发动机号码        db_column: engine_no
     */
    private String engineNo;
    /**
     * 发证日期        db_column: issue_date
     */
    private String issueDate;
    /**
     * 核定座数        db_column: auth_seat_num
     */
    private int authSeatNum;
    /**
     * 乘客座数        db_column: pass_seat_num
     */
    private int passSeatNum;
    /**
     * 行驶证图片        db_column: driving_license_picture
     */
    private String drivingLicensePicture;
    /**
     * 燃料类型 柴油 汽油 电动 混动        db_column: fuel_type
     */
    private String fuelType;
    /**
     * 品牌型号 行驶证识别        db_column: brand_model
     */
    private String brandModel;
    /**
     * 车辆状态        db_column: vehicle_status
     */
    private String vehicleStatus;
    /**
     * 检验有效期止        db_column: inspect_end_date
     */
    private String inspectEndDate;
    /**
     * 年检日期        db_column: check_date
     */
    private String checkDate;
    /**
     * 状态 1.未认证 2.待系统认证 3.待人工认证 4.认证通过 5.认证不通过        db_column: status
     */
    private Integer status;
    /**
     * 审核不通过原因 1.证件照片不清晰 2.证件照片作假 3.驾驶证已过期 4.准驾车型不符 5.驾龄不满足要求 6.驾驶证状态异常 7.其他        db_column: unaudit_reason
     */
    private Integer unauditReason;
    /**
     * 审核不通过其他原因        db_column: unaudit_remark
     */
    private String unauditRemark;
    /**
     * 车辆照片识别状态 0.待识别 1.识别通过        db_column: vehicle_picture_status
     */
    private Integer vehiclePictureStatus;
    /**
     * 车辆照片识别时间        db_column: vehicle_picture_start_date
     */
    private Date vehiclePictureStartDate;
    /**
     * 车辆照片识别有效时长        db_column: vehicle_valid_period
     */
    private Integer vehicleValidPeriod;
    /**
     * 车辆图片        db_column: vehicle_picture
     */
    private String vehiclePicture;
    /**
     * 创建人        db_column: create_user
     */
    private String createUser;
    /**
     * 创建时间        db_column: create_time
     */
    private Date createTime;
    /**
     * 修改人        db_column: update_user
     */
    private String updateUser;
    /**
     * 修改时间        db_column: update_time
     */
    private Date updateTime;
}
