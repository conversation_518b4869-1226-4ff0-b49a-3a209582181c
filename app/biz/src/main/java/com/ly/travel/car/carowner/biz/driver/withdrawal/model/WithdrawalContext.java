package com.ly.travel.car.carowner.biz.driver.withdrawal.model;

import com.ly.travel.car.carowner.dal.dataobject.DriverAccountDO;
import com.ly.travel.car.carowner.dal.dataobject.DriverBankCardDO;
import com.ly.travel.car.carowner.dal.dataobject.DriverBillDO;
import com.ly.travel.car.carowner.dal.dataobject.DriverWithdrawalDO;
import com.ly.travel.car.carowner.facade.request.DriverWithdrawalRequest;
import com.ly.travel.car.carowner.facade.request.WithdrawalApplyAuditRequest;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
public class WithdrawalContext {

    /**
     * 司机提现入参
     */
    private DriverWithdrawalRequest driverWithdrawalRequest;

    /**
     * 提现申请审核入参
     */
    private WithdrawalApplyAuditRequest withdrawalApplyAuditRequest;

    /**
     * 司机账户信息
     */
    private DriverAccountDO driverAccountDO;

    /**
     * 银行卡信息
     */
    private DriverBankCardDO driverBankCardDO;

    /**
     * 提现单信息
     */
    private DriverWithdrawalDO driverWithdrawalDO;

    /**
     * 车主账单流水
     */
    private List<DriverBillDO> driverBills;

    public Long getDriverId() {
        return driverWithdrawalRequest != null ? driverWithdrawalRequest.getDriverId() : withdrawalApplyAuditRequest.getDriverId();
    }

    public BigDecimal getAmount() {
        return driverWithdrawalRequest != null ? driverWithdrawalRequest.getAmount() : driverWithdrawalDO.getAmount();
    }

    public String getWithdrawNo() {
        return driverWithdrawalDO.getWithdrawNo();
    }

    public String getOperator() {
        if (withdrawalApplyAuditRequest != null) {
            return withdrawalApplyAuditRequest.getOperator();
        }
        return "system";
    }
}