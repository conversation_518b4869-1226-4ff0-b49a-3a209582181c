package com.ly.travel.car.carowner.biz.driver.withdrawal.service;

import com.ly.travel.car.carowner.dal.dataobject.DriverWithdrawalDO;
import com.ly.travel.car.carowner.facade.request.*;
import com.ly.travel.car.carowner.facade.response.CarOwnerResponseDTO;
import com.ly.travel.car.carowner.facade.response.RecognizeBankCardResponseDTO;

public interface DriverWithdrawalService {

    CarOwnerResponseDTO driverWithdrawal(DriverWithdrawalRequest request);

    CarOwnerResponseDTO sendSmsVerify(SmsVerifyRequest request);

    CarOwnerResponseDTO<RecognizeBankCardResponseDTO> recognizeBankCard(RecognizeBankCardRequest request);

    CarOwnerResponseDTO queryOrderIsWithdrawal(OrderIsWithdrawalRequest request);

    DriverWithdrawalDO findByWithdrawalNo(String withdrawalNo);

    void updateDriverWithdrawal(DriverWithdrawalDO driverWithdrawalDO);
}