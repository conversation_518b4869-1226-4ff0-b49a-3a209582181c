package com.ly.travel.car.carowner.biz.driver.withdrawal.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.biz.driver.account.enums.ChangeDriverAmountTypeEnum;
import com.ly.travel.car.carowner.biz.driver.account.service.ChangeDriverAmountProcess;
import com.ly.travel.car.carowner.biz.driver.withdrawal.model.WithdrawalContext;
import com.ly.travel.car.carowner.common.enums.*;
import com.ly.travel.car.carowner.common.utils.CarownerConfigCenterUtils;
import com.ly.travel.car.carowner.common.utils.DateUtil;
import com.ly.travel.car.carowner.common.utils.EnvUtil;
import com.ly.travel.car.carowner.dal.dataobject.*;
import com.ly.travel.car.carowner.dal.mapper.DriverBillMapper;
import com.ly.travel.car.carowner.dal.mapper.DriverInfoMapper;
import com.ly.travel.car.carowner.dal.mapper.DriverWithdrawLogMapper;
import com.ly.travel.car.carowner.dal.mapper.DriverWithdrawalMapper;
import com.ly.travel.car.carowner.integration.client.api.yeepay.YeePayApi;
import com.ly.travel.car.carowner.integration.client.api.yeepay.request.PayOrderRequest;
import com.ly.travel.car.carowner.integration.client.api.yeepay.response.PayOrderRemitRespDTOResult;
import com.ly.travel.car.carowner.integration.client.api.yeepay.response.PayOrderResponse;
import com.ly.travel.car.carowner.integration.wxwork.WxWorkAlertComponent;
import com.ly.travel.car.carowner.integration.wxwork.WxWorkTemplateEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public abstract class WithdrawalBaseService {
    @Resource
    protected DriverWithdrawLogMapper driverWithdrawLogMapper;
    @Resource
    protected DriverBillMapper driverBillMapper;
    @Resource
    protected DriverWithdrawalMapper driverWithdrawalMapper;
    @Value("${yeePay.callBackUrl}")
    protected String yeePayPayResultCallbackUrl;
    @Resource
    protected YeePayApi yeePayApi;
    @Resource
    protected DriverInfoMapper driverInfoMapper;
    @Resource
    protected WxWorkAlertComponent wxWorkAlertComponent;

    protected void callWithdraw(WithdrawalContext context) {
        if (Objects.equals(context.getDriverWithdrawalDO().getStatus(), DriverWithdrawalStatusEnum.PENDING_REVIEW.getStatus())) {
            log.info("当前提现需进行审核操作: driverId={}", context.getDriverId());
            return;
        }

        // 提现日志
        addWithdrawLog(context.getWithdrawNo(), context.getDriverId(), WithdrawLogTypeEnum.APPLY_WITHDRAW.code, "提现申请发起支付", context.getOperator());

        // 初始化变量
        DriverWithdrawalStatusEnum withdrawStatusEnum = DriverWithdrawalStatusEnum.WITHDRAWING;
        String remark = "提现申请支付中";
        String failReason = "";
        String yeePayOrderNo = "";
        PayOrderResponse payResp = null;

        // 构建易宝入参
        PayOrderRequest payOrderRequest = buildPayOrderRequest(context);
        try {
            // 调用易宝支付
            payResp = yeePayApi.payOrder(payOrderRequest);
        } catch (Exception e) {
            log.error("调用易宝打款接口失败: payOrderRequest={}", FastJsonUtils.toJSONString(payOrderRequest), e);
        }

        if (payResp != null && payResp.getResult() != null) {
            PayOrderRemitRespDTOResult result = payResp.getResult();
            if (!yeePayApi.isPayOrderSuccess(result.getReturnCode())) {
                String yeePayRemitFailCodes = CarownerConfigCenterUtils.YEE_PAY_REMIT_FAIL_CODES;
                if (yeePayRemitFailCodes.contains(result.getReturnCode())) {
                    withdrawStatusEnum = DriverWithdrawalStatusEnum.FAILURE;
                    remark = "提现申请支付失败，原因:" + result.getReturnMsg();
                    failReason = result.getReturnMsg();
                } else {
                    withdrawStatusEnum = DriverWithdrawalStatusEnum.EXCEPTION;
                    remark = "提现申请支付异常";
                }
                log.warn(remark);
            }
            yeePayOrderNo = result.getOrderNo();
        } else {
            // 异常情况
            withdrawStatusEnum = DriverWithdrawalStatusEnum.EXCEPTION;
            remark = "提现申请支付异常，调用易宝失败";
            log.warn(remark);
        }

        // 更新提现单
        updateDriverWithdrawal(context.getDriverWithdrawalDO(), withdrawStatusEnum.getStatus(), yeePayOrderNo, failReason);

        // 更新账户
        afterCallYeePay_updateDriverBillAndAccount(context, withdrawStatusEnum);

        // 提现日志
        addWithdrawLog(context.getWithdrawNo(), context.getDriverId(), WithdrawLogTypeEnum.PAY.code, remark, context.getOperator());

        // 易宝返回失败提醒
        yeePayFailNotice(withdrawStatusEnum, failReason, context.getDriverId(), context.getDriverWithdrawalDO().getApplyTime());
    }

    protected void yeePayFailNotice(DriverWithdrawalStatusEnum withdrawStatusEnum, String failReason, Long driverId, Date applyTime) {
        if (withdrawStatusEnum.getStatus() == DriverWithdrawalStatusEnum.FAILURE.getStatus()) {
            DriverInfoDO driverInfoDO = driverInfoMapper.selectById(driverId);
            wxWorkAlertComponent.alert(
                    WxWorkTemplateEnum.YEE_PAY_FAIL_NOTICE,
                    Arrays.asList(CarownerConfigCenterUtils.noticeUserConfig.split(",")),
                    driverInfoDO.getName(),
                    failReason,
                    DateUtil.date2String(applyTime)
            );
        }
    }

    protected void updateDriverWithdrawal(DriverWithdrawalDO driverWithdrawalDO, Integer status, String yeePayOrderNo, String failReason) {
        DriverWithdrawalDO updateEntity = new DriverWithdrawalDO();
        updateEntity.setId(driverWithdrawalDO.getId());
        updateEntity.setStatus(status);
        updateEntity.setYeePayOrderNo(yeePayOrderNo);
        updateEntity.setRemark(failReason);
        if (driverWithdrawalMapper.updateById(updateEntity) <= 0) {
            log.warn("司机提现更新提现单信息失败: updateEntity={}", FastJsonUtils.toJSONString(updateEntity));
        }
    }

    protected void beforeCallYeePay_updateDriverBillAndAccount(WithdrawalContext context) {
        DriverWithdrawalDO driverWithdrawalDO = context.getDriverWithdrawalDO();

        // 更新账户
        ChangeDriverAmountProcess.getInstance(ChangeDriverAmountTypeEnum.WITHDRAWING).changeAmount_sync(context.getDriverId(), driverWithdrawalDO.getAmount(),driverWithdrawalDO.getWithdrawNo());

        // 更新账单
        updateDriverBill(driverWithdrawalDO.getWithdrawNo(), context.getDriverBills());
    }

    protected void afterCallYeePay_updateDriverBillAndAccount(WithdrawalContext context, DriverWithdrawalStatusEnum withdrawStatusEnum) {
        if (withdrawStatusEnum.getStatus() == DriverWithdrawalStatusEnum.FAILURE.getStatus()) {
            DriverWithdrawalDO driverWithdrawalDO = context.getDriverWithdrawalDO();

            // 更新账户
            ChangeDriverAmountProcess.getInstance(ChangeDriverAmountTypeEnum.FAILURE_BACK).changeAmount_sync(context.getDriverId(), driverWithdrawalDO.getAmount(),driverWithdrawalDO.getWithdrawNo());

            // 更新账单
            updateDriverBill("", context.getDriverBills());
        }
    }

    protected PayOrderRequest buildPayOrderRequest(WithdrawalContext context) {
        DriverWithdrawalDO driverWithdrawalDO = context.getDriverWithdrawalDO();
        DriverBankCardDO driverBankCardDO = context.getDriverBankCardDO();
        PayOrderRequest payOrderRequest = new PayOrderRequest();
        payOrderRequest.setParentMerchantNo(CarownerConfigCenterUtils.yeePayParentMerchantNo);
        payOrderRequest.setMerchantNo(CarownerConfigCenterUtils.yeePayMerchantNo);
        payOrderRequest.setRequestNo(driverWithdrawalDO.getWithdrawNo() + "_" + (driverWithdrawalDO.getApplyTimes() + 1));
        payOrderRequest.setOrderAmount(driverWithdrawalDO.getAmount());
        payOrderRequest.setReceiverAccountNo(driverBankCardDO.getBankCardNo());
        payOrderRequest.setReceiverAccountName(driverBankCardDO.getOwnerName());
        payOrderRequest.setReceiverBankCode(driverBankCardDO.getBankCardCode());
        payOrderRequest.setBankAccountType(BankCardTypeEnum.getRemitCodeByCode(driverBankCardDO.getBankCardType()));
        payOrderRequest.setNotifyUrl(yeePayPayResultCallbackUrl);
        payOrderRequest.addHeader("key", CarownerConfigCenterUtils.yeePayAppId);
        return payOrderRequest;
    }

    protected void addWithdrawLog(String withdrawNo, Long driverId, Integer logType, String remark, String operator) {
        DriverWithdrawLogDO driverWithdrawLogDO = new DriverWithdrawLogDO();
        driverWithdrawLogDO.setWithdrawNo(withdrawNo);
        driverWithdrawLogDO.setLogType(logType);
        driverWithdrawLogDO.setRemark(remark);
        driverWithdrawLogDO.setDriverId(driverId);
        driverWithdrawLogDO.setCreateTime(new Date());
        driverWithdrawLogDO.setCreateUser(operator);
        if (driverWithdrawLogMapper.insert(driverWithdrawLogDO) <= 0) {
            log.warn("新增提现单日志信息失败: driverWithdrawLogDO={}", FastJsonUtils.toJSONString(driverWithdrawLogDO));
        }
    }

    protected void updateDriverBill(String withDrawNo, List<DriverBillDO> driverBillDOS) {
        List<String> billNos = driverBillDOS.stream().map(DriverBillDO::getBillNo).collect(Collectors.toList());
        DriverBillDO updateEntity = new DriverBillDO();
        updateEntity.setWithdrawNo(withDrawNo);
        updateEntity.setUpdateTime(new Date());
        updateEntity.setUpdateUser("system");
        int i = driverBillMapper.update(updateEntity, new LambdaQueryWrapper<DriverBillDO>()
                .in(DriverBillDO::getBillNo, billNos)
                .eq(DriverBillDO::getEnv, EnvUtil.getEnv().getValue()));
        if (i <= 0) {
            log.warn("更新账单信息失败: updateEntity={}", FastJsonUtils.toJSONString(updateEntity));
        }
    }

    protected DriverWithdrawalDO findDriverWithdrawalByWithdrawalNo(String withdrawalNo) {
        return driverWithdrawalMapper.selectOne(new LambdaQueryWrapper<DriverWithdrawalDO>()
                .eq(DriverWithdrawalDO::getWithdrawNo, withdrawalNo)
                .eq(DriverWithdrawalDO::getEnv, EnvUtil.getEnv().getValue())
                .last("limit 1"), false);
    }
}
