package com.ly.travel.car.carowner.biz.driver.withdrawal.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Maps;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.biz.driver.withdrawal.model.WithdrawalContext;
import com.ly.travel.car.carowner.biz.driver.withdrawal.service.DriverWithdrawalService;
import com.ly.travel.car.carowner.biz.driver.withdrawal.service.WithdrawalBaseService;
import com.ly.travel.car.carowner.biz.driver.withdrawal.validator.ValidationStrategy;
import com.ly.travel.car.carowner.common.cache.CacheUtils;
import com.ly.travel.car.carowner.common.enums.*;
import com.ly.travel.car.carowner.common.exception.BusinessException;
import com.ly.travel.car.carowner.common.lock.DistributedRedisLock;
import com.ly.travel.car.carowner.common.utils.*;
import com.ly.travel.car.carowner.dal.dataobject.*;
import com.ly.travel.car.carowner.facade.request.*;
import com.ly.travel.car.carowner.facade.response.CarOwnerResponseDTO;
import com.ly.travel.car.carowner.facade.response.RecognizeBankCardResponseDTO;
import com.ly.travel.car.carowner.integration.client.api.yeepay.request.GetcardbinRequest;
import com.ly.travel.car.carowner.integration.client.api.yeepay.response.BankCardRecognizeResp;
import com.ly.travel.car.carowner.integration.sms.SmsComponent;
import com.ly.travel.car.carowner.integration.sms.SmsTemplatesDefinedList;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

@Service
@Slf4j
public class DriverWithdrawalServiceImpl extends WithdrawalBaseService implements DriverWithdrawalService {
    private final List<ValidationStrategy<WithdrawalContext>> validationStrategies;
    @Resource
    private SmsComponent smsComponent;
    @Resource
    private DistributedRedisLock redisLock;

    @Autowired
    public DriverWithdrawalServiceImpl(List<ValidationStrategy<WithdrawalContext>> validationStrategies) {
        this.validationStrategies = validationStrategies;
    }

    @Override
    @Transactional
    public CarOwnerResponseDTO driverWithdrawal(DriverWithdrawalRequest request) {
        RLock lock = null;
        String lockKey = "";
        try {

            // 1、提现验证
            WithdrawalContext context = validate(request);

            lockKey = CacheKeyEnum.DRIVER_WITHDRAWAL.format(request.getDriverId());
            lock = redisLock.tryAcquire(lockKey, FastJsonUtils.toJSONString(request));
            if (lock == null) {
                log.warn("司机提现获取并发锁失败: lockKey={}", lockKey);
                throw new BusinessException(ResultEnum.SUBMIT_ERROR);
            }

            // 2、生成提现单
            generateWithdrawalOrder(context);

            // 3、调用易宝前更新账单和账户
            beforeCallYeePay_updateDriverBillAndAccount(context);

            // 4、发起提现
            callWithdraw(context);

            // 5、缓存当日提现次数
            cacheWithdrawCount(context.getDriverId());

        } catch (BusinessException e) {
            return CarOwnerResponseDTO.fail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("司机提现失败: request={}", FastJsonUtils.toJSONString(request), e);
            return CarOwnerResponseDTO.fail(ResultEnum.SUBMIT_ERROR.getCode(), ResultEnum.SUBMIT_ERROR.getDesc());
        } finally {
            if (lock != null) {
                redisLock.release(lock, lockKey, "");
            }
        }
        return CarOwnerResponseDTO.succeed(null);
    }

    private void cacheWithdrawCount(Long driverId) {
        String cacheKey = driverId + "_" + DateUtil.getTodayInYYYYMMDD();
        String cacheValue = CacheUtils.getCacheValue(cacheKey);
        if (StringUtils.isBlank(cacheValue)) {
            CacheUtils.setCacheValue(cacheKey, "1", DateUtil.getSecondsUntilMidnight());
            return;
        }
        CacheUtils.setCacheValue(cacheKey, String.valueOf(Integer.valueOf(cacheValue) + 1), DateUtil.getSecondsUntilMidnight());
    }

    private WithdrawalContext validate(DriverWithdrawalRequest request) {
        WithdrawalContext context = WithdrawalContext.builder().driverWithdrawalRequest(request).build();
        validationStrategies.stream().forEach(strategy -> strategy.validate(context));
        return context;
    }

    private void generateWithdrawalOrder(WithdrawalContext context) {
        DriverWithdrawalDO driverWithdrawalDO = new DriverWithdrawalDO();
        driverWithdrawalDO.setDriverId(context.getDriverId());
        driverWithdrawalDO.setWithdrawNo(IdGeneratorUtil.create("T", "X"));
        driverWithdrawalDO.setEnv(EnvUtil.getEnv().getValue());
        driverWithdrawalDO.setDriverBankCardId(context.getDriverBankCardDO().getId() + "");
        driverWithdrawalDO.setAmount(context.getAmount());
        int status = CarownerConfigCenterUtils.driverWithdrawalManualReviewStatus;
        status = Objects.equals(status, 1) ? DriverWithdrawalStatusEnum.PENDING_REVIEW.getStatus() : DriverWithdrawalStatusEnum.WITHDRAWING.getStatus();
        driverWithdrawalDO.setStatus(status);
        driverWithdrawalDO.setApplyTime(new Date());
        driverWithdrawalDO.setApprovalTime(null);
        driverWithdrawalDO.setApplyTimes(0);
        driverWithdrawalDO.setCreateTime(new Date());
        driverWithdrawalDO.setCreateUser(String.format("司机-%s", context.getDriverId()));
        DriverInfoDO driverInfoDO = driverInfoMapper.selectById(context.getDriverId());
        driverWithdrawalDO.setDriverDeviceId(driverInfoDO.getOpenId());
        if (driverWithdrawalMapper.insert(driverWithdrawalDO) <= 0) {
            log.warn("司机提现创建提现单失败: driverWithdrawalDO={}", FastJsonUtils.toJSONString(driverWithdrawalDO));
        }
        context.setDriverWithdrawalDO(driverWithdrawalDO);
    }

    @Override
    public CarOwnerResponseDTO sendSmsVerify(SmsVerifyRequest request) {
        try {
            // 1、入参验证
            if (StringUtils.isBlank(request.getDriverMobile())) {
                log.warn("司机手机号不能为空");
                throw new BusinessException("司机手机号不能为空");
            }

            // 2、生成短信验证码
            String verifyCode = String.format("%06d", new Random().nextInt(1000000));

            // 3、存入缓存 有效期60s
            addCache(verifyCode, request.getDriverMobile());

            // 4、发送短信
            smsComponent.sendTemplateMsg(SmsTemplatesDefinedList.WITHDRAWAL_TEMPLATE
                    .setTemplateParams(verifyCode, "http://ss").setMobile(request.getDriverMobile()));

        } catch (BusinessException e) {
            log.warn(e.getMessage());
            return CarOwnerResponseDTO.fail(e.getMessage());
        } catch (Exception e) {
            log.error("提现短信验证失败: request={}", FastJsonUtils.toJSONString(request), e);
            return CarOwnerResponseDTO.fail(ResultEnum.INTERNAL_SERVER_ERROR.getDesc());
        }
        return CarOwnerResponseDTO.succeed(null);
    }

    @Override
    public CarOwnerResponseDTO<RecognizeBankCardResponseDTO> recognizeBankCard(RecognizeBankCardRequest request) {
        // 1、入参验证
        if (StringUtils.isBlank(request.getBankCardNo())) {
            log.warn("银行卡号不能为空");
            throw new BusinessException("银行卡号不能为空");
        }
        RecognizeBankCardResponseDTO result = new RecognizeBankCardResponseDTO();
        GetcardbinRequest getcardbinRequest = new GetcardbinRequest();
        getcardbinRequest.setBankCardNo(request.getBankCardNo());
        getcardbinRequest.addHeader("key", CarownerConfigCenterUtils.yeePayAppId);
        BankCardRecognizeResp bankCardRecognizeResp = yeePayApi.recognizeBankCard(getcardbinRequest);
        if (bankCardRecognizeResp != null) {
            result.setBankCardType(bankCardRecognizeResp.getBankCardType());
            result.setBankCardTypeName(BankCardTypeEnum.getDescByCode(bankCardRecognizeResp.getBankCardType()));
            result.setBankName(bankCardRecognizeResp.getBankName());
            result.setBankCardCode(bankCardRecognizeResp.getBankCardCode());
        }
        return CarOwnerResponseDTO.succeed(result);
    }

    private void addCache(String verifyCode, String driverMobile) {
        HashMap<Object, Object> map = Maps.newHashMap();
        map.put("verifyCode", verifyCode);
        map.put("verifyCount", 0);
        CacheUtils.setCacheValue(driverMobile, FastJsonUtils.toJSONString(map), 60);
    }

    @Override
    public CarOwnerResponseDTO queryOrderIsWithdrawal(OrderIsWithdrawalRequest request) {
        try {
            if (StringUtils.isBlank(request.getPassengerOrderNo())) {
                log.warn("乘客订单号不能为空");
                throw new BusinessException("乘客订单号不能为空");
            }

            List<DriverBillDO> driverBillDOS = getOrderDriverBills(request.getPassengerOrderNo());
            if (CollectionUtils.isEmpty(driverBillDOS)) {
                log.warn("订单无关联账单: passengerOrderNo={}", request.getPassengerOrderNo());
                return CarOwnerResponseDTO.succeed(false);
            }

            // true-已提现 false-未提现  TODO 提现单不为空 订单均为已提现
            boolean hasWithdrawal = driverBillDOS.stream()
                    .anyMatch(driverBillDO -> StringUtils.isNotBlank(driverBillDO.getWithdrawNo()));

            return CarOwnerResponseDTO.succeed(hasWithdrawal);

        } catch (BusinessException e) {
            return CarOwnerResponseDTO.fail(e.getMessage());
        } catch (Exception e) {
            log.error("查询订单是否提现失败: request={}", FastJsonUtils.toJSONString(request), e);
            return CarOwnerResponseDTO.fail(ResultEnum.INTERNAL_SERVER_ERROR.getDesc());
        }
    }

    private List<DriverBillDO> getOrderDriverBills(String passengerOrderNo) {
        return driverBillMapper.selectList(
                new LambdaQueryWrapper<DriverBillDO>()
                        .eq(DriverBillDO::getVoucherNo, passengerOrderNo)
                        .in(DriverBillDO::getVoucherType, Arrays.asList(VoucherTypeEnum.DRIVER_ORDER_SETTLEMENT.getType(), VoucherTypeEnum.DRIVER_ORDER_FREE_COMMISSION.getType()))
                        .eq(DriverBillDO::getEnv, EnvUtils.getEnvCode())
        );
    }

    @Override
    public DriverWithdrawalDO findByWithdrawalNo(String withdrawalNo) {
        return findDriverWithdrawalByWithdrawalNo(withdrawalNo);
    }

    @Override
    public void updateDriverWithdrawal(DriverWithdrawalDO driverWithdrawalDO) {
        DriverWithdrawalDO updateEntity = new DriverWithdrawalDO();
        updateEntity.setId(driverWithdrawalDO.getId());
        updateEntity.setStatus(DriverWithdrawalStatusEnum.EXCEPTION.getStatus());
        updateEntity.setUpdateTime(new Date());
        if (driverWithdrawalMapper.updateById(updateEntity) <= 0) {
            log.warn("更新提现单失败: updateEntity={}", FastJsonUtils.toJSONString(updateEntity));
        } else {
            log.info("更新提现单成功: updateEntity={}", FastJsonUtils.toJSONString(updateEntity));
        }
    }
}
