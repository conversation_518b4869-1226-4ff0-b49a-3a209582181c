package com.ly.travel.car.carowner.biz.driver.withdrawal.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.biz.driver.account.service.DriverAccountService;
import com.ly.travel.car.carowner.biz.driver.bill.service.DriverBillService;
import com.ly.travel.car.carowner.biz.driver.withdrawal.model.WithdrawalContext;
import com.ly.travel.car.carowner.biz.driver.withdrawal.service.WithdrawalApplyAuditService;
import com.ly.travel.car.carowner.biz.driver.withdrawal.service.WithdrawalBaseService;
import com.ly.travel.car.carowner.common.enums.*;
import com.ly.travel.car.carowner.common.exception.BusinessException;
import com.ly.travel.car.carowner.common.lock.DistributedRedisLock;
import com.ly.travel.car.carowner.common.utils.EnvUtil;
import com.ly.travel.car.carowner.dal.dataobject.*;
import com.ly.travel.car.carowner.dal.mapper.DriverBankCardMapper;
import com.ly.travel.car.carowner.facade.request.WithdrawalApplyAuditRequest;
import com.ly.travel.car.carowner.facade.response.CarOwnerResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Service
@Slf4j
public class WithdrawalApplyAuditServiceImpl extends WithdrawalBaseService implements WithdrawalApplyAuditService {
    @Resource
    private DriverBankCardMapper driverBankCardMapper;
    @Resource
    private DriverAccountService driverAccountService;
    @Resource
    private DriverBillService driverBillService;
    @Resource
    private DistributedRedisLock redisLock;

    @Override
    public CarOwnerResponseDTO withdrawalApplyAudit(WithdrawalApplyAuditRequest request) {
        RLock lock = null;
        String lockKey = "";
        try {
            // 1、参数验证
            WithdrawalContext context = validate(request);

            lockKey = CacheKeyEnum.DRIVER_WITHDRAWAL.format(request.getDriverId());
            lock = redisLock.tryAcquire(lockKey, FastJsonUtils.toJSONString(request));
            if (lock == null) {
                log.warn("提现申请审核获取并发锁失败: lockKey={}", lockKey);
                throw new BusinessException("提现申请审核获取并发锁失败");
            }

            // 2、构建基本信息
            buildWithdrawalContext(context);

            // 3、更新提现单信息
            updateDriverWithdrawal(context);

            // 4、发起提现
            callWithdraw(context);

        } catch (BusinessException e) {
            return CarOwnerResponseDTO.fail(e.getMessage());
        } catch (Exception e) {
            log.error("提现申请审核失败: request={}", FastJsonUtils.toJSONString(request), e);
            return CarOwnerResponseDTO.fail(ResultEnum.INTERNAL_SERVER_ERROR.getDesc());
        } finally {
            if (lock != null) {
                redisLock.release(lock, lockKey, "");
            }
        }
        return CarOwnerResponseDTO.succeed(null);
    }

    private void updateDriverWithdrawal(WithdrawalContext context) {
        DriverWithdrawalDO driverWithdrawalDO = context.getDriverWithdrawalDO();
        DriverWithdrawalDO updateEntity = new DriverWithdrawalDO();
        updateEntity.setId(driverWithdrawalDO.getId());
        updateEntity.setStatus(DriverWithdrawalStatusEnum.WITHDRAWING.getStatus());
        updateEntity.setApplyTimes((driverWithdrawalDO.getApplyTimes() + 1));
        updateEntity.setUpdateUser(context.getOperator());
        updateEntity.setUpdateTime(new Date());
        if (driverWithdrawalMapper.updateById(updateEntity) <= 0) {
            log.warn("提现申请更新提现单失败: updateEntity={}", FastJsonUtils.toJSONString(updateEntity));
        }
        driverWithdrawalDO.setStatus(updateEntity.getStatus());
    }

    private void buildWithdrawalContext(WithdrawalContext context) {
        // 提现单信息
        context.setDriverWithdrawalDO(queryDriverWithdrawalInfo(context));

        // 银行卡信息
        context.setDriverBankCardDO(queryDriverBankCardInfo(context));

        // 账户信息
        context.setDriverAccountDO(driverAccountService.queryDriverAccount(context.getDriverId()));

        // 账单信息
        context.setDriverBills(driverBillService.findDriverBillByWithdrawNo(context.getWithdrawNo()));
    }

    private DriverBankCardDO queryDriverBankCardInfo(WithdrawalContext context) {
        return driverBankCardMapper.selectOne(new LambdaQueryWrapper<DriverBankCardDO>()
                .eq(DriverBankCardDO::getDriverId, context.getDriverId())
                .eq(DriverBankCardDO::getStatus, 1), false);
    }

    private DriverWithdrawalDO queryDriverWithdrawalInfo(WithdrawalContext context) {
        DriverWithdrawalDO driverWithdrawalDO = findDriverWithdrawalByWithdrawalNo(context.getWithdrawalApplyAuditRequest().getWithdrawNo());
        if (driverWithdrawalDO.getStatus() != DriverWithdrawalStatusEnum.PENDING_REVIEW.getStatus()) {
            throw new BusinessException("提现单状态非待审核");
        }
        return driverWithdrawalDO;
    }

    private WithdrawalContext validate(WithdrawalApplyAuditRequest request) {
        if (request == null) {
            log.warn("入参不能为空");
            throw new BusinessException("入参不能为空");
        }

        String validate = request.validate();
        if (StringUtils.isNotBlank(validate)) {
            log.warn(validate);
            throw new BusinessException(validate);
        }

        return WithdrawalContext.builder().withdrawalApplyAuditRequest(request).build();
    }
}
