package com.ly.travel.car.carowner.biz.driver.withdrawal.service.impl;

import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.biz.driver.account.enums.ChangeDriverAmountTypeEnum;
import com.ly.travel.car.carowner.biz.driver.account.service.ChangeDriverAmountProcess;
import com.ly.travel.car.carowner.biz.driver.withdrawal.service.WithdrawalBaseService;
import com.ly.travel.car.carowner.biz.driver.withdrawal.service.YeePayCallbackService;
import com.ly.travel.car.carowner.common.enums.*;
import com.ly.travel.car.carowner.common.exception.BusinessException;
import com.ly.travel.car.carowner.common.lock.DistributedRedisLock;
import com.ly.travel.car.carowner.dal.dataobject.DriverWithdrawalDO;
import com.ly.travel.car.carowner.facade.request.YeePayCallbackRequest;
import com.ly.travel.car.carowner.facade.response.CarOwnerResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Service
@Slf4j
public class YeePayCallbackServiceImpl extends WithdrawalBaseService implements YeePayCallbackService {
    @Resource
    private DistributedRedisLock redisLock;

    @Override
    public CarOwnerResponseDTO yeePayCallback(YeePayCallbackRequest request) {
        RLock lock = null;
        String lockKey = "";
        try {

            // 1、参数验证
            DriverWithdrawalDO driverWithdrawal = validate(request);

            lockKey = CacheKeyEnum.DRIVER_WITHDRAWAL.format(driverWithdrawal.getDriverId());
            lock = redisLock.tryAcquire(lockKey, FastJsonUtils.toJSONString(request));
            if (lock == null) {
                log.warn("易宝支付回调获取并发锁失败: lockKey={}", lockKey);
                throw new BusinessException("易宝支付回调获取并发锁失败");
            }

            // 2、易宝回调打款失败
            if (!request.isSuccess()) {
                return handleFail(driverWithdrawal, request);
            }

            // 3、易宝回调打款成功
            handleSuccess(driverWithdrawal, request);

        } catch (BusinessException e) {
            log.warn(e.getMessage());
            return CarOwnerResponseDTO.fail(e.getMessage());
        } catch (Exception e) {
            log.error("易宝回调失败: request={}", FastJsonUtils.toJSONString(request), e);
            return CarOwnerResponseDTO.fail(ResultEnum.INTERNAL_SERVER_ERROR.getDesc());
        } finally {
            if (lock != null) {
                redisLock.release(lock, lockKey, "");
            }
        }
        return CarOwnerResponseDTO.succeed(null);
    }

    private CarOwnerResponseDTO handleFail(DriverWithdrawalDO driverWithdrawal, YeePayCallbackRequest request) {

        // 更新提现单
        updateDriverWithdrawal(driverWithdrawal, request.isSuccess(), request.getFailReason());

        // 易宝返回失败提醒
        yeePayFailNotice(DriverWithdrawalStatusEnum.FAILURE, request.getFailReason(), driverWithdrawal.getDriverId(), driverWithdrawal.getApplyTime());

        return CarOwnerResponseDTO.succeed(null);
    }

    private void handleSuccess(DriverWithdrawalDO driverWithdrawal, YeePayCallbackRequest request) {
        // 更新提现单
        updateDriverWithdrawal(driverWithdrawal, request.isSuccess(), "");

        // 更新账户
        ChangeDriverAmountProcess.getInstance(ChangeDriverAmountTypeEnum.WITHDRAWAL_SUCCESS).changeAmount_sync(driverWithdrawal.getDriverId(), driverWithdrawal.getAmount(),driverWithdrawal.getWithdrawNo());
    }

    private void updateDriverWithdrawal(DriverWithdrawalDO driverWithdrawalDO, boolean success, String failReason) {
        Date now = new Date();
        DriverWithdrawalDO updateEntity = new DriverWithdrawalDO();
        updateEntity.setId(driverWithdrawalDO.getId());
        updateEntity.setUpdateTime(now);
        updateEntity.setUpdateUser("易宝回调Job");
        updateEntity.setStatus(success ? DriverWithdrawalStatusEnum.SUCCESS.getStatus() : DriverWithdrawalStatusEnum.PENDING_REVIEW.getStatus());
        updateEntity.setRemitBackTime(now);
        updateEntity.setApprovalTime(now);
        updateEntity.setRemark(failReason);
        if (driverWithdrawalMapper.updateById(updateEntity) <= 0) {
            log.warn("易宝回调更新提现单信息失败: updateEntity={}", FastJsonUtils.toJSONString(updateEntity));
        }
    }

    private DriverWithdrawalDO validate(YeePayCallbackRequest request) {
        DriverWithdrawalDO driverWithdrawal = findDriverWithdrawalByWithdrawalNo(request.getWithdrawNo());
        if (driverWithdrawal == null) {
            log.warn("未找到提现记录: withdrawNo={}", request.getWithdrawNo());
            throw new BusinessException("未找到提现记录");
        }

        if (!driverWithdrawal.getYeePayOrderNo().equals(request.getYeePayOrderNo())) {
            addWithdrawLog(request.getWithdrawNo(), driverWithdrawal.getDriverId(), WithdrawLogTypeEnum.PAY.code, "易宝支付回调:易宝支付单号不匹配", "");
            log.warn("易宝支付单号不匹配: dbYeePayOrderNo={},requestYeePayOrderNo={}", driverWithdrawal.getYeePayOrderNo(), request.getYeePayOrderNo());
            throw new BusinessException("易宝支付单号不匹配");
        }

        if (driverWithdrawal.getStatus() != DriverWithdrawalStatusEnum.WITHDRAWING.getStatus()) {
            String errorMsg = String.format("当前状态是：%s,不能处理", DriverWithdrawalStatusEnum.getNameByStatus(driverWithdrawal.getStatus()));
            addWithdrawLog(request.getWithdrawNo(), driverWithdrawal.getDriverId(), WithdrawLogTypeEnum.PAY.code, "易宝支付回调:" + errorMsg, "");
            log.warn(errorMsg);
            throw new BusinessException(errorMsg);
        }

        addWithdrawLog(request.getWithdrawNo(), driverWithdrawal.getDriverId(), WithdrawLogTypeEnum.PAY.code, "易宝支付回调结果:"
                + request.isSuccess() + (!request.isSuccess() ? "，失败原因=" + request.getFailReason() : ""), "");

        return driverWithdrawal;
    }
}
