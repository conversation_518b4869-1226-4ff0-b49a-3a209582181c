package com.ly.travel.car.carowner.biz.driver.withdrawal.validator.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.travel.car.carowner.biz.driver.withdrawal.model.WithdrawalContext;
import com.ly.travel.car.carowner.biz.driver.withdrawal.validator.ValidationStrategy;
import com.ly.travel.car.carowner.common.enums.DriverFreezeEnum;
import com.ly.travel.car.carowner.common.enums.ResultEnum;
import com.ly.travel.car.carowner.common.exception.BusinessException;
import com.ly.travel.car.carowner.dal.dataobject.DriverAccountDO;
import com.ly.travel.car.carowner.dal.mapper.DriverAccountMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * 验证司机账户
 */
@Slf4j
@Component
@Order(2)
public class ValidateDriverAccountStrategy implements ValidationStrategy<WithdrawalContext> {
    @Resource
    private DriverAccountMapper driverAccountMapper;

    @Override
    public void validate(WithdrawalContext context) {
        DriverAccountDO driverAccountDO = getDriverAccount(context.getDriverId());
        validateDriverAccountExists(driverAccountDO, context.getDriverId());
        validateAccountFreezeStatus(driverAccountDO);
        validateWithdrawalAmount(context.getAmount(), driverAccountDO.getAvailableAmount());
        context.setDriverAccountDO(driverAccountDO);
    }

    private void validateDriverAccountExists(DriverAccountDO driverAccountDO, Long driverId) {
        if (driverAccountDO == null) {
            log.warn("司机账户不存在: driverId={}", driverId);
            throw new BusinessException(ResultEnum.SUBMIT_ERROR);
        }
    }

    private void validateAccountFreezeStatus(DriverAccountDO driverAccountDO) {
        log.info("账户是否冻结: isFreeze={}", driverAccountDO.getIsFreeze());
        if (Objects.equals(driverAccountDO.getIsFreeze(), DriverFreezeEnum.CORRECT.getCode())) {
            log.warn("账户冻结中，请联系客服处理: driverId={}", driverAccountDO.getDriverId());
            throw new BusinessException(ResultEnum.ACCOUNT_STATUS_ERROR);
        }
    }

    private void validateWithdrawalAmount(BigDecimal requestedAmount, BigDecimal availableAmount) {
        log.info("验证提现金额: requestedAmount={},availableAmount={}", requestedAmount, availableAmount);
        if (requestedAmount.compareTo(availableAmount) > 0) {
            log.warn("提现金额不能超过可用金额: amount={},availableAmount={}", requestedAmount, availableAmount);
            throw new BusinessException(ResultEnum.WITHDRAWAL_AMOUNT_ERROR);
        }
    }

    private DriverAccountDO getDriverAccount(Long driverId) {
        return driverAccountMapper.selectOne(new LambdaQueryWrapper<DriverAccountDO>()
                .eq(DriverAccountDO::getDriverId, driverId), false);
    }
}
