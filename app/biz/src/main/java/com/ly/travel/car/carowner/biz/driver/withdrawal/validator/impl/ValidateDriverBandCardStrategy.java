package com.ly.travel.car.carowner.biz.driver.withdrawal.validator.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.travel.car.carowner.biz.driver.withdrawal.model.WithdrawalContext;
import com.ly.travel.car.carowner.biz.driver.withdrawal.validator.ValidationStrategy;
import com.ly.travel.car.carowner.common.enums.ResultEnum;
import com.ly.travel.car.carowner.common.exception.BusinessException;
import com.ly.travel.car.carowner.dal.dataobject.DriverBankCardDO;
import com.ly.travel.car.carowner.dal.mapper.DriverBankCardMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 验证银行卡
 */
@Slf4j
@Component
@Order(3)
public class ValidateDriverBandCardStrategy implements ValidationStrategy<WithdrawalContext> {
    @Resource
    private DriverBankCardMapper driverBankCardMapper;

    @Override
    public void validate(WithdrawalContext context) {
        DriverBankCardDO driverBankCard = getDriverBankCard(context.getDriverId());
        validateDriverBankCard(driverBankCard, context.getDriverId());
        context.setDriverBankCardDO(driverBankCard);
    }

    private void validateDriverBankCard(DriverBankCardDO driverBankCard, Long driverId) {
        if (driverBankCard == null || StringUtils.isBlank(driverBankCard.getBankCardNo())) {
            log.warn("查询司机银行卡信息为空: driverId={}", driverId);
            throw new BusinessException(ResultEnum.CAR_BAND_ERROR);
        }
    }

    private DriverBankCardDO getDriverBankCard(Long driverId) {
        return driverBankCardMapper.selectOne(new LambdaQueryWrapper<DriverBankCardDO>()
                .eq(DriverBankCardDO::getDriverId, driverId)
                .eq(DriverBankCardDO::getStatus, 1), false);
    }
}