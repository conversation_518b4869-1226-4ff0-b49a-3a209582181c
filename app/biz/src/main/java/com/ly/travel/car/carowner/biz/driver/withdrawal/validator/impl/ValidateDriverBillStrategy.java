package com.ly.travel.car.carowner.biz.driver.withdrawal.validator.impl;

import com.ly.travel.car.carowner.biz.driver.bill.service.DriverBillService;
import com.ly.travel.car.carowner.biz.driver.withdrawal.model.WithdrawalContext;
import com.ly.travel.car.carowner.biz.driver.withdrawal.validator.ValidationStrategy;
import com.ly.travel.car.carowner.common.enums.ResultEnum;
import com.ly.travel.car.carowner.common.exception.BusinessException;
import com.ly.travel.car.carowner.dal.dataobject.DriverBillDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * 车主流水验证器
 */
@Slf4j
@Component
@Order(5)
public class ValidateDriverBillStrategy implements ValidationStrategy<WithdrawalContext> {
    @Resource
    private DriverBillService driverBillService;

    @Override
    public void validate(WithdrawalContext context) {
        List<DriverBillDO> driverBills = fetchDriverBills(context.getDriverId());
        BigDecimal sumAmount = driverBills.stream()
                .map(DriverBillDO::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 验证提现金额
        validateWithdrawalAmount(context.getAmount(), sumAmount);

        context.setDriverBills(driverBills);
    }

    private List<DriverBillDO> fetchDriverBills(Long driverId) {
        List<DriverBillDO> driverBills = driverBillService.findDriverBillByDriverId(driverId);
        if (CollectionUtils.isEmpty(driverBills)) {
            log.warn("查询司机账单信息为空: driverId={}", driverId);
            throw new BusinessException(ResultEnum.WITHDRAWAL_AMOUNT_ERROR);
        }
        return driverBills;
    }

    private void validateWithdrawalAmount(BigDecimal requestedAmount, BigDecimal totalBillAmount) {
        log.info("账单金额验证: requestedAmount={},totalBillAmount={}", requestedAmount, totalBillAmount);
        if (requestedAmount == null || requestedAmount.compareTo(BigDecimal.ZERO) <= 0 || requestedAmount.compareTo(totalBillAmount) != 0) {
            log.warn("提现金额异常或与实际可提现金额不符: amount={},sumAmount={}", requestedAmount, totalBillAmount);
            throw new BusinessException(ResultEnum.WITHDRAWAL_AMOUNT_ERROR);
        }
    }
}