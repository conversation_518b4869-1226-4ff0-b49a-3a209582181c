package com.ly.travel.car.carowner.biz.driver.withdrawal.validator.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.biz.driver.withdrawal.model.WithdrawalContext;
import com.ly.travel.car.carowner.biz.driver.withdrawal.validator.ValidationStrategy;
import com.ly.travel.car.carowner.common.cache.CacheUtils;
import com.ly.travel.car.carowner.common.enums.ResultEnum;
import com.ly.travel.car.carowner.common.exception.BusinessException;
import com.ly.travel.car.carowner.common.utils.DateUtil;
import com.ly.travel.car.carowner.common.utils.EnvUtil;
import com.ly.travel.car.carowner.dal.dataobject.DriverWithdrawalDO;
import com.ly.travel.car.carowner.dal.dataobject.DriverWithdrawalOtherAmountSettingDO;
import com.ly.travel.car.carowner.dal.dataobject.DriverWithdrawalSettingDO;
import com.ly.travel.car.carowner.dal.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 验证提现配置
 */
@Slf4j
@Component
@Order(4)
public class ValidateDriverWithdrawConfigStrategy implements ValidationStrategy<WithdrawalContext> {
    @Resource
    private DriverWithdrawalOtherAmountSettingMapper driverWithdrawalOtherAmountSettingMapper;
    @Resource
    private DriverWithdrawalSettingMapper driverWithdrawalSettingMapper;
    @Resource
    private DriverWithdrawalMapper driverWithdrawalMapper;

    @Override
    public void validate(WithdrawalContext context) {
        // 当日提现次数
        int todayWithdrawCount = 0;
        String cacheValue = CacheUtils.getCacheValue(context.getDriverId() + "_" + DateUtil.getTodayInYYYYMMDD());
        if (StringUtils.isNotBlank(cacheValue)) {
            todayWithdrawCount = Integer.valueOf(cacheValue);
        }

        // 提现配置
        DriverWithdrawalSettingDO driverWithdrawalSettingDO = queryDriverWithdrawalSetting();
        if (driverWithdrawalSettingDO != null) {
            log.info("提现配置验证提现次数: limitSum={},todayWithdrawCount={}", driverWithdrawalSettingDO.getLimitSum(), todayWithdrawCount);
            if (driverWithdrawalSettingDO.getLimitSum() < todayWithdrawCount) {
                log.warn("当日超过最大提现次数: limitSum={},todayWithdrawCount={}", driverWithdrawalSettingDO.getLimitSum(), todayWithdrawCount);
                throw new BusinessException(ResultEnum.THE_DAY_WITHDRAWAL_COUNT_ERROR);
            }

            log.info("提现配置验证提现单次最大金额: amount={},limitAmount={}", context.getAmount(), driverWithdrawalSettingDO.getLimitAmount());
            if (context.getAmount().compareTo(driverWithdrawalSettingDO.getLimitAmount()) > 0) {
                log.warn("提现金额超过单次最大提现金额: amount={},limitAmount={}", context.getAmount(), driverWithdrawalSettingDO.getLimitAmount());
                throw new BusinessException(ResultEnum.MAXIMUM_WITHDRAWAL_AMOUNT_PER_TRANSACTION_ERROR);
            }

            List<DriverWithdrawalOtherAmountSettingDO> driverWithdrawalOtherAmountSettingDOS = queryDriverWithdrawalOtherAmountSettingList(driverWithdrawalSettingDO);
            if (CollectionUtils.isNotEmpty(driverWithdrawalOtherAmountSettingDOS)) {
                for (DriverWithdrawalOtherAmountSettingDO settingDO : driverWithdrawalOtherAmountSettingDOS) {
                    List<DriverWithdrawalDO> driverWithdrawalDOS = queryDriverWithdrawalList(context, settingDO.getLimitSum());
                    log.info("根据提现配置时间段查询到的提现单: driverWithdrawalDOS={}", FastJsonUtils.toJSONString(driverWithdrawalDOS));
                    BigDecimal amount = new BigDecimal(0);
                    if (CollectionUtils.isNotEmpty(driverWithdrawalDOS)) {
                        // 累加提现单金额
                        for (DriverWithdrawalDO driverWithdrawalDO : driverWithdrawalDOS) {
                            amount = amount.add(driverWithdrawalDO.getAmount());
                        }
                    }

                    // 累加本次提现金额
                    amount = amount.add(context.getAmount());
                    log.info("提现配置验证n天提现金额: amount={},limitSum={},limitAmount={}", amount, settingDO.getLimitSum(), settingDO.getLimitAmount());
                    if (amount.compareTo(settingDO.getLimitAmount()) > 0) {
                        log.warn(String.format("%s天内，提现金额不得超过%s元", settingDO.getLimitSum(), settingDO.getLimitAmount()));
                        throw new BusinessException(ResultEnum.MAXIMUM_WITHDRAWAL_AMOUNT_ERROR);
                    }
                }
            }
        }
    }

    private DriverWithdrawalSettingDO queryDriverWithdrawalSetting() {
        return driverWithdrawalSettingMapper.selectOne(new LambdaQueryWrapper<DriverWithdrawalSettingDO>()
                .eq(DriverWithdrawalSettingDO::getEnv, EnvUtil.getEnv().getValue()), false);
    }

    private List<DriverWithdrawalOtherAmountSettingDO> queryDriverWithdrawalOtherAmountSettingList(DriverWithdrawalSettingDO driverWithdrawalSettingDO) {
        return driverWithdrawalOtherAmountSettingMapper.selectList(new LambdaQueryWrapper<DriverWithdrawalOtherAmountSettingDO>()
                .eq(DriverWithdrawalOtherAmountSettingDO::getWithdrawalSettingId, driverWithdrawalSettingDO.getId()));
    }

    private List<DriverWithdrawalDO> queryDriverWithdrawalList(WithdrawalContext context, BigDecimal limitSum) {
        return driverWithdrawalMapper.selectList(new LambdaQueryWrapper<DriverWithdrawalDO>()
                .eq(DriverWithdrawalDO::getDriverId, context.getDriverId())
                .between(DriverWithdrawalDO::getCreateTime, DateUtil.getLastTime(limitSum), new Date()));
    }
}