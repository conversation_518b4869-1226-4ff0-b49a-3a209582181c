package com.ly.travel.car.carowner.biz.driver.withdrawal.validator.impl;

import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.biz.driver.bill.service.DriverBillService;
import com.ly.travel.car.carowner.biz.driver.withdrawal.model.WithdrawalContext;
import com.ly.travel.car.carowner.biz.driver.withdrawal.validator.ValidationStrategy;
import com.ly.travel.car.carowner.biz.order.model.RideOrderInfoVO;
import com.ly.travel.car.carowner.biz.order.model.RideOrderSettlementVO;
import com.ly.travel.car.carowner.biz.order.service.RideOrderInfoService;
import com.ly.travel.car.carowner.biz.settlement.service.RideOrderSettlementService;
import com.ly.travel.car.carowner.common.enums.ResultEnum;
import com.ly.travel.car.carowner.common.exception.BusinessException;
import com.ly.travel.car.carowner.dal.dataobject.DriverBillDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 提现验价
 */
@Slf4j
@Component
@Order(7)
public class ValidateDriverWithdrawPriceVerifyStrategy implements ValidationStrategy<WithdrawalContext> {

    @Resource
    private RideOrderInfoService rideOrderInfoService;

    @Resource
    private DriverBillService driverBillService;

    @Resource
    private RideOrderSettlementService orderSettlementService;

    @Override
    public void validate(WithdrawalContext context) {
        List<String> failOrderList = new ArrayList<>();
        List<String> orderNos = context.getDriverBills().stream().map(DriverBillDO::getVoucherNo).distinct().collect(Collectors.toList());
        for (String orderNo : orderNos) {
            RideOrderInfoVO rideOrderInfoVO = rideOrderInfoService.queryOrderInfo(orderNo, "");
            if (rideOrderInfoVO == null) {
                log.warn("订单不存在: orderNo={}", orderNo);
                continue;
            }

            RideOrderSettlementVO orderSettlementVO = orderSettlementService.queryByOrderNo(orderNo);
            if (orderSettlementVO == null) {
                log.warn("订单无结算信息: orderNo={},driverId={}", orderNo, rideOrderInfoVO.getDriverId());
                continue;
            }

            // 验证结算金额
            boolean isValidate = driverBillService.validateSettlementAmount(orderSettlementVO, rideOrderInfoVO);
            if (isValidate) {
                failOrderList.add(rideOrderInfoVO.getOrderNo());
            }
        }

        if (failOrderList.size() > 0) {
            log.warn("订单存在争议: failOrderList={}", FastJsonUtils.toJSONString(failOrderList));
            throw new BusinessException(ResultEnum.WITHDRAWAL_PRICE_VERIFY_ERROR);
        }
    }
}
