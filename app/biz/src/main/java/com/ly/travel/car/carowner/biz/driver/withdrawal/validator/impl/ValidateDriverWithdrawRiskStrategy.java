package com.ly.travel.car.carowner.biz.driver.withdrawal.validator.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.biz.driver.withdrawal.model.WithdrawalContext;
import com.ly.travel.car.carowner.biz.driver.withdrawal.validator.ValidationStrategy;
import com.ly.travel.car.carowner.biz.order.model.RideOrderInfoVO;
import com.ly.travel.car.carowner.biz.order.service.RideOrderInfoService;
import com.ly.travel.car.carowner.biz.risk.OrderRiskService;
import com.ly.travel.car.carowner.biz.sys.service.SysConfigService;
import com.ly.travel.car.carowner.common.constant.RiskConstant;
import com.ly.travel.car.carowner.common.entity.RvOperationLogEntity;
import com.ly.travel.car.carowner.common.enums.*;
import com.ly.travel.car.carowner.common.exception.BusinessException;
import com.ly.travel.car.carowner.common.utils.CarownerConfigCenterUtils;
import com.ly.travel.car.carowner.common.utils.EnvUtil;
import com.ly.travel.car.carowner.common.utils.EnvUtils;
import com.ly.travel.car.carowner.dal.dataobject.DriverBillDO;
import com.ly.travel.car.carowner.dal.dataobject.DriverInfoDO;
import com.ly.travel.car.carowner.dal.dataobject.VehicleInfoDO;
import com.ly.travel.car.carowner.dal.mapper.DriverInfoMapper;
import com.ly.travel.car.carowner.dal.mapper.VehicleInfoMapper;
import com.ly.travel.car.carowner.integration.client.api.risk.RiskClient;
import com.ly.travel.car.carowner.integration.client.api.risk.model.dto.CustomerVO;
import com.ly.travel.car.carowner.integration.client.api.risk.model.request.RiskApiRequest;
import com.ly.travel.car.carowner.integration.client.api.risk.model.request.SimpleCheckRequest;
import com.ly.travel.car.carowner.integration.client.api.risk.model.response.SimpleCheckResponse;
import com.ly.travel.car.carowner.integration.es.EsClient;
import com.ly.travel.car.carowner.integration.exception.IntegrationException;
import com.ly.travel.car.carowner.integration.wxwork.WxWorkAlertComponent;
import com.ly.travel.car.carowner.integration.wxwork.WxWorkTemplateEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 提现风控
 */
@Slf4j
@Component
@Order(6)
public class ValidateDriverWithdrawRiskStrategy implements ValidationStrategy<WithdrawalContext> {
    @Resource
    private RiskClient riskClient;
    @Resource
    private VehicleInfoMapper vehicleInfoMapper;
    @Resource
    private DriverInfoMapper driverInfoMapper;
    @Resource
    private SysConfigService sysConfigService;
    @Resource
    private WxWorkAlertComponent wxWorkAlertComponent;
    @Resource
    private EsClient esClient;
    @Resource
    private RideOrderInfoService rideOrderInfoService;
    @Resource
    private OrderRiskService orderRiskService;

    @Override
    public void validate(WithdrawalContext context) {
        // 提现风控
        withdrawalRisk(context);

        // 完单风控
        completeRisk(context);
    }

    private void completeRisk(WithdrawalContext context) {
        List<String> failOrderList = new ArrayList<>();
        List<DriverBillDO> driverBills = context.getDriverBills();
        List<String> orderNos = driverBills.stream().map(DriverBillDO::getVoucherNo).collect(Collectors.toList());
        for (String orderNo : orderNos) {
            RideOrderInfoVO rideOrderInfoVO = rideOrderInfoService.queryOrderInfo(orderNo, "");
            if (rideOrderInfoVO == null) {
                log.warn("订单不存在: orderNo={}", orderNo);
                continue;
            }

            boolean isCompleteRisk = orderRiskService.orderRisk(rideOrderInfoVO, RiskSceneEnum.ORDER_WITHDRAW);
            if (isCompleteRisk) {
                failOrderList.add(orderNo);
            }
        }
        if (failOrderList.size() > 0) {
            log.warn("订单存在争议: failOrderList={}", FastJsonUtils.toJSONString(failOrderList));
            throw new BusinessException(ResultEnum.WITHDRAWAL_PRICE_VERIFY_ERROR);
        }
    }

    private void withdrawalRisk(WithdrawalContext context) {
        SimpleCheckRequest request = new SimpleCheckRequest();
        request.setScene(RiskSceneEnum.DRIVER_WITHDRAW.getScene());
        CustomerVO customerVO = new CustomerVO();
        customerVO.setCustomerType(CustomerTypeEnums.DRIVER_PLATE_NUMBER.getCode());
        customerVO.setCustomerValue(findVehicleInfoByDriverId(context.getDriverId()));
        request.setCustomerVOList(Collections.singletonList(customerVO));
        RiskApiRequest riskApiRequest = new RiskApiRequest();
        riskApiRequest.setRequest(request);
        riskApiRequest.setAppId(RiskConstant.APP_ID);
        riskApiRequest.setProductLine(RiskConstant.PRODUCT_LINE);
        riskApiRequest.setTraceId(UUID.randomUUID().toString());
        try {
            SimpleCheckResponse simpleCheckResponse = riskClient.simpleCheck(riskApiRequest);
            if (simpleCheckResponse != null) {
                log.warn(simpleCheckResponse.getMsg());
                //发送群消息，增加日志
                DriverInfoDO driverInfo = driverInfoMapper.selectById(context.getDriverId());
                wxWorkAlertComponent.alert(WxWorkTemplateEnum.DRIVER_RISK_FAIL, Arrays.asList(sysConfigService.getStringCachedCfgValue(SysConfigEnum.riskNotifyUser).split(",")), driverInfo.getName(), "提现");
                rvRiskOperationLog("车主命中风控", "提现风控不通过", context.getDriverId());
                throw new BusinessException(ResultEnum.WITHDRAWAL_ACCOUNT_ERROR);
            }

        } catch (IntegrationException e) {
            log.error("司机提现风控调用失败: request={}", FastJsonUtils.toJSONString(request), e);
            throw new BusinessException(ResultEnum.SUBMIT_ERROR);
        }
    }

    private String findVehicleInfoByDriverId(Long driverId) {
        VehicleInfoDO vehicleInfoDO = vehicleInfoMapper.selectOne(new LambdaQueryWrapper<VehicleInfoDO>()
                .eq(VehicleInfoDO::getDriverId, driverId)
                .eq(VehicleInfoDO::getEnv, EnvUtil.getEnv().getValue()));
        if (Objects.nonNull(vehicleInfoDO)) {
            return vehicleInfoDO.getVehicleNo();
        }
        return "";
    }

    public void rvRiskOperationLog(String subCategory, String operation, Long driverId) {
        RvOperationLogEntity rvOperationLogEntity = new RvOperationLogEntity();
        rvOperationLogEntity.setAppUk("groundtravel.shared.mobility.carowner.mobile");
        rvOperationLogEntity.setCategory("车主风控");
        rvOperationLogEntity.setCategoryId(996L);
        rvOperationLogEntity.setSubCategory(subCategory);
        rvOperationLogEntity.setUserName("system");
        rvOperationLogEntity.setStartTime(new Date().getTime());
        rvOperationLogEntity.setDetail(operation);
        rvOperationLogEntity.setMainId(driverId.toString());
        rvOperationLogEntity.setEnv(EnvUtils.getEnv().getValue());

        esClient.save(String.join("-", CarownerConfigCenterUtils.indexPrefix, EnvUtils.getEnv().getValue(), CarownerConfigCenterUtils.table_rvoperationlog), Arrays.asList(rvOperationLogEntity), CarownerConfigCenterUtils.authentication);
    }
}
