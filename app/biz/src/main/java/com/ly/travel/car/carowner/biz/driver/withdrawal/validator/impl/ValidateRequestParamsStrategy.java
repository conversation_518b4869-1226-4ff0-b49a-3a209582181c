package com.ly.travel.car.carowner.biz.driver.withdrawal.validator.impl;

import com.ly.travel.car.carowner.biz.driver.withdrawal.model.WithdrawalContext;
import com.ly.travel.car.carowner.biz.driver.withdrawal.validator.ValidationStrategy;
import com.ly.travel.car.carowner.common.enums.ResultEnum;
import com.ly.travel.car.carowner.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 验证提现参数
 */
@Slf4j
@Component
@Order(1)
public class ValidateRequestParamsStrategy implements ValidationStrategy<WithdrawalContext> {
    @Override
    public void validate(WithdrawalContext context) {
        validateNotNull(context.getDriverWithdrawalRequest(), "入参不能为空", ResultEnum.SUBMIT_ERROR);
        validateNotNull(context.getDriverId(), "司机id不能为空", ResultEnum.SUBMIT_ERROR);
        validateAmount(context.getAmount());
    }

    private void validateAmount(BigDecimal amount) {
        validateNotNull(amount, "提现金额不能为空", ResultEnum.SUBMIT_ERROR);
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            throwValidationException("提现金额不能小于等于0", ResultEnum.WITHDRAWAL_AMOUNT_ERROR);
        }
    }

    private void validateNotNull(Object value, String logDesc, ResultEnum resultEnum) {
        if (value == null) {
            throwValidationException(logDesc, resultEnum);
        }
    }

    private void validateNotBlank(String value, String logDesc, ResultEnum resultEnum) {
        if (StringUtils.isBlank(value)) {
            throwValidationException(logDesc, resultEnum);
        }
    }

    private void throwValidationException(String logDesc, ResultEnum resultEnum) {
        log.warn(logDesc);
        throw new BusinessException(resultEnum);
    }
}