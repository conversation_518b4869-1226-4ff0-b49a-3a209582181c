/**
* PassengerOrderInfoMapping
* This file is generated by <tt>toolkit(sof-batis-gen)</tt>, a DAL (Data Access Layer)
* @version Id : PassengerOrderInfoMapping, v 0.1
*/
package com.ly.travel.car.carowner.biz.order.mapper;

import com.ly.flight.toolkit.converter.BooleanConverter;
import com.ly.flight.toolkit.converter.FullDateConverter;
import com.ly.sof.utils.page.Paginator;

import com.ly.travel.car.carowner.dal.dataobject.*;
import com.ly.travel.car.carowner.dal.operation.passengerorderinfo.*;

import com.ly.travel.car.carowner.biz.order.model.QueryPassengerOrderInfoVO;
import com.ly.travel.car.carowner.biz.order.model.PassengerOrderInfoVO;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.NullValueMappingStrategy;

import java.util.Collection;
import java.util.List;

@Mapper(componentModel = "spring", nullValueMappingStrategy = NullValueMappingStrategy.RETURN_NULL, uses = { FullDateConverter.class, BooleanConverter.class })
public interface PassengerOrderInfoMapping {

PassengerOrderInfoDO v2d(PassengerOrderInfoVO v);

PassengerOrderInfoVO d2v(PassengerOrderInfoDO d);

List<PassengerOrderInfoVO> d2v(Collection<PassengerOrderInfoDO> d);

List<PassengerOrderInfoDO> v2d(Collection<PassengerOrderInfoVO> v);

@Mappings({@Mapping(target = "page", expression = "java(page.getCurrentPage())")})
FindPageQuery v2FindPage(QueryPassengerOrderInfoVO v,Paginator page);

@Mappings({@Mapping(target = "page", expression = "java(page.getCurrentPage())")})
FindPageQuery v2FindPage(PassengerOrderInfoVO v,Paginator page);

QueryAllQuery v2QueryAllQuery(QueryPassengerOrderInfoVO v);
}