/**
* RideOrderInfoMapping
* This file is generated by <tt>toolkit(sof-batis-gen)</tt>, a DAL (Data Access Layer)
* @version Id : RideOrderInfoMapping, v 0.1
*/
package com.ly.travel.car.carowner.biz.order.mapper;

import com.ly.flight.toolkit.converter.BooleanConverter;
import com.ly.flight.toolkit.converter.FullDateConverter;
import com.ly.sof.utils.page.Paginator;

import com.ly.travel.car.carowner.dal.dataobject.*;
import com.ly.travel.car.carowner.dal.operation.rideorderinfo.*;

import com.ly.travel.car.carowner.biz.order.model.QueryRideOrderInfoVO;
import com.ly.travel.car.carowner.biz.order.model.RideOrderInfoVO;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.NullValueMappingStrategy;

import java.util.Collection;
import java.util.List;

@Mapper(componentModel = "spring", nullValueMappingStrategy = NullValueMappingStrategy.RETURN_NULL)
public interface RideOrderInfoMapping {

RideOrderInfoDO v2d(RideOrderInfoVO v);

RideOrderInfoVO d2v(RideOrderInfoDO d);

List<RideOrderInfoVO> d2v(Collection<RideOrderInfoDO> d);

List<RideOrderInfoDO> v2d(Collection<RideOrderInfoVO> v);

@Mappings({@Mapping(target = "page", expression = "java(page.getCurrentPage())")})
FindPageQuery v2FindPage(QueryRideOrderInfoVO v,Paginator page);

@Mappings({@Mapping(target = "page", expression = "java(page.getCurrentPage())")})
FindPageQuery v2FindPage(RideOrderInfoVO v,Paginator page);

QueryAllQuery v2QueryAllQuery(QueryRideOrderInfoVO v);
}
