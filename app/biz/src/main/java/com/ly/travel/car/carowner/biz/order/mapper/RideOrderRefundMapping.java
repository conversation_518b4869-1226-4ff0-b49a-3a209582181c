/**
* RideOrderRefundMapping
* This file is generated by <tt>toolkit(sof-batis-gen)</tt>, a DAL (Data Access Layer)
* @version Id : RideOrderRefundMapping, v 0.1
*/
package com.ly.travel.car.carowner.biz.order.mapper;

import com.ly.flight.toolkit.converter.BooleanConverter;
import com.ly.flight.toolkit.converter.FullDateConverter;
import com.ly.sof.utils.page.Paginator;

import com.ly.travel.car.carowner.dal.dataobject.*;
import com.ly.travel.car.carowner.dal.operation.rideorderrefund.*;

import com.ly.travel.car.carowner.biz.order.model.RideOrderRefundVO;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.NullValueMappingStrategy;

import java.util.Collection;
import java.util.List;

@Mapper(componentModel = "spring", nullValueMappingStrategy = NullValueMappingStrategy.RETURN_NULL, uses = { FullDateConverter.class, BooleanConverter.class })
public interface RideOrderRefundMapping {

RideOrderRefundDO v2d(RideOrderRefundVO v);

RideOrderRefundVO d2v(RideOrderRefundDO d);

List<RideOrderRefundVO> d2v(Collection<RideOrderRefundDO> d);

List<RideOrderRefundDO> v2d(Collection<RideOrderRefundVO> v);

}
