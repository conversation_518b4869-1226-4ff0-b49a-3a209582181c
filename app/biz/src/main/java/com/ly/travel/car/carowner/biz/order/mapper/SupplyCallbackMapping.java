package com.ly.travel.car.carowner.biz.order.mapper;

import com.ly.flight.toolkit.converter.BooleanConverter;
import com.ly.flight.toolkit.converter.FullDateConverter;
import com.ly.travel.car.carowner.biz.takeorder.model.GrabOrderStoreModel;
import com.ly.travel.car.carowner.facade.request.callback.AcceptOrderRequest;
import com.ly.travel.car.carowner.facade.request.callback.CancelOrderRequest;
import com.ly.travel.car.carowner.facade.request.callback.DriverTripRequest;
import com.ly.travel.car.carowner.facade.request.callback.FinishOrderRequest;
import com.ly.travel.car.carowner.integration.client.api.supplychain.request.CallbackRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueMappingStrategy;

/**
 * <AUTHOR>
 * @desc SupplyCallbackMapping
 */
@Mapper(componentModel = "spring", nullValueMappingStrategy = NullValueMappingStrategy.RETURN_NULL, uses = { FullDateConverter.class, BooleanConverter.class })
public interface SupplyCallbackMapping {

    CallbackRequest convert(AcceptOrderRequest request);

    CallbackRequest convert(CancelOrderRequest request);

    @Mapping(target = "status", source = "tripType")
    CallbackRequest convert(DriverTripRequest request);

    CallbackRequest convert(FinishOrderRequest request);

    GrabOrderStoreModel convertGrabOrder(AcceptOrderRequest request);
}
