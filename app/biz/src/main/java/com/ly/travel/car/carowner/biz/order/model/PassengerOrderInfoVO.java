/**
* PassengerOrderInfoVO
* This file is generated by <tt>toolkit(sof-batis-gen)</tt>, a DAL (Data Access Layer)
* @version Id : PassengerOrderInfoVO, v 0.1
*/
package com.ly.travel.car.carowner.biz.order.model;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import com.ly.flight.toolkit.pojo.BaseFormVO;

import com.ly.flight.toolkit.annoations.Diff;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ly.flight.toolkit.converter.DateTimeJsonSerializer;
import com.ly.flight.toolkit.converter.JsonDateDeserializer;
import lombok.EqualsAndHashCode;

@Data

public class PassengerOrderInfoVO {

    private String env;
    /** 订单号 */
    private String              orderNo;

    /** 分销商平台订单号 */
    private String              distributorOrderNo;

    /** 供应链主订单号 */
    private String              distributorMainOrderNo;

    /** 出发城市id */
    private Long              startCityId;

    /** 到达城市id */
    private Long              endCityId;

    /** 出发城市 */
    private String              startCity;

    /** 到达城市 */
    private String              endCity;

    /** 出发城市行政区code */
    private String              startDistrictCode;

    /** 出发城市行政区 */
    private String              startDistrict;

    /** 目的城市行政区code */
    private String              endDistrictCode;

    /** 目的城市行政区 */
    private String              endDistrict;

    /** 出发地点 */
    private String              startingAdd;

    /** 目的地点 */
    private String              endingAdd;

    /** 出发点经度 */
    private BigDecimal              startingLon;

    /** 出发点纬度 */
    private BigDecimal              startingLat;

    /** 目的点经度 */
    private BigDecimal              endingLon;

    /** 目的点纬度 */
    private BigDecimal              endingLat;

    /** 用车时间 */
                @JsonSerialize(using = DateTimeJsonSerializer.class)
        @JsonDeserialize(using = JsonDateDeserializer.class)

    private Date              startingTime;

    /** 最晚用车时间 */
                @JsonSerialize(using = DateTimeJsonSerializer.class)
        @JsonDeserialize(using = JsonDateDeserializer.class)

    private Date              lastStartingTime;

    /** 乘客联系电号 */
    private String              telephone;

    /** 真实号码 */
    private String              realTelephone;

    /** 数量 */
    private Integer              passengerCount;

    /** 订单金额 */
    private BigDecimal amount;

    /** 付款金额 */
    private BigDecimal              payPrice;

    /** 订单状态  -1已删除   1.已下单      2.已取消   3.已接单 */
    private Integer              status;

    /** 支付状态  0未支付   1 已支付  2部分支付  3已退款  */
    private Integer              payStatus;

    /** 支付类型  1普通  2信用分 */
    private Integer              payType;

    /** refId */
    private String              refId;

    /** 订单类型：1、包车； 2、拼车； */
    private Integer              orderType;

    /** 用户会员id */
    private Long              memberId;

    /** 创建人 */
    private String              createUser;

    /** 创建时间 */
                @JsonSerialize(using = DateTimeJsonSerializer.class)
        @JsonDeserialize(using = JsonDateDeserializer.class)
    private Date              createTime;

    /** 修改人 */
    private String              updateUser;

    /** 修改时间 */
                @JsonSerialize(using = DateTimeJsonSerializer.class)
        @JsonDeserialize(using = JsonDateDeserializer.class)
    private Date              updateTime;

    /** 备注 */
    private String              remark;

    /** 出发详细地址 */
    private String              startingAddDetail;

    /** 到达详细地址 */
    private String              endingAddDetail;

    /** 总里程数(单位：千米） */
    private BigDecimal              distance;

    /** 时长，分钟单位 */
    private Integer              duration;

    /** 随单分佣比例0-1之间 */
    private BigDecimal              orderCommissionRatio;

    /** 随单分佣上限(单位元) */
    private BigDecimal              orderCommissionCap;

    /** 佣金类型：0比例，1定额 */
    private Integer              commissionType;
    private Integer              source;
    private Date payTime;
}
