/**
* PassengerOrderInfoVO
* This file is generated by <tt>toolkit(sof-batis-gen)</tt>, a DAL (Data Access Layer)
* @version Id : PassengerOrderInfoVO, v 0.1
*/
package com.ly.travel.car.carowner.biz.order.model;

import java.util.Date;
import lombok.Data;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ly.flight.toolkit.converter.DateTimeJsonSerializer;
import com.ly.flight.toolkit.converter.JsonDateDeserializer;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper=true)
@Data
public class QueryPassengerOrderInfoVO extends PassengerOrderInfoVO{
                                                                                                                                                                                                                                                                                                                
        /** 用车时间 时间开始*/
        private String              startingTimeStart;

        /** 用车时间 时间结束*/
        private String              startingTimeEnd;
                        
        /** 最晚用车时间 时间开始*/
        private String              lastStartingTimeStart;

        /** 最晚用车时间 时间结束*/
        private String              lastStartingTimeEnd;
                                                                                                                                                                                                                        
        /** 创建时间 时间开始*/
        private String              createTimeStart;

        /** 创建时间 时间结束*/
        private String              createTimeEnd;
                                        
        /** 修改时间 时间开始*/
        private String              updateTimeStart;

        /** 修改时间 时间结束*/
        private String              updateTimeEnd;
                                                                                                                                            
}