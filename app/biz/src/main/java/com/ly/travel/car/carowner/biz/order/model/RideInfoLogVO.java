/**
* RideInfoLogVO
* This file is generated by <tt>toolkit(sof-batis-gen)</tt>, a DAL (Data Access Layer)
* @version Id : RideInfoLogVO, v 0.1
*/
package com.ly.travel.car.carowner.biz.order.model;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ly.travel.car.carowner.common.enums.OrderLogTypeEnum;
import lombok.Data;
import com.ly.flight.toolkit.pojo.BaseFormVO;

import com.ly.flight.toolkit.annoations.Diff;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ly.flight.toolkit.converter.DateTimeJsonSerializer;
import com.ly.flight.toolkit.converter.JsonDateDeserializer;
import lombok.EqualsAndHashCode;

@Data
public class RideInfoLogVO   {
    private String orderNo;

    private String createUser;


    private int status;

    private String remark;

    private OrderLogTypeEnum logType;
    private int logId;
}
