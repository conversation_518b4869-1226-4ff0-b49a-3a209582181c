/**
* RideOrderInfoVO
* This file is generated by <tt>toolkit(sof-batis-gen)</tt>, a DAL (Data Access Layer)
* @version Id : RideOrderInfoVO, v 0.1
*/
package com.ly.travel.car.carowner.biz.order.model;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import com.ly.flight.toolkit.pojo.BaseFormVO;

import com.ly.flight.toolkit.annoations.Diff;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ly.flight.toolkit.converter.DateTimeJsonSerializer;
import com.ly.flight.toolkit.converter.JsonDateDeserializer;
import lombok.EqualsAndHashCode;

@Data
@Diff(name = "RideOrderInfoVO")
public class RideOrderInfoVO  {

    private Long id;

    private String env;

    /** 订单号 */
    private String              orderNo;

    /** 供应链订单号 */
    private String              distributorOrderNo;

    /** 供应链主订单号 */
    private String              distributorMainOrderNo;

    /** 司机行程号 */
    private String              driverTripNo;

    /** 订单来源 0:未知 1:同程C端 */
    private Integer              source;

    /** 顺路度 */
    private Integer              hitchPercent;

    /** 出发城市id */
    private Long              startCityId;

    /** 到达城市id */
    private Long              endCityId;

    /** 出发城市 */
    private String              startCity;

    /** 到达城市 */
    private String              endCity;

    /** 出发城市行政区code */
    private String              startDistrictCode;

    /** 出发城市行政区 */
    private String              startDistrict;

    /** 目的城市行政区code */
    private String              endDistrictCode;

    /** 目的城市行政区 */
    private String              endDistrict;

    /** 出发地点 */
    private String              startingAdd;

    /** 目的地点 */
    private String              endingAdd;

    /** 出发点经度 */
    private BigDecimal              startingLon;

    /** 出发点纬度 */
    private BigDecimal              startingLat;

    /** 目的点经度 */
    private BigDecimal              endingLon;

    /** 目的点纬度 */
    private BigDecimal              endingLat;

    /** 用车时间 */
                @JsonSerialize(using = DateTimeJsonSerializer.class)
        @JsonDeserialize(using = JsonDateDeserializer.class)
    private Date              startingTime;

    /** 最晚用车时间 */
                @JsonSerialize(using = DateTimeJsonSerializer.class)
        @JsonDeserialize(using = JsonDateDeserializer.class)
    private Date              lastStartingTime;

    /** 乘客联系电号 */
    private String              telephone;

    /** 真实号码 */
    private String              realTelephone;

    /** 数量 */
    private Integer              passengerCount;

    /** 订单金额 */
    private BigDecimal              amount;

    /** 付款金额 */
    private BigDecimal              payPrice;

    /** 订单状态  -1已删除   1.抢单中(c端决策之前)  2.已派车  3已完成  4已取消 */
    private Integer              status;

    /** 支付状态  0未支付   1 已支付  2部分支付  3已退款  4部分退款 */
    private Integer              payStatus;

    /** 支付类型  1普通  2信用分 */
    private Integer              payType;

    /** refId */
    private String              refId;

    /** 订单类型：1、包车； 2、拼车； */
    private Integer              orderType;

    /** 用户id */
    private Long              memberId;

    /** 车牌 */
    private String              vehicleNo;

    /** 司机姓名 */
    private String              driverName;

    /** 司机电话 */
    private String              driverMobile;

    /** 司机id */
    private Long              driverId;

    /** 下单时间 */
                @JsonSerialize(using = DateTimeJsonSerializer.class)
        @JsonDeserialize(using = JsonDateDeserializer.class)
    private Date              bookTime;

    /** 创建人 */
    private String              createUser;

    /** 创建时间 */
                @JsonSerialize(using = DateTimeJsonSerializer.class)
        @JsonDeserialize(using = JsonDateDeserializer.class)
    private Date              createTime;

    /** 修改人 */
    private String              updateUser;

    /** 修改时间 */
    @JsonSerialize(using = DateTimeJsonSerializer.class)
    @JsonDeserialize(using = JsonDateDeserializer.class)
    private Date              updateTime;

    /** 备注 */
    private String              remark;

    /** 出发详细地址 */
    private String              startingAddDetail;

    /** 到达详细地址 */
    private String              endingAddDetail;

    /** 总里程数(单位：千米） */
    private BigDecimal distance;

    private Integer              duration;

    private BigDecimal              orderCommissionRatio;

    private BigDecimal              orderCommissionCap;

    @JsonSerialize(using = DateTimeJsonSerializer.class)
    @JsonDeserialize(using = JsonDateDeserializer.class)
    private Date payTime;

    @JsonSerialize(using = DateTimeJsonSerializer.class)
    @JsonDeserialize(using = JsonDateDeserializer.class)
    private Date finishTime;
}
