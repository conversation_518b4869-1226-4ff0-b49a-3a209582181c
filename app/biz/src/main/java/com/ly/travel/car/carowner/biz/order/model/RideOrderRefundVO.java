/**
* RideOrderRefundVO
* This file is generated by <tt>toolkit(sof-batis-gen)</tt>, a DAL (Data Access Layer)
* @version Id : RideOrderRefundVO, v 0.1
*/
package com.ly.travel.car.carowner.biz.order.model;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import com.ly.flight.toolkit.pojo.BaseFormVO;

import com.ly.flight.toolkit.annoations.Diff;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ly.flight.toolkit.converter.DateTimeJsonSerializer;
import com.ly.flight.toolkit.converter.JsonDateDeserializer;
import lombok.EqualsAndHashCode;

@Data
@Diff(name = "RideOrderRefundVO")
@ColumnWidth(value = 15)
@ExcelIgnoreUnannotated
@EqualsAndHashCode(callSuper=true)
public class RideOrderRefundVO extends BaseFormVO {

    /** 订单编号 */
            @Diff(name = "订单编号")
    @ExcelProperty(value = "订单编号")
    private String              orderNo;

    /** 退款金额 */
            @Diff(name = "退款金额")
    @ExcelProperty(value = "退款金额")
    private BigDecimal refundAmount;

    /** 退款状态- 0:退款, 1:退款成功, 2:退款失败 */
            @Diff(name = "退款状态- 0:退款, 1:退款成功, 2:退款失败")
    @ExcelProperty(value = "退款状态- 0:退款, 1:退款成功, 2:退款失败")
    private Integer              refundStatus;

    /** 退款原因备注 */
            @Diff(name = "退款原因备注")
    @ExcelProperty(value = "退款原因备注")
    private String              refundRemark;

    /** 退款时间 */
                @JsonSerialize(using = DateTimeJsonSerializer.class)
        @JsonDeserialize(using = JsonDateDeserializer.class)
            @Diff(name = "退款时间")
    @ExcelProperty(value = "退款时间")
    private Date              refundTime;

    /** 创建人 */
            @Diff(name = "创建人")
    @ExcelProperty(value = "创建人")
    private String              createUser;

    /** 创建时间 */
                @JsonSerialize(using = DateTimeJsonSerializer.class)
        @JsonDeserialize(using = JsonDateDeserializer.class)
            @Diff(name = "创建时间")
    @ExcelProperty(value = "创建时间")
    private Date              createTime;

    /** 备注 */
            @Diff(name = "备注")
    @ExcelProperty(value = "备注")
    private String              remark;

}
