/**
 * RideOrderSettlementVO
 * This file is generated by <tt>toolkit(sof-batis-gen)</tt>, a DAL (Data Access Layer)
 *
 * @version Id : RideOrderSettlementVO, v 0.1
 */
package com.ly.travel.car.carowner.biz.order.model;

import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;
import com.ly.flight.toolkit.pojo.BaseFormVO;

import com.ly.flight.toolkit.annoations.Diff;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ly.flight.toolkit.converter.DateTimeJsonSerializer;
import com.ly.flight.toolkit.converter.JsonDateDeserializer;
import lombok.EqualsAndHashCode;

@Data
public class RideOrderSettlementVO  {
    private Long id;

    /** 订单号 */
    private String orderNo;

    /** 司机ID */
    private Long driverId;

    /** 供应商结算价 */
    private BigDecimal supplierSettlementPrice;

    /** 平台结算收益 */
    private BigDecimal platformSettlementPrice;

    /** 供应商分佣比例 */
    private BigDecimal supplierMaidRate;

    /** 平台分佣比例 */
    private BigDecimal platformMaidRate;

    /** 司机结算价 */
    private BigDecimal driverSettlementPrice;


    private  BigDecimal supplierLimitMaxAmount;

    private BigDecimal oldDriverSettlementPrice;
    private  BigDecimal driverExtraSettlementPrice;


}
