/**
 * PassengerOrderInfoService
 * This file is generated by <tt>toolkit(sof-batis-gen)</tt>, a DAL (Data Access Layer)
 *
 * @version Id : PassengerOrderInfoService, v 0.1
 */
package com.ly.travel.car.carowner.biz.order.service;

import com.ly.flight.toolkit.service.FormService;

import java.util.List;

import com.ly.sof.utils.page.PageList;
import com.ly.sof.utils.page.Paginator;

import com.ly.travel.car.carowner.biz.order.model.PassengerOrderInfoVO;
import com.ly.travel.car.carowner.biz.order.model.QueryPassengerOrderInfoVO;

public interface PassengerOrderInfoService {


    List<PassengerOrderInfoVO> queryByOrderNoList(List<String> orderNos)  ;
    PassengerOrderInfoVO queryByOrderNo(String orderNo)  ;

    /**
     * 禁用使用，全表更新
     * @param form
     * @return
     */
    boolean updatePassengerOrder(PassengerOrderInfoVO form);

    boolean updatePassengerOrderStatus(String orderNo,Integer status,String updateMethod);

    }
