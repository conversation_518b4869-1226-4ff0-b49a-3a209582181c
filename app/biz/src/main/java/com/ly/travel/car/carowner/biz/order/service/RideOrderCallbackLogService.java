package com.ly.travel.car.carowner.biz.order.service;

import com.ly.travel.car.carowner.dal.dataobject.RideOrderCallbackLogDO;

import java.util.List;

/**
 * <AUTHOR>
 * @desc RideOrderCallbackLogService
 */
public interface RideOrderCallbackLogService {

    int insert(RideOrderCallbackLogDO rideOrderCallbackLogDO);

    int update(RideOrderCallbackLogDO rideOrderCallbackLogDO);

    List<RideOrderCallbackLogDO> listByOrderNoAndType(String orderNo, Integer callbackType);
}
