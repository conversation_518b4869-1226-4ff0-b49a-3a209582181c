/**
 * RideOrderInfoService
 * This file is generated by <tt>toolkit(sof-batis-gen)</tt>, a DAL (Data Access Layer)
 *
 * @version Id : RideOrderInfoService, v 0.1
 */
package com.ly.travel.car.carowner.biz.order.service;

import java.util.Date;
import java.util.List;

import com.ly.travel.car.carowner.biz.order.model.RideOrderInfoVO;

public interface RideOrderInfoService {

    int saveRideOrder(RideOrderInfoVO form);

    boolean updateRideOrder(RideOrderInfoVO form);

    RideOrderInfoVO queryOrderInfo(String orderNo,String distributorOrderNo);

    List<RideOrderInfoVO> queryOrderInfoByOrderNos(List<String> orderNos);

    /**
     * 获取用车时间内完单数量
     * @param driverId
     * @param startDate
     * @param endDate
     * @return
     */
    long queryFinishOrderCount(Long driverId, Date startDate, Date endDate);

    RideOrderInfoVO queryOrderInfo(String distributorMainOrderNo);
}
