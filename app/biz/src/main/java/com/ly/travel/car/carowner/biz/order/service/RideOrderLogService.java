/**
* RideInfoLogService
* This file is generated by <tt>toolkit(sof-batis-gen)</tt>, a DAL (Data Access Layer)
* @version Id : RideInfoLogService, v 0.1
*/
package com.ly.travel.car.carowner.biz.order.service;

import com.ly.flight.toolkit.service.FormService;
import java.util.List;

import com.ly.sof.utils.page.PageList;
import com.ly.sof.utils.page.Paginator;

import com.ly.travel.car.carowner.biz.order.model.RideInfoLogVO;
import com.ly.travel.car.carowner.common.enums.OrderLogTypeEnum;

public interface RideOrderLogService {
     void addLog(String orderNo, OrderLogTypeEnum logType, String remark, String createUser) ;
     void addLog(String orderNo, int logType,String logTypeName, String remark, String createUser) ;

}
