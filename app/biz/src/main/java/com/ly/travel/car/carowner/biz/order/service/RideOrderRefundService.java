/**
 * RideOrderRefundService
 * This file is generated by <tt>toolkit(sof-batis-gen)</tt>, a DAL (Data Access Layer)
 *
 * @version Id : RideOrderRefundService, v 0.1
 */
package com.ly.travel.car.carowner.biz.order.service;

import java.math.BigDecimal;
import java.util.List;

import com.ly.travel.car.carowner.biz.order.model.RideOrderRefundVO;

public interface RideOrderRefundService {

    List<RideOrderRefundVO> queryRefundByOrderNo(String orderNo);
    BigDecimal queryRefundAmountByOrderNo(String orderNo);


}
