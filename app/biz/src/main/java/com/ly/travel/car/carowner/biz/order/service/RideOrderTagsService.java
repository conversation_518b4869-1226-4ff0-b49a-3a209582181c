/**
* RideInfoLogService
* This file is generated by <tt>toolkit(sof-batis-gen)</tt>, a DAL (Data Access Layer)
* @version Id : RideInfoLogService, v 0.1
*/
package com.ly.travel.car.carowner.biz.order.service;

import com.ly.travel.car.carowner.common.enums.OrderLogTypeEnum;

import java.util.List;

public interface RideOrderTagsService {
     void addTag(String orderNo, List<String>  tags,Long orderId);
     void addTag(String orderNo, String  tag,Long orderId);
     void removeTag(String orderNo, String tag,Long orderId);
     List<String> queryOrderTag(String orderNo);
     boolean existsOrderTag(String orderNo,String tag);
}
