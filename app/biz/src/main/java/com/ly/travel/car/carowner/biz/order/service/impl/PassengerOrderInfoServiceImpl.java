/**
 * PassengerOrderInfoServiceImpl
 * This file is generated by <tt>toolkit(sof-batis-gen)</tt>, a DAL (Data Access Layer)
 *
 * @version Id : PassengerOrderInfoServiceImpl, v 0.1
 */
package com.ly.travel.car.carowner.biz.order.service.impl;

import java.io.IOException;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.UpdateChainWrapper;
import com.ly.sof.utils.page.PageList;
import com.ly.sof.utils.page.Paginator;
import com.google.common.collect.Lists;
import com.ly.travel.car.carowner.dal.mapper.PassengerOrderInfoMapper;
import org.springframework.stereotype.Service;

import com.ly.travel.car.carowner.dal.dataobject.*;
import com.ly.travel.car.carowner.dal.operation.passengerorderinfo.*;

import com.ly.travel.car.carowner.biz.order.model.PassengerOrderInfoVO;
import com.ly.travel.car.carowner.biz.order.model.QueryPassengerOrderInfoVO;
import com.ly.travel.car.carowner.biz.order.service.PassengerOrderInfoService;
import com.ly.travel.car.carowner.biz.order.mapper.PassengerOrderInfoMapping;

import com.ly.flight.toolkit.service.BaseFormService;

@Service
public class PassengerOrderInfoServiceImpl implements PassengerOrderInfoService {

    @Resource
    private PassengerOrderInfoMapping mapping;

    @Resource
    private PassengerOrderInfoMapper passengerOrderInfoMapper;

    @Override
    public List<PassengerOrderInfoVO> queryByOrderNoList(List<String> orderNo)   {
        return mapping.d2v(passengerOrderInfoMapper.selectList(new QueryWrapper<PassengerOrderInfoDO>().in("order_no",orderNo)));
    }

    @Override
    public PassengerOrderInfoVO queryByOrderNo(String orderNo) {
        return mapping.d2v(passengerOrderInfoMapper.selectOne(new QueryWrapper<PassengerOrderInfoDO>().eq("order_no",orderNo),false));
    }

    @Override
    public boolean updatePassengerOrder(PassengerOrderInfoVO passengerOrderInfoVO) {
        PassengerOrderInfoDO passengerOrderInfoDO = mapping.v2d(passengerOrderInfoVO);
        return passengerOrderInfoMapper.update(passengerOrderInfoDO, new LambdaQueryWrapper<PassengerOrderInfoDO>().eq(PassengerOrderInfoDO::getOrderNo, passengerOrderInfoVO.getOrderNo())) > 0;
    }

    @Override
    public boolean updatePassengerOrderStatus(String orderNo,Integer status,String updateMethod) {

        return passengerOrderInfoMapper.update(new LambdaUpdateWrapper<PassengerOrderInfoDO>()
                .set(PassengerOrderInfoDO::getStatus,status)
                .set(PassengerOrderInfoDO::getUpdateTime,new Date())
                .set(PassengerOrderInfoDO::getUpdateUser,updateMethod)
                .eq(PassengerOrderInfoDO::getOrderNo,orderNo)) > 0;
    }
}
