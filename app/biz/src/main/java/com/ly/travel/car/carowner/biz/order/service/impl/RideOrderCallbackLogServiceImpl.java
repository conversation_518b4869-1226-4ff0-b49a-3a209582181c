package com.ly.travel.car.carowner.biz.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.travel.car.carowner.biz.order.service.RideOrderCallbackLogService;
import com.ly.travel.car.carowner.common.utils.EnvUtil;
import com.ly.travel.car.carowner.dal.dataobject.RideOrderCallbackLogDO;
import com.ly.travel.car.carowner.dal.mapper.RideOrderCallbackLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @desc RideOrderCallbackLogService
 */
@Slf4j
@Service
public class RideOrderCallbackLogServiceImpl implements RideOrderCallbackLogService {

    @Resource
    private RideOrderCallbackLogMapper rideOrderCallbackLogMapper;

    @Override
    public int insert(RideOrderCallbackLogDO rideOrderCallbackLogDO) {
        return rideOrderCallbackLogMapper.insert(rideOrderCallbackLogDO);
    }

    @Override
    public int update(RideOrderCallbackLogDO rideOrderCallbackLogDO) {
        return rideOrderCallbackLogMapper.updateById(rideOrderCallbackLogDO);
    }

    @Override
    public List<RideOrderCallbackLogDO> listByOrderNoAndType(String orderNo, Integer callbackType) {
        List<RideOrderCallbackLogDO> rideOrderCallbackLogDOS = rideOrderCallbackLogMapper.selectList(new QueryWrapper<RideOrderCallbackLogDO>()
                .eq("order_no", orderNo).ne("callback_type", callbackType).eq("env", EnvUtil.getEnv()).orderByDesc("create_time"));
        return rideOrderCallbackLogDOS;
    }
}
