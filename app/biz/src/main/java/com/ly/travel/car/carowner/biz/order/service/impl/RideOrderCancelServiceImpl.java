package com.ly.travel.car.carowner.biz.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.travel.car.carowner.biz.order.service.RideOrderCancelService;
import com.ly.travel.car.carowner.dal.dataobject.RideOrderCancelDO;
import com.ly.travel.car.carowner.dal.mapper.RideOrderCancelMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @desc RideOrderCancelService
 */
@Slf4j
@Service
public class RideOrderCancelServiceImpl implements RideOrderCancelService {

    @Resource
    private RideOrderCancelMapper rideOrderCancelMapper;

    @Override
    public int insert(RideOrderCancelDO rideOrderCancelDO) {
        return rideOrderCancelMapper.insert(rideOrderCancelDO);
    }

    @Override
    public List<RideOrderCancelDO> listByOrderNoAndCancelType(String orderNo, Integer cancelType) {
        List<RideOrderCancelDO> rideOrderCancelDOS = rideOrderCancelMapper.selectList(new QueryWrapper<RideOrderCancelDO>()
                .eq("order_no", orderNo).eq("cancel_type", cancelType).orderByDesc("create_time"));
        return rideOrderCancelDOS;
    }
}
