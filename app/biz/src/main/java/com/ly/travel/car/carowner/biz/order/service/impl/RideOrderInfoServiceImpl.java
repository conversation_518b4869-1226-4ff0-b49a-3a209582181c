/**
 * RideOrderInfoServiceImpl
 * This file is generated by <tt>toolkit(sof-batis-gen)</tt>, a DAL (Data Access Layer)
 *
 * @version Id : RideOrderInfoServiceImpl, v 0.1
 */
package com.ly.travel.car.carowner.biz.order.service.impl;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.travel.car.carowner.common.enums.OrderStatusEnum;
import com.ly.travel.car.carowner.common.exception.BaseException;
import com.ly.travel.car.carowner.common.utils.EnvUtil;
import com.ly.travel.car.carowner.dal.mapper.RideOrderInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ly.travel.car.carowner.dal.dataobject.*;

import com.ly.travel.car.carowner.biz.order.model.RideOrderInfoVO;
import com.ly.travel.car.carowner.biz.order.service.RideOrderInfoService;
import com.ly.travel.car.carowner.biz.order.mapper.RideOrderInfoMapping;

import org.springframework.util.StringUtils;
@Slf4j
@Service
public class RideOrderInfoServiceImpl implements RideOrderInfoService {

    @Resource
    private RideOrderInfoMapping mapping;
    @Autowired
    private RideOrderInfoMapper rideOrderInfoMapper;


    @Override
    public int saveRideOrder(RideOrderInfoVO form) {

        return rideOrderInfoMapper.insert(mapping.v2d(form)) ;
    }

    @Override
    public boolean updateRideOrder(RideOrderInfoVO form) {

        RideOrderInfoDO rideOrderInfoDO = mapping.v2d(form);

        return rideOrderInfoMapper.update(rideOrderInfoDO, new LambdaQueryWrapper<RideOrderInfoDO>().eq(RideOrderInfoDO::getOrderNo, form.getOrderNo())) > 0;

    }

    @Override
    public RideOrderInfoVO queryOrderInfo(String orderNo,String distributorOrderNo){
        if(!StringUtils.hasText(orderNo)&&!StringUtils.hasText(distributorOrderNo)){
            throw new BaseException("555","必需传入一个订单号");
        }
        RideOrderInfoDO rideOrderInfoDO = rideOrderInfoMapper.selectOne(new LambdaQueryWrapper<RideOrderInfoDO>()
                .eq(StringUtils.hasText(orderNo),RideOrderInfoDO::getOrderNo, orderNo)
                .eq(StringUtils.hasText(distributorOrderNo),RideOrderInfoDO::getDistributorOrderNo, distributorOrderNo), false);
        return mapping.d2v(rideOrderInfoDO);
    }

    @Override
    public RideOrderInfoVO queryOrderInfo(String distributorMainOrderNo) {
        if (!StringUtils.hasText(distributorMainOrderNo)) {
            throw new BaseException("555", "必需传入一个订单号");
        }
        RideOrderInfoDO rideOrderInfoDO = rideOrderInfoMapper.selectOne(new LambdaQueryWrapper<RideOrderInfoDO>()
                .eq(RideOrderInfoDO::getDistributorMainOrderNo, distributorMainOrderNo)
                .eq(RideOrderInfoDO::getStatus, OrderStatusEnum.COMPLETED.getStatus())
                .last("limit 1"), false);
        return mapping.d2v(rideOrderInfoDO);
    }

    @Override
    public List<RideOrderInfoVO> queryOrderInfoByOrderNos(List<String> orderNos) {
        List<RideOrderInfoDO> rideOrderInfoDO = rideOrderInfoMapper.selectList(new LambdaQueryWrapper<RideOrderInfoDO>().in(RideOrderInfoDO::getOrderNo, orderNos));
        return mapping.d2v(rideOrderInfoDO);
    }

    @Override
    public long queryFinishOrderCount(Long driverId, Date startDate, Date endDate) {
        LambdaQueryWrapper<RideOrderInfoDO> queryWrapper = new LambdaQueryWrapper<RideOrderInfoDO>();
        queryWrapper.eq(RideOrderInfoDO::getDriverId, driverId);
        queryWrapper.eq(RideOrderInfoDO::getStatus, OrderStatusEnum.COMPLETED.getStatus());
        queryWrapper.eq(RideOrderInfoDO::getEnv, EnvUtil.getEnv().getValue());
        queryWrapper.ge(RideOrderInfoDO::getFinishTime, startDate);
        queryWrapper.le(RideOrderInfoDO::getFinishTime, endDate);
        return rideOrderInfoMapper.selectCount(queryWrapper);
    }
}



