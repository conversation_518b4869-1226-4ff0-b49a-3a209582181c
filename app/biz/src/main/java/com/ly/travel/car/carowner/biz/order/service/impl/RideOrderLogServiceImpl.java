package com.ly.travel.car.carowner.biz.order.service.impl;
import java.util.Date;

import com.alibaba.fastjson.JSON;
import com.ly.travel.car.carowner.biz.order.service.RideOrderLogService;
import com.ly.travel.car.carowner.common.enums.OrderLogTypeEnum;
import com.ly.travel.car.carowner.dal.dataobject.RideOrderLogDO;
import com.ly.travel.car.carowner.dal.mapper.RideOrderLogMapper;
import com.tcelong.hubble.probe.util.threadlocal.ApmTraceUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class RideOrderLogServiceImpl implements RideOrderLogService {
    @Resource
    private RideOrderLogMapper rideOrderLogMapper;
    @Override
    public void addLog(String orderNo, OrderLogTypeEnum logType,String remark,String createUser) {
        RideOrderLogDO orderLog=new RideOrderLogDO();
        orderLog.setOrderNo(orderNo);
        orderLog.setCreateUser(createUser);
        orderLog.setCreateTime(new Date());
        orderLog.setStatus(0);
        orderLog.setRemark(remark);
        orderLog.setLogType(logType.getType());
        orderLog.setLogTypeName(logType.getName());
        orderLog.setLogId(ApmTraceUtils.getTraceId());
        try {
            rideOrderLogMapper.insert(orderLog);
        }catch (Exception ex){
            log.error("插入订单日志失败",ex);
        }
        finally {
            log.info("插入订单日志,{}", JSON.toJSONString(orderLog));
        }
    }

    @Override
    public void addLog(String orderNo, int logType, String logTypeName, String remark, String createUser) {
        RideOrderLogDO orderLog=new RideOrderLogDO();
        orderLog.setOrderNo(orderNo);
        orderLog.setCreateUser(createUser);
        orderLog.setCreateTime(new Date());
        orderLog.setStatus(0);
        orderLog.setRemark(remark);
        orderLog.setLogType(logType);
        orderLog.setLogTypeName(logTypeName);
        orderLog.setLogId(ApmTraceUtils.getTraceId());
        try {
            rideOrderLogMapper.insert(orderLog);
        }catch (Exception ex){
            log.error("插入订单日志失败",ex);
        }
        finally {
            log.info("插入订单日志,{}", JSON.toJSONString(orderLog));
        }
    }
}
