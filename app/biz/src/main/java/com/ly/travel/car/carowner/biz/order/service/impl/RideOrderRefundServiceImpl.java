/**
 * RideOrderRefundServiceImpl
 * This file is generated by <tt>toolkit(sof-batis-gen)</tt>, a DAL (Data Access Layer)
 *
 * @version Id : RideOrderRefundServiceImpl, v 0.1
 */
package com.ly.travel.car.carowner.biz.order.service.impl;

import java.beans.BeanInfo;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.ly.travel.car.carowner.dal.dataobject.RideOrderRefundDO;
import com.ly.travel.car.carowner.dal.mapper.RideOrderRefundMapper;
import org.springframework.stereotype.Service;

import com.ly.travel.car.carowner.dal.operation.rideorderrefund.*;

import com.ly.travel.car.carowner.biz.order.model.RideOrderRefundVO;
import com.ly.travel.car.carowner.biz.order.service.RideOrderRefundService;
import com.ly.travel.car.carowner.biz.order.mapper.RideOrderRefundMapping;

@Service
public class RideOrderRefundServiceImpl implements RideOrderRefundService {

    @Resource
    private RideOrderRefundMapping mapping;

    @Resource
    private RideOrderRefundMapper rideOrderRefundMapper;

    @Override
    public List<RideOrderRefundVO> queryRefundByOrderNo(String orderNo) {
        List<RideOrderRefundDO> list= rideOrderRefundMapper.selectList(new LambdaQueryWrapper<RideOrderRefundDO>()
                .eq(RideOrderRefundDO::getRefundStatus,1)
                .eq(RideOrderRefundDO::getOrderNo,orderNo));
        return mapping.d2v(list);
    }

    @Override
    public BigDecimal queryRefundAmountByOrderNo(String orderNo) {
        List<RideOrderRefundDO> list= rideOrderRefundMapper.selectList(new LambdaQueryWrapper<RideOrderRefundDO>()
                .eq(RideOrderRefundDO::getOrderNo,orderNo)
                .eq(RideOrderRefundDO::getRefundStatus,1)
        );
        BigDecimal refundAmount=list.stream().map(RideOrderRefundDO::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        return refundAmount;
    }

}
