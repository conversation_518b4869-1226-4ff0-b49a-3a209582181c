package com.ly.travel.car.carowner.biz.order.service.impl;

import com.google.common.collect.Lists;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.biz.order.model.RideOrderTagsEsDTO;
import com.ly.travel.car.carowner.biz.order.service.RideOrderTagsService;
import com.ly.travel.car.carowner.common.utils.EnvUtils;
import com.ly.travel.car.carowner.dal.dataobject.RideOrderInfoDO;
import com.ly.travel.car.carowner.dal.dataobject.RideOrderTagsDO;
import com.ly.travel.car.carowner.dal.mapper.RideOrderInfoMapper;
import com.ly.travel.car.carowner.dal.mapper.RideOrderTagsMapper;
import com.ly.travel.car.carowner.integration.es.EsClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RideOrderTagsServiceImpl implements RideOrderTagsService {

    @Autowired
    private RideOrderTagsMapper rideOrderTagsMapper;
    @Autowired
    private EsClient esClient;
    @Autowired
    private RideOrderInfoMapper rideOrderInfoMapper;

    private static final String ride_order_index = "carowner-order-";
    private static final String ride_order_index_token = "c2bfb75e-bf22-419e-9265-b9fb64aee0dc";

    @Override
    public void addTag(String orderNo, List<String> tags, Long orderId) {
        try {
            for (String tag : tags) {
                log.info("给订单打标签,{},tags:{},订单id:{}", orderNo, tag, orderId);
                RideOrderTagsDO orderTagsDO = new RideOrderTagsDO();
                orderTagsDO.setOrderNo(orderNo);
                orderTagsDO.setStatus(1);
                orderTagsDO.setTag(tag);
                orderTagsDO.setCreateTime(new Date());
                rideOrderTagsMapper.insert(orderTagsDO);
            }

            syncEsOrderTag(orderNo, orderId);
        }catch (Exception ex){
            log.info("给订单打标签异常,{},tags:{},订单id:{}", orderNo, FastJsonUtils.toJSONString(tags), orderId);
        }
    }


    @Override
    public void addTag(String orderNo, String tag, Long orderId) {
        addTag(orderNo,Arrays.asList(tag),orderId);
    }
    private void syncEsOrderTag(String orderNo, Long orderId) {

        if (orderId == null) {
            log.info("同步订单标签ES前查询订单id,{},,订单id:{}",orderNo,orderId);
            orderId = rideOrderInfoMapper.selectOne(new LambdaQueryWrapper<RideOrderInfoDO>().eq(RideOrderInfoDO::getOrderNo, orderNo).select(RideOrderInfoDO::getId)).getId();
        }
        List<String> tags = queryOrderTag(orderNo);
        log.info("同步订单标签ES到,{},tags:{},订单id:{}",orderNo,String.join(",",tags),orderId);

        RideOrderTagsEsDTO rideOrderTagsEsDTO = new RideOrderTagsEsDTO();
        rideOrderTagsEsDTO.setId(orderId);
        rideOrderTagsEsDTO.setTags(tags);
        esClient.save(ride_order_index + EnvUtils.getEnv().getValue(), Arrays.asList(rideOrderTagsEsDTO), ride_order_index_token);


    }

    @Override
    public void removeTag(String orderNo, String tag, Long orderId) {
        log.info("给订单删除标签,{},tags:{},订单id:{}",orderNo,tag,orderId);


        rideOrderTagsMapper.update(new LambdaUpdateWrapper<RideOrderTagsDO>().eq(RideOrderTagsDO::getOrderNo, orderNo)
                .eq(RideOrderTagsDO::getTag, tag).set(RideOrderTagsDO::getStatus, 0));

        syncEsOrderTag(orderNo,orderId);
    }

    @Override
    public List<String> queryOrderTag(String orderNo) {
        return rideOrderTagsMapper.selectList(new LambdaQueryWrapper<RideOrderTagsDO>().eq(RideOrderTagsDO::getOrderNo, orderNo)
                        .eq(RideOrderTagsDO::getStatus, 1).select(RideOrderTagsDO::getTag))
                .stream().map(item -> item.getTag()).collect(Collectors.toList());
    }

    @Override
    public boolean existsOrderTag(String orderNo, String tag) {
        return rideOrderTagsMapper.exists(new LambdaQueryWrapper<RideOrderTagsDO>().eq(RideOrderTagsDO::getOrderNo, orderNo)
                .eq(RideOrderTagsDO::getStatus, 1).eq(RideOrderTagsDO::getTag, tag));
    }
}
