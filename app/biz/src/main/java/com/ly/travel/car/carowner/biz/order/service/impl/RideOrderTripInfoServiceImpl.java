package com.ly.travel.car.carowner.biz.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.travel.car.carowner.biz.order.service.RideOrderTripInfoService;
import com.ly.travel.car.carowner.dal.dataobject.RideOrderTripInfoDO;
import com.ly.travel.car.carowner.dal.mapper.RideOrderTripInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @desc RideOrderTripInfoService
 */
@Slf4j
@Service
public class RideOrderTripInfoServiceImpl implements RideOrderTripInfoService {

    @Resource
    private RideOrderTripInfoMapper rideOrderTripInfoMapper;

    @Override
    public int saveRideOrderTripInfo(RideOrderTripInfoDO form) {
        return rideOrderTripInfoMapper.insert(form);
    }

    @Override
    public RideOrderTripInfoDO queryByOrderNo(String orderNo) {
        return rideOrderTripInfoMapper.selectOne(new QueryWrapper<RideOrderTripInfoDO>().eq("order_no",orderNo),false);

    }

    @Override
    public boolean updateRideOrderTripInfo(RideOrderTripInfoDO rideOrderTripInfoDO) {
        return rideOrderTripInfoMapper.updateById(rideOrderTripInfoDO) > 0;
    }
}
