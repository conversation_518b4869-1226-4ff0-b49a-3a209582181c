package com.ly.travel.car.carowner.biz.push.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.travel.car.carowner.biz.order.model.PassengerOrderInfoVO;
import com.ly.travel.car.carowner.biz.order.model.RideOrderInfoVO;
import com.ly.travel.car.carowner.biz.order.model.RideOrderSettlementVO;
import com.ly.travel.car.carowner.biz.order.service.PassengerOrderInfoService;
import com.ly.travel.car.carowner.biz.order.service.RideOrderInfoService;
import com.ly.travel.car.carowner.biz.push.service.PushMsgService;
import com.ly.travel.car.carowner.biz.settlement.service.RideOrderSettlementService;
import com.ly.travel.car.carowner.dal.dataobject.DriverInfoDO;
import com.ly.travel.car.carowner.dal.dataobject.DriverLineInfoDO;
import com.ly.travel.car.carowner.dal.dataobject.DriverTripInfoDO;
import com.ly.travel.car.carowner.dal.dataobject.PassengerOrderInfoDO;
import com.ly.travel.car.carowner.dal.mapper.DriverInfoMapper;
import com.ly.travel.car.carowner.dal.mapper.DriverLineInfoMapper;
import com.ly.travel.car.carowner.dal.mapper.DriverTripInfoMapper;
import com.ly.travel.car.carowner.dal.mapper.RideOrderInfoMapper;
import com.ly.travel.car.carowner.facade.request.PushMsgRequest;
import com.ly.travel.car.carowner.integration.pushmsg.PushComponent;
import com.ly.travel.car.carowner.integration.pushmsg.PushTemplatesDefinedList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PushMsgServiceImpl implements PushMsgService {
    @Autowired
    private RideOrderInfoService rideOrderInfoService;
    @Autowired
    private DriverTripInfoMapper driverTripInfoMapper;
    @Autowired
    private DriverLineInfoMapper driverLineInfoMapper;
    @Autowired
    private DriverInfoMapper driverInfoMapper;
    @Autowired
    private PushComponent pushComponent;
    @Autowired
    private RideOrderSettlementService rideOrderSettlementService;
    @Autowired
    private PassengerOrderInfoService passengerOrderInfoService;

    private DriverTripInfoDO queryDriverTrip(String driverTripNo){
        return driverTripInfoMapper.selectOne(new LambdaQueryWrapper<DriverTripInfoDO>().eq(DriverTripInfoDO::getDriverTripNo,driverTripNo),false);
    }
    private DriverLineInfoDO queryDriverLine(String driverLineNo){
        return driverLineInfoMapper.selectOne(new LambdaQueryWrapper<DriverLineInfoDO>().eq(DriverLineInfoDO::getDriverLineNo,driverLineNo),false);
    }
    public boolean cxPush(PushMsgRequest pushMsgRequest){

        if("order_cancel".equals(pushMsgRequest.getScene())){
            RideOrderInfoVO rideOrderInfoVO = rideOrderInfoService.queryOrderInfo(pushMsgRequest.getOrderNo(),null);
            Long driverId= pushMsgRequest.getDriverId();
            if(driverId==null){
                driverId=rideOrderInfoVO.getDriverId();
            }
            DriverInfoDO driverInfoDO = driverInfoMapper.selectById(driverId);
            pushComponent.pushTemplate(PushTemplatesDefinedList.CANCEL_ORDER_TEMPLATE.setTemplateParams(
                    rideOrderInfoVO.getStartingTime(),rideOrderInfoVO.getStartCity(),rideOrderInfoVO.getStartDistrict(),rideOrderInfoVO.getStartingAdd(),rideOrderInfoVO.getEndCity()
            ,rideOrderInfoVO.getEndDistrict(),rideOrderInfoVO.getEndingAdd(),rideOrderInfoVO.getOrderNo(),rideOrderInfoVO.getDriverTripNo()
                    ,rideOrderInfoVO.getRealTelephone(),driverInfoDO.getMobile())
                    .setToUser(driverInfoDO.getPublicOpenId()));
            return true;

        }else if("trip_new_order".equals(pushMsgRequest.getScene())) {
            Long driverId= pushMsgRequest.getDriverId();
            PassengerOrderInfoVO passengerOrderInfoDO= passengerOrderInfoService.queryByOrderNo(pushMsgRequest.getOrderNo());
            DriverTripInfoDO driverTripInfoDO=queryDriverTrip(pushMsgRequest.getDriverTripNo());
            if(driverId==null){
                driverId=driverTripInfoDO.getDriverId();
            }
            DriverInfoDO driverInfoDO = driverInfoMapper.selectById(driverId);

            RideOrderSettlementVO rideOrderSettlementVO=rideOrderSettlementService.calculationSettlement(passengerOrderInfoDO);
            pushComponent.pushTemplate(PushTemplatesDefinedList.BROASTCASE_TEMPLATE.setTemplateParams(pushMsgRequest.getExtFields().get("shunludu").toString(),
                    passengerOrderInfoDO.getStartingTime(),passengerOrderInfoDO.getStartCity(),passengerOrderInfoDO.getStartDistrict(),passengerOrderInfoDO.getStartingAdd(),passengerOrderInfoDO.getEndCity()
                    ,passengerOrderInfoDO.getEndDistrict(),passengerOrderInfoDO.getEndingAdd(),passengerOrderInfoDO.getOrderNo(),driverTripInfoDO.getDriverTripNo()
                    ,passengerOrderInfoDO.getRealTelephone(),driverInfoDO.getMobile(),passengerOrderInfoDO.getOrderType(),rideOrderSettlementVO.getDriverSettlementPrice())
                    .setToUser(driverInfoDO.getPublicOpenId()));
            return true;

        }
        else if("line_new_order".equals(pushMsgRequest.getScene())) {
            Long driverId= pushMsgRequest.getDriverId();
            PassengerOrderInfoVO passengerOrderInfoDO= passengerOrderInfoService.queryByOrderNo(pushMsgRequest.getOrderNo());
            DriverLineInfoDO driverLineInfoDO=queryDriverLine(pushMsgRequest.getDriverTripNo());
            if(driverId==null){
                driverId=driverLineInfoDO.getDriverId();
            }
            DriverInfoDO driverInfoDO = driverInfoMapper.selectById(driverId);

            RideOrderSettlementVO rideOrderSettlementVO=rideOrderSettlementService.calculationSettlement(passengerOrderInfoDO);
            pushComponent.pushTemplate(PushTemplatesDefinedList.BROASTCASE_LINE_TEMPLATE.setTemplateParams(driverLineInfoDO.getDriverLineName(),pushMsgRequest.getExtFields().get("shunludu").toString(),
                    passengerOrderInfoDO.getStartingTime(),passengerOrderInfoDO.getStartCity(),passengerOrderInfoDO.getStartDistrict(),passengerOrderInfoDO.getStartingAdd(),passengerOrderInfoDO.getEndCity()
                    ,passengerOrderInfoDO.getEndDistrict(),passengerOrderInfoDO.getEndingAdd(),passengerOrderInfoDO.getOrderNo(),driverLineInfoDO.getDriverLineNo()
                    ,passengerOrderInfoDO.getRealTelephone(),driverInfoDO.getMobile(),passengerOrderInfoDO.getOrderType(),rideOrderSettlementVO.getDriverSettlementPrice())
                    .setToUser(driverInfoDO.getPublicOpenId()));
            return true;

        }
        else if("trip_auto_cancel".equals(pushMsgRequest.getScene())) {
            Long driverId= pushMsgRequest.getDriverId();
            DriverTripInfoDO driverTripInfoDO=queryDriverTrip(pushMsgRequest.getDriverTripNo());
            if(driverId==null){
                driverId=driverTripInfoDO.getDriverId();
            }
            DriverInfoDO driverInfoDO = driverInfoMapper.selectById(driverId);
            pushComponent.pushTemplate(PushTemplatesDefinedList.CANCEL_TRIP_ORDER_TEMPLATE.setTemplateParams(
                    driverTripInfoDO.getPlanStartTime(),driverTripInfoDO.getStartCityName(),driverTripInfoDO.getStartDistrict()
                    ,driverTripInfoDO.getStartAddress(),driverTripInfoDO.getEndCityName()
                    ,driverTripInfoDO.getEndDistrict(),driverTripInfoDO.getEndAddress(),driverTripInfoDO.getDriverTripNo(),driverInfoDO.getMobile())
                    .setToUser(driverInfoDO.getPublicOpenId()));
            return true;
        }
        else if("trip_invite".equals(pushMsgRequest.getScene())) {
            Long driverId= pushMsgRequest.getDriverId();
            PassengerOrderInfoVO passengerOrderInfoDO= passengerOrderInfoService.queryByOrderNo(pushMsgRequest.getOrderNo());
            DriverTripInfoDO driverTripInfoDO=queryDriverTrip(pushMsgRequest.getDriverTripNo());
            if(driverId==null){
                driverId=driverTripInfoDO.getDriverId();
            }
            DriverInfoDO driverInfoDO = driverInfoMapper.selectById(driverId);

            RideOrderSettlementVO rideOrderSettlementVO=rideOrderSettlementService.calculationSettlement(passengerOrderInfoDO);
            pushComponent.pushTemplate(PushTemplatesDefinedList.INVITE_ORDER_TEMPLATE.setTemplateParams(pushMsgRequest.getExtFields().get("shunludu").toString(),
                    passengerOrderInfoDO.getStartingTime(),passengerOrderInfoDO.getStartCity(),passengerOrderInfoDO.getStartDistrict(),passengerOrderInfoDO.getStartingAdd(),passengerOrderInfoDO.getEndCity()
                    ,passengerOrderInfoDO.getEndDistrict(),passengerOrderInfoDO.getEndingAdd(),passengerOrderInfoDO.getOrderNo(),driverTripInfoDO.getDriverTripNo()
                    ,passengerOrderInfoDO.getRealTelephone(),driverInfoDO.getMobile(),passengerOrderInfoDO.getOrderType(),rideOrderSettlementVO.getDriverSettlementPrice())
                    .setToUser(driverInfoDO.getPublicOpenId()));
            return true;
        }
        return false;

    }

}
