package com.ly.travel.car.carowner.biz.retry;

import com.ly.travel.car.carowner.common.enums.BusinessBizEnum;
import lombok.Data;

@Data
public class BusinessMsgPayload {

    /**
     * 业务类型
     */
    private BusinessBizEnum businessBizEnum;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 消息体
     */
    private String msgJsonStr;
    /**
     * 重试次数
     */
    private int retryCount = 1;

}
