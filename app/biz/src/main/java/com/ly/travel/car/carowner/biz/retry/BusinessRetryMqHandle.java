package com.ly.travel.car.carowner.biz.retry;

import com.alibaba.fastjson2.JSON;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.biz.order.service.RideOrderCallbackLogService;
import com.ly.travel.car.carowner.biz.turbomq.producer.RetryableProducer;
import com.ly.travel.car.carowner.common.enums.BusinessBizEnum;
import com.ly.travel.car.carowner.common.enums.CallbackTypeEnum;
import com.ly.travel.car.carowner.common.utils.CarownerConfigCenterUtils;
import com.ly.travel.car.carowner.common.utils.EnvUtil;
import com.ly.travel.car.carowner.dal.dataobject.RideOrderCallbackLogDO;
import com.ly.travel.car.carowner.integration.client.api.supplychain.SupplyChainApi;
import com.ly.travel.car.carowner.integration.client.api.supplychain.request.CallbackRequest;
import com.ly.travel.car.carowner.integration.client.api.supplychain.response.CallbackBaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class BusinessRetryMqHandle {


    @Resource
    private RetryableProducer retryableProducer;
    @Resource
    private SupplyChainApi supplyChainApi;
    @Resource
    private RideOrderCallbackLogService rideOrderCallbackLogService;

    /**
     * 接单通知供应链重试
     *
     * @param businessBizEnum
     */
    public CallbackBaseResponse grabOrderRetry(String orderNo, BusinessBizEnum businessBizEnum, String reqJson, int retryCount) {
        try {
            if (retryCount <= businessBizEnum.getRetryMax()) {
                CallbackRequest request = JSON.parseObject(reqJson, CallbackRequest.class);
                CallbackBaseResponse response = supplyChainApi.callback(request);
                log.info("[grabOrderRetry]当前订单[{}],派单结果回写失败重试-{},responseDTO={}", orderNo, retryCount, JSON.toJSONString(response));

                //保存日志
                this.saveCallbackLog(orderNo, retryCount, request, response);
                return response;
            }
        } catch (Exception e) {
            log.error("[grabOrderRetry]", e);
        }
        return fail();
    }

    /**
     * 取消订单通知供应链重试
     *
     * @param businessBizEnum
     */
    public CallbackBaseResponse cancelOrderRetry(String orderNo, BusinessBizEnum businessBizEnum, String reqJson, int retryCount) {
        try {
            if (retryCount <= businessBizEnum.getRetryMax()) {
                CallbackRequest request = JSON.parseObject(reqJson, CallbackRequest.class);
                CallbackBaseResponse response = supplyChainApi.callback(request);
                log.info("[cancelOrderRetry]当前订单[{}],取消订单失败重试-{},responseDTO={}", orderNo, retryCount, JSON.toJSONString(response));

                //保存日志
                this.saveCallbackLog(orderNo, retryCount, request, response);
                return response;
            }
        } catch (Exception e) {
            log.error("[cancelOrderRetry]", e);
        }
        return fail();
    }

    /**
     * 司机修改订单状态通知供应链重试
     *
     * @param businessBizEnum
     */
    public CallbackBaseResponse driverTripRetry(String orderNo, BusinessBizEnum businessBizEnum, String reqJson, int retryCount) {
        try {
            if (retryCount <= businessBizEnum.getRetryMax()) {
                CallbackRequest request = JSON.parseObject(reqJson, CallbackRequest.class);
                CallbackBaseResponse response = supplyChainApi.callback(request);
                log.warn("[driverTripRetry]当前订单[{}],司机修改订单状态失败重试-{},responseDTO={}", orderNo, retryCount, JSON.toJSONString(response));

                //保存日志
                this.saveCallbackLog(orderNo, retryCount, request, response);

                boolean result = response.isSuccess();
                if (!result) {
                    //继续发送重试
                    BusinessMsgPayload payload = new BusinessMsgPayload();
                    payload.setOrderNo(orderNo);
                    payload.setMsgJsonStr(reqJson);
                    payload.setBusinessBizEnum(businessBizEnum);
                    payload.setRetryCount(++retryCount);
                    retryableProducer.sendDelayMsg(retryableProducer.getTopicName(), "*", FastJsonUtils.toJSONString(payload), businessBizEnum.getSeconds(), TimeUnit.SECONDS);
                }
            }
        } catch (Exception e) {
            log.error("[driverTripRetry]", e);
        }
        //返回成功
        return success();

    }

    public CallbackBaseResponse success() {
        CallbackBaseResponse response = new CallbackBaseResponse();
        response.setCode("200");
        return response;
    }

    public CallbackBaseResponse fail() {
        CallbackBaseResponse response = new CallbackBaseResponse();
        response.setCode("-1");
        return response;
    }

    private void saveCallbackLog(String orderNo, int retryCount, CallbackRequest request, CallbackBaseResponse response) {
        log.info("[BusinessRetryMqHandle][saveCallbackLog]订单：{} 回调供应链异常，保存日志，request：{}", orderNo, FastJsonUtils.toJSONString(request));
        String callbackUrl = CarownerConfigCenterUtils.carPlatformCallBackUrl + "/sfc/tcsfc/status";
        CallbackTypeEnum callbackTypeEnum = CallbackTypeEnum.getByStatus(request.getStatus());
        if (Objects.isNull(callbackTypeEnum)) {
            log.warn("[BusinessRetryMqHandle][saveCallbackLog]回调供应链status：{}异常", request.getStatus());
            return;
        }
        RideOrderCallbackLogDO rideOrderCallbackLogDO;
        List<RideOrderCallbackLogDO> rideOrderCallbackLogDOS = rideOrderCallbackLogService.listByOrderNoAndType(orderNo, callbackTypeEnum.getCallbackType());
        if (CollectionUtils.isEmpty(rideOrderCallbackLogDOS)) {
            rideOrderCallbackLogDO = new RideOrderCallbackLogDO();
            rideOrderCallbackLogDO.setOrderNo(orderNo);
            rideOrderCallbackLogDO.setEnv(EnvUtil.getEnv().getValue());
            rideOrderCallbackLogDO.setCallbackType(callbackTypeEnum.getCallbackType());
            rideOrderCallbackLogDO.setCallbackUrl(callbackUrl);
            rideOrderCallbackLogDO.setCallbackParams(FastJsonUtils.toJSONString(request));
            rideOrderCallbackLogDO.setRetry(retryCount);
            rideOrderCallbackLogDO.setReturnCode(Objects.nonNull(response) ? response.getCode() : null);
            rideOrderCallbackLogDO.setStatus(0);
            rideOrderCallbackLogService.insert(rideOrderCallbackLogDO);
        } else {
            rideOrderCallbackLogDO = rideOrderCallbackLogDOS.get(0);
            rideOrderCallbackLogDO.setRetry(retryCount);
            rideOrderCallbackLogDO.setReturnCode(Objects.nonNull(response) ? response.getCode() : null);
            rideOrderCallbackLogDO.setUpdateTime(new Date());
            rideOrderCallbackLogService.update(rideOrderCallbackLogDO);
        }
    }

}
