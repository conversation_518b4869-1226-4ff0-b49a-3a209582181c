package com.ly.travel.car.carowner.biz.retry;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @desc OrderEventPayload
 */
@Data
@AllArgsConstructor
public class OrderEventPayload {

    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 消息体
     */
    private String eventType;
    /**
     * 订单状态
     */
    private Integer state;
    /**
     * 修改前订单状态
     */
    private Integer preState;
}
