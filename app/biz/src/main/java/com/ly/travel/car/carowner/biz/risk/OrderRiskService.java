package com.ly.travel.car.carowner.biz.risk;

import com.ly.travel.car.carowner.biz.order.model.RideOrderInfoVO;
import com.ly.travel.car.carowner.common.enums.RiskSceneEnum;
import com.ly.travel.car.carowner.integration.client.api.risk.model.response.UnifyCheckResponse;

public interface OrderRiskService {

    /**
     * 订单风控
     */
    boolean orderRisk(RideOrderInfoVO rideOrderInfoVO, RiskSceneEnum riskSceneEnum);

    /**
     * 更新订单冻结时间
     */
    void updateOrderFreezeTime(UnifyCheckResponse response);
}