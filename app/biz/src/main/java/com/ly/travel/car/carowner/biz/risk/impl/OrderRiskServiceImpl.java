package com.ly.travel.car.carowner.biz.risk.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.biz.driver.account.enums.ChangeDriverAmountTypeEnum;
import com.ly.travel.car.carowner.biz.driver.account.service.ChangeDriverAmountProcess;
import com.ly.travel.car.carowner.biz.driver.bill.service.DriverBillService;
import com.ly.travel.car.carowner.biz.driver.withdrawal.service.DriverWithdrawalService;
import com.ly.travel.car.carowner.biz.order.model.RideOrderInfoVO;
import com.ly.travel.car.carowner.biz.order.service.RideOrderInfoService;
import com.ly.travel.car.carowner.biz.order.service.RideOrderTagsService;
import com.ly.travel.car.carowner.biz.risk.OrderRiskService;
import com.ly.travel.car.carowner.biz.sys.service.SysConfigService;
import com.ly.travel.car.carowner.biz.turbomq.producer.DriverBillUnFreezeProducer;
import com.ly.travel.car.carowner.common.constant.RiskConstant;
import com.ly.travel.car.carowner.common.enums.*;
import com.ly.travel.car.carowner.common.exception.BusinessException;
import com.ly.travel.car.carowner.common.lock.DistributedRedisLock;
import com.ly.travel.car.carowner.common.utils.CarownerConfigCenterUtils;
import com.ly.travel.car.carowner.common.utils.DateUtil;
import com.ly.travel.car.carowner.common.utils.EnvUtil;
import com.ly.travel.car.carowner.common.utils.EnvUtils;
import com.ly.travel.car.carowner.dal.dataobject.*;
import com.ly.travel.car.carowner.dal.mapper.*;
import com.ly.travel.car.carowner.facade.request.FreezeDriverBillRequest;
import com.ly.travel.car.carowner.facade.request.UnfreezeDriverBillRequest;
import com.ly.travel.car.carowner.facade.response.CarOwnerResponseDTO;
import com.ly.travel.car.carowner.integration.client.api.risk.RiskClient;
import com.ly.travel.car.carowner.integration.client.api.risk.model.dto.RiskStrategyRespResult;
import com.ly.travel.car.carowner.integration.client.api.risk.model.request.UnifyCheckRequest;
import com.ly.travel.car.carowner.integration.client.api.risk.model.request.RiskApiRequest;
import com.ly.travel.car.carowner.integration.client.api.risk.model.response.UnifyCheckResponse;
import com.ly.travel.car.carowner.integration.exception.IntegrationException;
import com.ly.travel.car.carowner.integration.wxwork.WxWorkAlertComponent;
import com.ly.travel.car.carowner.integration.wxwork.WxWorkTemplateEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OrderRiskServiceImpl implements OrderRiskService {

    @Resource
    private RiskClient riskClient;

    @Resource
    private RideOrderRiskInfoMapper rideOrderRiskInfoMapper;

    @Resource
    private DriverBillService driverBillService;

    @Resource
    private RideOrderRiskLogMapper rideOrderRiskLogMapper;

    @Resource
    private RideOrderInfoService rideOrderInfoService;

    @Resource
    private DriverWithdrawalService driverWithdrawalService;

    @Resource
    private DriverWithdrawLogMapper driverWithdrawLogMapper;

    @Resource
    private DriverBillMapper driverBillMapper;

    @Resource
    private DistributedRedisLock redisLock;

    @Resource
    private RideOrderTagsService rideOrderTagsService;

    @Resource
    private DriverBillUnFreezeProducer turboMqProducer;

    @Resource
    private DriverBillFreezeMapper driverBillFreezeMapper;

    @Resource
    private WxWorkAlertComponent wxWorkAlertComponent;

    @Resource
    private SysConfigService sysConfigService;

    @Override
    public boolean orderRisk(RideOrderInfoVO rideOrderInfoVO, RiskSceneEnum riskSceneEnum) {
        // 构建风控入参
        RiskApiRequest<UnifyCheckRequest> riskApiRequest = buildRiskApiRequest(rideOrderInfoVO, riskSceneEnum);
        try {
            // 调用风控
            UnifyCheckResponse unifyCheckResponse = riskClient.unifyCheck(riskApiRequest);

            // 风控异常单打标
            return handleRiskResult(rideOrderInfoVO, unifyCheckResponse, riskApiRequest, riskSceneEnum);

        } catch (IntegrationException e) {
            log.warn("调用风控验单异常: orderNo={}", rideOrderInfoVO.getOrderNo(), e);
            return handleRiskResult(rideOrderInfoVO, null, riskApiRequest, riskSceneEnum);
        }
    }

    private RiskApiRequest<UnifyCheckRequest> buildRiskApiRequest(RideOrderInfoVO rideOrderInfoVO, RiskSceneEnum riskSceneEnum) {
        RiskApiRequest<UnifyCheckRequest> request = new RiskApiRequest<>();
        request.setTraceId(UUID.randomUUID().toString().replaceAll("_", ""));
        request.setProductLine(RiskConstant.PRODUCT_LINE);
        request.setAppId(RiskConstant.APP_ID);

        UnifyCheckRequest body = new UnifyCheckRequest();
        body.setOrderId(rideOrderInfoVO.getDistributorMainOrderNo());
        body.setProductLine(RiskConstant.PRODUCT_LINE);
        body.setChannel(RiskConstant.RISK_CHANNEL);
        body.setScene(riskSceneEnum.getScene());
        body.setTraceId(request.getTraceId());
        body.setSupplierOrderId(rideOrderInfoVO.getOrderNo());
        body.setDriverId(rideOrderInfoVO.getDriverId() + "");
        body.setCarNum(rideOrderInfoVO.getVehicleNo());
        body.setCertName(rideOrderInfoVO.getDriverName());
        body.setDriverPhone(rideOrderInfoVO.getDriverMobile());
        body.setCarOwnerName(rideOrderInfoVO.getDriverName());
        request.setRequest(body);
        return request;
    }

    private boolean handleRiskResult(RideOrderInfoVO rideOrderInfoVO, UnifyCheckResponse unifyCheckResponse, RiskApiRequest<UnifyCheckRequest> riskApiRequest, RiskSceneEnum riskSceneEnum) {
        if (riskSceneEnum == null) {
            log.warn("订单{}风控验单场景值不存在", rideOrderInfoVO.getOrderNo());
            return false;
        }

        // 日志记录
        addOrderRiskLog(riskSceneEnum, rideOrderInfoVO, unifyCheckResponse);

        RideOrderRiskInfoDO dbRideOrderRiskInfoDO = rideOrderRiskInfoMapper.selectOne(new LambdaQueryWrapper<RideOrderRiskInfoDO>().eq(RideOrderRiskInfoDO::getOrderNo, rideOrderInfoVO.getOrderNo()), false);
        RideOrderRiskInfoDO rideOrderRiskInfoDO = new RideOrderRiskInfoDO();
        rideOrderRiskInfoDO.setUpdateTime(new Date());

        int retryCount = 0; // 当前重试次数
        int maxRetry = 3;   // 最大重试次数
        boolean isError = false;
        switch (riskSceneEnum) {
            case ORDER_COMPLETE:
                // 风控异常单打标
                if (unifyCheckResponse == null) { // 风控返参异常 || 风控未命中
                    rideOrderRiskInfoDO.setUnfreezeTime(DateUtil.addHour(new Date(), CarownerConfigCenterUtils.completeRiskFreezeHour));  // 完单风控未命中解冻时间默认24
                } else {
                    // 命中风控
                    String freezeTime = unifyCheckResponse.getFreezeTime();
                    if (StringUtils.isBlank(freezeTime)) {  // 风控未配策略默认24
                        rideOrderRiskInfoDO.setUnfreezeTime(DateUtil.addHour(new Date(), CarownerConfigCenterUtils.completeRiskFreezeHour));  // 完单风控未命中解冻时间默认24
                    } else {
                        // 完单风控命中 如果晚于当前时间则默认24 反之则取风控解冻时间
                        rideOrderRiskInfoDO.setUnfreezeTime(DateUtil.string2Date(freezeTime).before(new Date()) ? DateUtil.addHour(new Date(), CarownerConfigCenterUtils.completeRiskFreezeHour) : DateUtil.string2Date(freezeTime));
                    }
                }
                break;
            case ORDER_UNFREEZE:
                while (retryCount < maxRetry) {
                    try {
                        // 如果unifyCheckResponse为空，或者满足风险标志条件，调用风控
                        if (unifyCheckResponse == null || StringUtils.isBlank(unifyCheckResponse.getFreezeTime())) {
                            unifyCheckResponse = riskClient.unifyCheck(riskApiRequest);
                            // 日志记录
                            addOrderRiskLog(riskSceneEnum, rideOrderInfoVO, unifyCheckResponse);
                            if (unifyCheckResponse == null || StringUtils.isBlank(unifyCheckResponse.getFreezeTime())) {
                                retryCount++;
                                continue;
                            }
                        }

                        // 小于当前时间则解冻
                        if (DateUtil.string2Date(unifyCheckResponse.getFreezeTime()).before(new Date())) {
                            log.info("订单{}风控解冻时间已过，解冻成功", rideOrderInfoVO.getOrderNo());
                            return false;
                        } else {
                            isError = true;
                        }

                        rideOrderRiskInfoDO.setUnfreezeTime(DateUtil.string2Date(unifyCheckResponse.getFreezeTime()));
                        break;
                    } catch (Exception e) {
                        retryCount++;
                        log.warn("解冻: 订单{}风控解冻调用异常，当前重试次数{}", rideOrderInfoVO.getOrderNo(), retryCount, e);
                    }
                }

                // 如果重试三次后仍然失败
                if (retryCount >= maxRetry) {
                    log.warn("解冻：订单{}风控调用重试3次仍然失败，正常解冻", rideOrderInfoVO.getOrderNo());
                    return false;
                }
                break;
            case ORDER_WITHDRAW:
                while (retryCount < maxRetry) {
                    try {
                        // 如果unifyCheckResponse为空，或者满足风险标志条件，调用风控
                        if (unifyCheckResponse == null || StringUtils.isBlank(unifyCheckResponse.getFreezeTime())) {
                            unifyCheckResponse = riskClient.unifyCheck(riskApiRequest);
                            // 日志记录
                            addOrderRiskLog(riskSceneEnum, rideOrderInfoVO, unifyCheckResponse);
                            if (unifyCheckResponse == null || StringUtils.isBlank(unifyCheckResponse.getFreezeTime())) {
                                retryCount++;
                                continue;
                            }
                        }

                        // 小于当前时间则解冻
                        if (DateUtil.string2Date(unifyCheckResponse.getFreezeTime()).before(new Date())) {
                            log.info("订单{}风控解冻时间已过，解冻成功", rideOrderInfoVO.getOrderNo());
                            return false;
                        }

                        rideOrderRiskInfoDO.setUnfreezeTime(DateUtil.string2Date(unifyCheckResponse.getFreezeTime()));
                        break;
                    } catch (Exception e) {
                        retryCount++;
                        log.warn("提现: 订单{}风控解冻调用异常，准备重试，当前重试次数{}", rideOrderInfoVO.getOrderNo(), retryCount, e);
                    }
                }

                // 如果重试三次后仍然失败
                if (retryCount >= maxRetry) {
                    log.warn("提现: 订单{}风控调用重试3次仍然失败，异常处理", rideOrderInfoVO.getOrderNo());
                    throw new BusinessException(ResultEnum.NETWORK_ERROR);
                }
                break;
        }

        rideOrderRiskInfoDO.setIsFreeze(IsFreezeEnum.CORRECT.getCode());
        if (dbRideOrderRiskInfoDO != null) {
            rideOrderRiskInfoDO.setId(dbRideOrderRiskInfoDO.getId());
            if (rideOrderRiskInfoMapper.updateById(rideOrderRiskInfoDO) <= 0) {
                log.warn("更新订单风控异常单信息失败: rideOrderRiskInfoDO={}", FastJsonUtils.toJSONString(rideOrderRiskInfoDO));
            } else {
                log.info("更新订单风控异常单信息成功: rideOrderRiskInfoDO={}", FastJsonUtils.toJSONString(rideOrderRiskInfoDO));
            }
        } else {
            rideOrderRiskInfoDO.setOrderNo(rideOrderInfoVO.getOrderNo());
            rideOrderRiskInfoDO.setEnv(EnvUtils.getEnvCode());
            rideOrderRiskInfoDO.setCreateTime(new Date());
            if (rideOrderRiskInfoMapper.insert(rideOrderRiskInfoDO) <= 0) {
                log.warn("新增订单风控异常单信息失败: rideOrderRiskInfoDO={}", FastJsonUtils.toJSONString(rideOrderRiskInfoDO));
            } else {
                log.info("新增订单风控异常单信息成功: rideOrderRiskInfoDO={}", FastJsonUtils.toJSONString(rideOrderRiskInfoDO));
            }
        }

        // 订单冻结
        Long second = (rideOrderRiskInfoDO.getUnfreezeTime().getTime() - new Date().getTime()) / 1000;
        FreezeDriverBillRequest request = buildFreezeDriverBillRequest(rideOrderInfoVO.getOrderNo(), rideOrderInfoVO.getDriverId(), second.intValue());
        log.info("订单号{}风控冻结参数: request={}", rideOrderInfoVO.getOrderNo(), FastJsonUtils.toJSONString(request));
        CarOwnerResponseDTO carOwnerResponseDTO = driverBillService.freezeDriverBill(request);
        if (carOwnerResponseDTO.isSuccess()) {
            log.info("订单号{}风控冻结执行成功", rideOrderInfoVO.getOrderNo());
        }

        if (isError) {
            log.warn("订单{}风控解冻时间未到，请稍后重试", rideOrderInfoVO.getOrderNo());
            throw new BusinessException("当前订单未到风控解冻时间");
        }

        return true;
    }

    private FreezeDriverBillRequest buildFreezeDriverBillRequest(String orderNo, Long driverId, Integer freezeSecond) {
        FreezeDriverBillRequest request = new FreezeDriverBillRequest();
        request.setOrderNo(orderNo);
        request.setFreezeType(FreezeTypeEnum.RISK_FREEZE.getType());
        request.setDriverId(driverId);
        request.setOperator("风控冻结");
        request.setFreezeSecond(freezeSecond + 15);
        return request;
    }

    private void addOrderRiskLog(RiskSceneEnum riskSceneEnum, RideOrderInfoVO rideOrderInfoVO, UnifyCheckResponse unifyCheckResponse) {
        if (unifyCheckResponse == null) {
            log.warn("风控异常单日志: 订单{}风控返参为空不记录日志", rideOrderInfoVO.getOrderNo());
            return;
        }
        RideOrderRiskLogDO rideOrderRiskLogDO = new RideOrderRiskLogDO();
        rideOrderRiskLogDO.setOrderNo(rideOrderInfoVO.getOrderNo());
        rideOrderRiskLogDO.setScene(riskSceneEnum.getCode());
        rideOrderRiskLogDO.setState(unifyCheckResponse.isRiskFlag() ? 1 : 0);
        if (unifyCheckResponse.isRiskFlag() && CollectionUtils.isNotEmpty(unifyCheckResponse.getMatchedStrategy())) {
            rideOrderRiskLogDO.setExt(FastJsonUtils.toJSONString(unifyCheckResponse.getMatchedStrategy()));
            List<String> content = unifyCheckResponse.getMatchedStrategyList().stream().map(RiskStrategyRespResult::getStrategyName)
                    .collect(Collectors.toList());
            rideOrderRiskLogDO.setContent(StringUtils.join(content, ";"));
        }
        rideOrderRiskLogDO.setEnv(EnvUtils.getEnvCode());
        if (unifyCheckResponse.getFreezeTime() != null) {
            rideOrderRiskLogDO.setFreezeTime(DateUtil.string2Date(unifyCheckResponse.getFreezeTime()));
        }
        rideOrderRiskLogDO.setGmtOperated(new Date());
        if (rideOrderRiskLogMapper.insert(rideOrderRiskLogDO) <= 0) {
            log.warn("新增订单风控异常单日志失败: rideOrderRiskLogDO={}", FastJsonUtils.toJSONString(rideOrderRiskLogDO));
        } else {
            log.info("新增订单风控异常单日志成功: rideOrderRiskLogDO={}", FastJsonUtils.toJSONString(rideOrderRiskLogDO));
        }
    }

    @Override
    public void updateOrderFreezeTime(UnifyCheckResponse response) {
        if (response == null) {
            log.warn("监听风控更新订单冻结时间: 订单风控返参为空不处理");
            return;
        }

        String freezeTime = response.getFreezeTime();
        if (StringUtils.isEmpty(freezeTime)) {
            log.warn("监听风控更新订单冻结时间: 订单风控返参无冻结时间不处理");
            return;
        }

        RideOrderInfoVO rideOrderInfoVO = rideOrderInfoService.queryOrderInfo(response.getOrderId());
        if (rideOrderInfoVO == null) {
            log.warn("监听风控更新订单冻结时间: 订单不存在");
            return;
        }

        if (rideOrderInfoVO.getStatus() != OrderStatusEnum.COMPLETED.getStatus()) {
            log.warn("监听风控更新订单冻结时间: 订单未完成，不处理");
            return;
        }

        RideOrderRiskInfoDO rideOrderRiskInfoDO = rideOrderRiskInfoMapper.selectOne(new LambdaQueryWrapper<RideOrderRiskInfoDO>()
                .eq(RideOrderRiskInfoDO::getOrderNo, rideOrderInfoVO.getOrderNo())
                .last("LIMIT 1"), false);

        // 订单未存在冻结记录 & 冻结时间在当前时间之前
        if (rideOrderRiskInfoDO == null && DateUtil.string2Date(freezeTime).before(new Date())) {
            log.info("监听风控更新订单冻结时间: 不存在冻结记录且冻结时间在当前时间之前，不处理");
            return;
        }

        RLock lock = null;
        String lockKey = CacheKeyEnum.ORDER_RISK_LOCK.format(rideOrderInfoVO.getOrderNo());
        try {
            lock = redisLock.tryAcquire(lockKey, UUID.randomUUID().toString(), 10, 30, TimeUnit.SECONDS);
            if (lock == null) {
                log.warn("监听风控更新订单冻结时间获取并发锁失败: lockKey={}", lockKey);
                return;
            }

            List<DriverBillDO> driverBillDOS = driverBillService.findDriverBillByOrderNo(rideOrderInfoVO.getOrderNo());
            if (CollectionUtils.isEmpty(driverBillDOS)) {
                log.info("监听风控更新订单冻结时间: 订单号{}未查询到账单", rideOrderInfoVO.getOrderNo());
                return;
            }

            // 查询订单是否提现申请 || 提现
            String withdrawNo = driverBillDOS.get(0).getWithdrawNo();
            if (StringUtils.isNotBlank(withdrawNo)) {
                DriverWithdrawalDO driverWithdrawalDO = driverWithdrawalService.findByWithdrawalNo(withdrawNo);
                if (driverWithdrawalDO == null) {
                    log.info("监听风控更新订单冻结时间: 订单号{}未查询到提现申请单", withdrawNo);
                    return;
                }

                // 提现单待审核
                if (driverWithdrawalDO.getStatus() == DriverWithdrawalStatusEnum.PENDING_REVIEW.getStatus()) {
                    // 冻结时间在当前时间之前
                    if (DateUtil.string2Date(freezeTime).before(new Date())) {
                        // 更新冻结时间
                        updateRiskOrderInfo(rideOrderRiskInfoDO, null, freezeTime, null);
                        log.info("监听风控更新订单冻结时间: 订单风控返参冻结时间已过，订单提现申请单处于待审核状态，更新冻结时间成功");

                        // 日志记录
                        addOrderRiskLog(RiskSceneEnum.ORDER_WITHDRAW, rideOrderInfoVO, response);
                        return;
                    }

                    // 解冻时间在当前时间之后  更新提现单状态为异常
                    driverWithdrawalService.updateDriverWithdrawal(driverWithdrawalDO);

                    // 记录日志
                    addWithdrawLog(withdrawNo, rideOrderInfoVO.getDriverId(), WithdrawLogTypeEnum.APPLY_WITHDRAW.code, "有订单司乘存在争议，不可提现", "风控");

                    // 返还该提现单金额
                    withdrawalReturn(driverWithdrawalDO);

                    // 重新冻结订单
                    CarOwnerResponseDTO carOwnerResponseDTO = freezeDriverBill(rideOrderInfoVO.getOrderNo(), driverWithdrawalDO.getDriverId(), freezeTime);
                    if (carOwnerResponseDTO.isSuccess()) {
                        log.info("监听风控更新订单冻结时间: 冻结时间在当前时间之后且订单已解冻，再次冻结成功");
                        // 更新冻结时间 并冻结
                        updateRiskOrderInfo(rideOrderRiskInfoDO, IsFreezeEnum.CORRECT.getCode(), freezeTime, null);

                        // 日志记录
                        addOrderRiskLog(RiskSceneEnum.ORDER_WITHDRAW, rideOrderInfoVO, response);
                    }
                }

                // 提现单成功提现
                if (driverWithdrawalDO.getStatus() == DriverWithdrawalStatusEnum.SUCCESS.getStatus()) {
                    // 冻结时间在当前时间之前
                    if (DateUtil.string2Date(freezeTime).before(new Date())) {
                        log.info("监听风控更新订单冻结时间: 订单风控返参冻结时间已过，订单提现单处于成功状态");

                        // 日志记录
                        addOrderRiskLog(RiskSceneEnum.ORDER_WITHDRAW, rideOrderInfoVO, response);
                        return;
                    }

                    // 打标
                    updateRiskOrderInfo(rideOrderRiskInfoDO, null, "", WithdrawalRiskStatusEnum.RISK.getCode());

                    // 订单打标
                    rideOrderTagsService.addTag(rideOrderInfoVO.getOrderNo(), OrderTagEnum.WITHDRAWAL_RISK.getCode(), rideOrderInfoVO.getId());

                    // 日志记录
                    addOrderRiskLog(RiskSceneEnum.ORDER_WITHDRAW, rideOrderInfoVO, response);

                    // 机器人通知
                    wxWorkAlertComponent.alert(WxWorkTemplateEnum.WITHDRAW_RISK,
                            Arrays.asList(sysConfigService.getStringCachedCfgValue(SysConfigEnum.WITHDRAWAL_RISK_USER).split(",")),
                            rideOrderInfoVO.getOrderNo());
                }
                return;
            }

            // 订单未存在冻结记录 & 冻结时间在当前时间之后
            if (rideOrderRiskInfoDO == null && DateUtil.string2Date(freezeTime).after(new Date())) {
                // 订单冻结
                CarOwnerResponseDTO carOwnerResponseDTO = freezeDriverBill(rideOrderInfoVO.getOrderNo(), rideOrderInfoVO.getDriverId(), freezeTime);
                if (carOwnerResponseDTO.isSuccess()) {
                    log.info("监听风控更新订单冻结时间: 不存在冻结记录且冻结时间在当前时间之后，执行冻结成功");
                    // 新增订单风控信息
                    addRideOrderRiskInfo(freezeTime, rideOrderInfoVO.getOrderNo());

                    // 日志记录
                    addOrderRiskLog(RiskSceneEnum.ORDER_UNFREEZE, rideOrderInfoVO, response);
                }
                return;
            }

            // 冻结时间在当前时间之前 & 订单处于冻结
            if (DateUtil.string2Date(freezeTime).before(new Date()) && rideOrderRiskInfoDO.getIsFreeze() == IsFreezeEnum.CORRECT.getCode()) {
                log.info("监听风控更新订单冻结时间: 订单风控返参冻结时间已过，直接解冻");
                // 订单解冻
                CarOwnerResponseDTO carOwnerResponseDTO = unfreezeDriverBill(rideOrderInfoVO.getOrderNo());
                if (carOwnerResponseDTO.isSuccess()) {
                    log.info("监听风控更新订单冻结时间: 订单风控返参冻结时间已过且订单处于冻结中，直接解冻成功");
                    // 更新冻结时间 并解冻
                    updateRiskOrderInfo(rideOrderRiskInfoDO, IsFreezeEnum.DENY.getCode(), freezeTime, null);

                    // 日志记录
                    addOrderRiskLog(RiskSceneEnum.ORDER_UNFREEZE, rideOrderInfoVO, response);
                }
                return;
            }

            // 冻结时间在当前时间之后 & 订单处于冻结
            if (DateUtil.string2Date(freezeTime).after(new Date()) && rideOrderRiskInfoDO.getIsFreeze() == IsFreezeEnum.CORRECT.getCode()) {
                // 更新冻结时间
                updateRiskOrderInfo(rideOrderRiskInfoDO, null, freezeTime, null);
                log.info("监听风控更新订单冻结时间: 订单风控返参冻结时间在当前时间之后且订单处于冻结中，更新冻结时间成功");

                // 日志记录
                addOrderRiskLog(RiskSceneEnum.ORDER_UNFREEZE, rideOrderInfoVO, response);

                // 更新账单解冻时间
                updateDriverBill(driverBillDOS, freezeTime);

                // 发送延迟解冻
                sendUnfreezeDelayMQ(rideOrderInfoVO, freezeTime);

                return;
            }

            // 冻结时间在当前时间之后 & 订单已解冻
            if (DateUtil.string2Date(freezeTime).after(new Date()) && rideOrderRiskInfoDO.getIsFreeze() == IsFreezeEnum.DENY.getCode()) {
                // 订单冻结
                CarOwnerResponseDTO carOwnerResponseDTO = freezeDriverBill(rideOrderInfoVO.getOrderNo(), rideOrderInfoVO.getDriverId(), freezeTime);
                if (carOwnerResponseDTO.isSuccess()) {
                    log.info("监听风控更新订单冻结时间: 冻结时间在当前时间之后且订单已解冻，再次冻结成功");
                    // 更新冻结时间 并冻结
                    updateRiskOrderInfo(rideOrderRiskInfoDO, IsFreezeEnum.CORRECT.getCode(), freezeTime, null);

                    // 日志记录
                    addOrderRiskLog(RiskSceneEnum.ORDER_UNFREEZE, rideOrderInfoVO, response);
                }
                return;
            }

            // 冻结时间在当前时间之前 & 订单已解冻
            if (DateUtil.string2Date(freezeTime).before(new Date()) && rideOrderRiskInfoDO.getIsFreeze() == IsFreezeEnum.DENY.getCode()) {
                // 更新冻结时间
                updateRiskOrderInfo(rideOrderRiskInfoDO, null, freezeTime, null);
                log.info("监听风控更新订单冻结时间: 订单风控返参冻结时间已过且订单已解冻，更新冻结时间成功");

                // 日志记录
                addOrderRiskLog(RiskSceneEnum.ORDER_UNFREEZE, rideOrderInfoVO, response);
            }

        } catch (Exception e) {
            log.error("监听风控更新订单冻结时间处理异常: orderNo={}", rideOrderInfoVO.getOrderNo(), e);
        } finally {
            if (lock != null) {
                redisLock.release(lock, lockKey, "");
            }
        }
    }

    private void updateDriverBill(List<DriverBillDO> driverBillDOS, String freezeTime) {
        List<String> billNos = driverBillDOS.stream().map(DriverBillDO::getBillNo).distinct().collect(Collectors.toList());
        DriverBillFreezeDO updateEntity = new DriverBillFreezeDO();
        updateEntity.setUnfreezeTime(DateUtil.string2Date(freezeTime));
        updateEntity.setUpdateTime(new Date());
        int i = driverBillFreezeMapper.update(updateEntity, new LambdaQueryWrapper<DriverBillFreezeDO>()
                .in(DriverBillFreezeDO::getBillNo, billNos)
                .eq(DriverBillFreezeDO::getFreezeType, FreezeTypeEnum.RISK_FREEZE.getType())
                .eq(DriverBillFreezeDO::getEnv, EnvUtil.getEnv().getValue()));
        if (i <= 0) {
            log.warn("更新风控账单类型冻结时间失败: billNos={}", billNos);
        } else {
            log.info("更新风控账单类型冻结时间成功: billNos={}", billNos);
        }
    }

    private void sendUnfreezeDelayMQ(RideOrderInfoVO rideOrderInfoVO, String freezeTime) {
        UnfreezeDriverBillRequest unfreezeDriverBillRequest = new UnfreezeDriverBillRequest();
        unfreezeDriverBillRequest.setTraceId(UUID.randomUUID().toString());
        unfreezeDriverBillRequest.setOrderNo(rideOrderInfoVO.getOrderNo());
        unfreezeDriverBillRequest.setUnFreezeTypes(Collections.singletonList(FreezeTypeEnum.RISK_FREEZE.getType()));
        unfreezeDriverBillRequest.setOperator("系统定时");
        Long second = (DateUtil.string2Date(freezeTime).getTime() - new Date().getTime()) / 1000;
        try {
            turboMqProducer.sendDelayMsg(turboMqProducer.getTopicName(), FastJsonUtils.toJSONString(unfreezeDriverBillRequest), second + 10, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.warn("订单风控监听发送延迟解冻消息失败: unfreezeDriverBillRequest={}", FastJsonUtils.toJSONString(unfreezeDriverBillRequest), e);
        }
    }

    private CarOwnerResponseDTO freezeDriverBill(String orderNo, Long driverId, String freezeTime) {
        FreezeDriverBillRequest request = new FreezeDriverBillRequest();
        request.setOrderNo(orderNo);
        request.setFreezeType(FreezeTypeEnum.RISK_FREEZE.getType());
        request.setDriverId(driverId);
        request.setOperator("订单风控监听冻结");
        Long second = (DateUtil.string2Date(freezeTime).getTime() - new Date().getTime()) / 1000;
        request.setFreezeSecond(second.intValue() + 10);
        return driverBillService.freezeDriverBill(request);
    }

    private CarOwnerResponseDTO unfreezeDriverBill(String orderNo) {
        UnfreezeDriverBillRequest request = new UnfreezeDriverBillRequest();
        request.setOrderNo(orderNo);
        request.setUnFreezeTypes(Arrays.asList(FreezeTypeEnum.RISK_FREEZE.getType()));
        request.setOperator("订单风控监听解冻");
        return driverBillService.unfreezeDriverBill(request);
    }

    private void addRideOrderRiskInfo(String freezeTime, String orderNo) {
        RideOrderRiskInfoDO rideOrderRiskInfoDO = new RideOrderRiskInfoDO();
        rideOrderRiskInfoDO.setOrderNo(orderNo);
        rideOrderRiskInfoDO.setIsFreeze(IsFreezeEnum.CORRECT.getCode());
        rideOrderRiskInfoDO.setUnfreezeTime(DateUtil.string2Date(freezeTime));
        rideOrderRiskInfoDO.setCreateTime(new Date());
        rideOrderRiskInfoDO.setEnv(EnvUtils.getEnvCode());
        rideOrderRiskInfoDO.setUpdateTime(new Date());
        if (rideOrderRiskInfoMapper.insert(rideOrderRiskInfoDO) <= 0) {
            log.warn("监听风控更新订单冻结时间: 新增订单风控异常单信息失败: rideOrderRiskInfoDO={}", FastJsonUtils.toJSONString(rideOrderRiskInfoDO));
        } else {
            log.info("监听风控更新订单冻结时间: 新增订单风控异常单信息成功: rideOrderRiskInfoDO={}", FastJsonUtils.toJSONString(rideOrderRiskInfoDO));
        }
    }

    private void withdrawalReturn(DriverWithdrawalDO driverWithdrawalDO) {
        // 更新账户
        ChangeDriverAmountProcess.getInstance(ChangeDriverAmountTypeEnum.FAILURE_BACK).changeAmount_sync(driverWithdrawalDO.getDriverId(), driverWithdrawalDO.getAmount(), driverWithdrawalDO.getWithdrawNo());

        // 更新账单
        List<DriverBillDO> driverBillDOS = driverBillMapper.selectList(new LambdaQueryWrapper<DriverBillDO>()
                .eq(DriverBillDO::getWithdrawNo, driverWithdrawalDO.getWithdrawNo())
                .eq(DriverBillDO::getDriverId, driverWithdrawalDO.getDriverId())
                .eq(DriverBillDO::getEnv, EnvUtil.getEnv().getValue()));
        updateDriverBill("", driverBillDOS);
    }

    private void updateDriverBill(String withDrawNo, List<DriverBillDO> driverBillDOS) {
        List<Long> billIds = driverBillDOS.stream().map(DriverBillDO::getId).collect(Collectors.toList());
        DriverBillDO updateEntity = new DriverBillDO();
        updateEntity.setWithdrawNo(withDrawNo);
        updateEntity.setUpdateTime(new Date());
        updateEntity.setUpdateUser("system");
        int i = driverBillMapper.update(updateEntity, new LambdaQueryWrapper<DriverBillDO>()
                .in(DriverBillDO::getId, billIds)
                .eq(DriverBillDO::getEnv, EnvUtil.getEnv().getValue()));
        if (i <= 0) {
            log.warn("更新账单信息失败: updateEntity={}", FastJsonUtils.toJSONString(updateEntity));
        }
    }

    private void updateRiskOrderInfo(RideOrderRiskInfoDO rideOrderRiskInfoDO, Integer isFreeze, String freezeTime, Integer withdrawalRiskStatus) {
        RideOrderRiskInfoDO updateEntity = new RideOrderRiskInfoDO();
        updateEntity.setId(rideOrderRiskInfoDO.getId());
        updateEntity.setWithdrawalRiskStatus(withdrawalRiskStatus);
        updateEntity.setUpdateTime(new Date());

        if (StringUtils.isNotBlank(freezeTime)) {
            updateEntity.setUnfreezeTime(DateUtil.string2Date(freezeTime));
        }

        if (isFreeze != null) {
            updateEntity.setIsFreeze(isFreeze);
        }

        if (rideOrderRiskInfoMapper.updateById(updateEntity) <= 0) {
            log.warn("监听风控更新订单冻结时间: 更新订单风控异常单信息失败: updateEntity={}", FastJsonUtils.toJSONString(updateEntity));
        } else {
            log.info("监听风控更新订单冻结时间: 更新订单风控异常单信息成功: updateEntity={}", FastJsonUtils.toJSONString(updateEntity));
        }
    }

    private void addWithdrawLog(String withdrawNo, Long driverId, Integer logType, String remark, String operator) {
        DriverWithdrawLogDO driverWithdrawLogDO = new DriverWithdrawLogDO();
        driverWithdrawLogDO.setWithdrawNo(withdrawNo);
        driverWithdrawLogDO.setLogType(logType);
        driverWithdrawLogDO.setRemark(remark);
        driverWithdrawLogDO.setDriverId(driverId);
        driverWithdrawLogDO.setCreateTime(new Date());
        driverWithdrawLogDO.setCreateUser(operator);
        if (driverWithdrawLogMapper.insert(driverWithdrawLogDO) <= 0) {
            log.warn("新增提现单日志信息失败: driverWithdrawLogDO={}", FastJsonUtils.toJSONString(driverWithdrawLogDO));
        }
    }
}