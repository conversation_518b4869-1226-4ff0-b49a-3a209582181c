package com.ly.travel.car.carowner.biz.settlement.enums;

import com.ly.travel.car.carowner.biz.settlement.service.SettlementPolicyProcess;
import com.ly.travel.car.carowner.biz.settlement.service.impl.SettlementCancelPolicyProcess;
import com.ly.travel.car.carowner.biz.settlement.service.impl.SettlementCompletedPolicyProcess;
import com.ly.travel.car.carowner.biz.settlement.service.impl.SettlementReceivedOrderPolicyProcess;
import com.ly.travel.car.carowner.biz.settlement.service.impl.SettlementRefundPolicyProcess;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum SettlementTypeEnum {

    RECEIVED_ORDER(1, "接单成功", "settlementReceivedOrderPolicyProcess"),

    CANCEL(2, "取消", "settlementCancelPolicyProcess"),

    COMPLETED_ORDER(3, "完单", "settlementCompletedPolicyProcess"),

    REFUND(4, "退款", "settlementRefundPolicyProcess"),

    ;

    private int code;

    private String desc;
    private String aClass;

    public static SettlementTypeEnum getByCode(Integer value) {
        if (null == value) {
            return null;
        }
        SettlementTypeEnum[] elements = values();
        for (SettlementTypeEnum element : elements) {
            if (element.getCode() == value.intValue()) {
                return element;
            }
        }
        return null;
    }
}
