/**
* CommissionSettingMapping
* This file is generated by <tt>toolkit(sof-batis-gen)</tt>, a DAL (Data Access Layer)
* @version Id : CommissionSettingMapping, v 0.1
*/
package com.ly.travel.car.carowner.biz.settlement.mapper;

import com.ly.flight.toolkit.converter.BooleanConverter;
import com.ly.flight.toolkit.converter.FullDateConverter;

import com.ly.travel.car.carowner.dal.dataobject.*;

import com.ly.travel.car.carowner.biz.settlement.model.CommissionSettingVO;

import org.mapstruct.Mapper;
import org.mapstruct.NullValueMappingStrategy;

import java.util.Collection;
import java.util.List;

@Mapper(componentModel = "spring", nullValueMappingStrategy = NullValueMappingStrategy.RETURN_NULL, uses = { FullDateConverter.class, BooleanConverter.class })
public interface CommissionSettingMapping {

CommissionSettingDO v2d(CommissionSettingVO v);

CommissionSettingVO d2v(CommissionSettingDO d);

List<CommissionSettingVO> d2v(Collection<CommissionSettingDO> d);

List<CommissionSettingDO> v2d(Collection<CommissionSettingVO> v);

}
