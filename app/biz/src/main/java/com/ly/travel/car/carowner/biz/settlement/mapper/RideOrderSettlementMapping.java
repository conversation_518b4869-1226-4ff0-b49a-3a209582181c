/**
* RideOrderSettlementMapping
* This file is generated by <tt>toolkit(sof-batis-gen)</tt>, a DAL (Data Access Layer)
* @version Id : RideOrderSettlementMapping, v 0.1
*/
package com.ly.travel.car.carowner.biz.settlement.mapper;

import com.ly.flight.toolkit.converter.BooleanConverter;
import com.ly.flight.toolkit.converter.FullDateConverter;
import com.ly.sof.utils.page.Paginator;

import com.ly.travel.car.carowner.dal.dataobject.*;
import com.ly.travel.car.carowner.dal.operation.rideordersettlement.*;

import com.ly.travel.car.carowner.biz.order.model.QueryRideOrderSettlementVO;
import com.ly.travel.car.carowner.biz.order.model.RideOrderSettlementVO;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.NullValueMappingStrategy;

import java.util.Collection;
import java.util.List;

@Mapper(componentModel = "spring", nullValueMappingStrategy = NullValueMappingStrategy.RETURN_NULL, uses = { FullDateConverter.class, BooleanConverter.class })
public interface RideOrderSettlementMapping {

RideOrderSettlementDO v2d(RideOrderSettlementVO v);

RideOrderSettlementVO d2v(RideOrderSettlementDO d);

List<RideOrderSettlementVO> d2v(Collection<RideOrderSettlementDO> d);

List<RideOrderSettlementDO> v2d(Collection<RideOrderSettlementVO> v);

@Mappings({@Mapping(target = "page", expression = "java(page.getCurrentPage())")})
FindPageQuery v2FindPage(QueryRideOrderSettlementVO v,Paginator page);

@Mappings({@Mapping(target = "page", expression = "java(page.getCurrentPage())")})
FindPageQuery v2FindPage(RideOrderSettlementVO v,Paginator page);

QueryAllQuery v2QueryAllQuery(QueryRideOrderSettlementVO v);
}
