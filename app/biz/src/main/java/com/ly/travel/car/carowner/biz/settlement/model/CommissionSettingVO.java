/**
* CommissionSettingVO
* This file is generated by <tt>toolkit(sof-batis-gen)</tt>, a DAL (Data Access Layer)
* @version Id : CommissionSettingVO, v 0.1
*/
package com.ly.travel.car.carowner.biz.settlement.model;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import com.ly.flight.toolkit.pojo.BaseFormVO;

import com.ly.flight.toolkit.annoations.Diff;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ly.flight.toolkit.converter.DateTimeJsonSerializer;
import com.ly.flight.toolkit.converter.JsonDateDeserializer;
import lombok.EqualsAndHashCode;

@Data
public class CommissionSettingVO   {

    /** 佣金计算类型 1 比例 2 固定金额 */

    private Integer              type;

    /** 佣金计算比例 包车 */
    private BigDecimal              commRateAll;

    /** 佣金计算比例 拼车 */
    private BigDecimal commRateJoin;

    /** 佣金上限 包车 */
    private BigDecimal              commLimitAll;

    /** 佣金上限 拼车 */
    private BigDecimal              commLimitJoin;



}
