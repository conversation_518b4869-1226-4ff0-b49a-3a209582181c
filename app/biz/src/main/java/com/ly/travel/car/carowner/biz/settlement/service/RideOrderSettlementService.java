/**
 * RideOrderSettlementService
 * This file is generated by <tt>toolkit(sof-batis-gen)</tt>, a DAL (Data Access Layer)
 *
 * @version Id : RideOrderSettlementService, v 0.1
 */
package com.ly.travel.car.carowner.biz.settlement.service;

import com.ly.flight.toolkit.service.FormService;

import java.math.BigDecimal;
import java.util.List;

import com.ly.sof.utils.page.PageList;
import com.ly.sof.utils.page.Paginator;

import com.ly.travel.car.carowner.biz.order.model.PassengerOrderInfoVO;
import com.ly.travel.car.carowner.biz.order.model.RideOrderInfoVO;
import com.ly.travel.car.carowner.biz.order.model.RideOrderSettlementVO;
import com.ly.travel.car.carowner.biz.order.model.QueryRideOrderSettlementVO;
import com.ly.travel.car.carowner.biz.settlement.model.CommissionSettingVO;

public interface RideOrderSettlementService {

    //查结算信息
    List<RideOrderSettlementVO> queryByOrderNos(List<String> orderNos);

    RideOrderSettlementVO queryByOrderNo(String orderNo) ;

        //计算结算信息
    RideOrderSettlementVO calculationSettlement(PassengerOrderInfoVO passengerOrderInfoVO);

    void saveOrderSettlement(RideOrderSettlementVO rideOrderSettlementVO);

    BigDecimal calculationDriverRefundSettlement(BigDecimal refund,RideOrderSettlementVO orderSettlementVO);

    }
