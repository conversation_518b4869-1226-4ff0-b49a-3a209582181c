package com.ly.travel.car.carowner.biz.settlement.service;

import com.alibaba.fastjson.JSON;
import com.ly.travel.car.carowner.biz.order.model.RideOrderInfoVO;
import com.ly.travel.car.carowner.biz.order.model.RideOrderSettlementVO;
import com.ly.travel.car.carowner.biz.order.service.RideOrderInfoService;
import com.ly.travel.car.carowner.biz.settlement.enums.SettlementTypeEnum;
import com.ly.travel.car.carowner.common.enums.CacheKeyEnum;
import com.ly.travel.car.carowner.common.exception.OrderSettlementException;
import com.ly.travel.car.carowner.common.lock.DistributedRedisLock;
import com.ly.travel.car.carowner.common.utils.ApplicationContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;


public abstract class SettlementPolicyProcess {

    final Logger log=LoggerFactory.getLogger(this.getClass());
    @Autowired
    private RideOrderInfoService rideOrderInfoService;
    @Autowired
    private RideOrderSettlementService rideOrderSettlementService;
    @Autowired
    private DistributedRedisLock distributedRedisLock;

    public abstract RideOrderSettlementVO beforeProcess(RideOrderInfoVO rideOrderInfo, Map<String,Object> extend);

    public abstract RideOrderSettlementVO afterProcess(RideOrderInfoVO rideOrderInfo, RideOrderSettlementVO orderSettlement,Map<String,Object> extend);


    public void processing(String orderNo) {
        String lockKey="core:settlement:lock:"+orderNo;
        RLock lock=null;
        String lockVal=String.valueOf(System.currentTimeMillis());
        try {
            lock=distributedRedisLock.tryAcquire(lockKey,lockVal,10,30, TimeUnit.SECONDS);
            if(lock==null) {
                log.warn("获取锁失败，可能是重复结算请求,{}",orderNo);
                return;
            }
            RideOrderInfoVO rideOrderInfoVO = rideOrderInfoService.queryOrderInfo(orderNo, null);
            if(rideOrderInfoVO==null){
                throw new OrderSettlementException("没有订单信息，重试处理,{}",orderNo);
            }
            Map<String, Object> extend = new HashMap<>(1);
            RideOrderSettlementVO orderSettlement = beforeProcess(rideOrderInfoVO, extend);
            log.info("订单新结算数据为:{}", JSON.toJSONString(orderSettlement));
            rideOrderSettlementService.saveOrderSettlement(orderSettlement);
            afterProcess(rideOrderInfoVO, orderSettlement, extend);
        }catch (Exception ex){
            log.error("处理结算时异常,{}",orderNo,ex);
            throw ex;
        }finally {
            distributedRedisLock.release(lock,lockKey,lockVal);
        }
    }


    public static SettlementPolicyProcess getInstance(SettlementTypeEnum settlementTypeEnum) {
        return ApplicationContextUtils.getContext().getBean(settlementTypeEnum.getAClass(),SettlementPolicyProcess.class);
    }
}
