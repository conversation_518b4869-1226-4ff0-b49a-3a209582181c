/**
 * CommissionSettingServiceImpl
 * This file is generated by <tt>toolkit(sof-batis-gen)</tt>, a DAL (Data Access Layer)
 *
 * @version Id : CommissionSettingServiceImpl, v 0.1
 */
package com.ly.travel.car.carowner.biz.settlement.service.impl;

import javax.annotation.Resource;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.travel.car.carowner.biz.settlement.service.CommissionSettingService;
import com.ly.travel.car.carowner.common.cache.CacheUtils;
import com.ly.travel.car.carowner.common.enums.CacheKeyEnum;
import com.ly.travel.car.carowner.common.utils.EnvUtil;
import com.ly.travel.car.carowner.dal.mapper.CommissionSettingMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import com.ly.travel.car.carowner.dal.dataobject.*;

import com.ly.travel.car.carowner.biz.settlement.model.CommissionSettingVO;
import com.ly.travel.car.carowner.biz.settlement.mapper.CommissionSettingMapping;

import org.springframework.util.StringUtils;
@Slf4j
@Service
public class CommissionSettingServiceImpl   implements CommissionSettingService {

    @Resource
    private CommissionSettingMapping mapping;

    @Resource
    private CommissionSettingMapper commissionSettingMapper;



    @Override
    public CommissionSettingVO queryAvailableCommission()   {
        CommissionSettingVO commissionSettingVO=null;
                String cacheValue=CacheUtils.getCacheValue(CacheKeyEnum.COMMISSION_CONFIG.getFormat());

        if(StringUtils.hasText(cacheValue)){
            if(cacheValue.equals("null")){
                log.error("没有配置分佣异常");
                return null;
            }
            commissionSettingVO= JSON.parseObject(cacheValue,CommissionSettingVO.class);
            return commissionSettingVO;
        }

        commissionSettingVO = mapping.d2v(commissionSettingMapper.selectOne(new LambdaQueryWrapper<CommissionSettingDO>()
                .eq(CommissionSettingDO::getEnv, EnvUtil.getEnv().getValue())));
        if(commissionSettingVO==null){
            log.error("没有配置分佣异常db");
            cacheValue="null";
            CacheUtils.setCacheValue(CacheKeyEnum.COMMISSION_CONFIG.getFormat(),cacheValue,60);
        }else{
            cacheValue=JSON.toJSONString(commissionSettingVO);
            CacheUtils.setCacheValue(CacheKeyEnum.COMMISSION_CONFIG.getFormat(),cacheValue,CacheKeyEnum.COMMISSION_CONFIG.getExpire());
        }
        return commissionSettingVO;
    }


}
