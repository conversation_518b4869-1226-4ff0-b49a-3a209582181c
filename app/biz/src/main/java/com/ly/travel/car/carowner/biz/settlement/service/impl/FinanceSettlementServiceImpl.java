package com.ly.travel.car.carowner.biz.settlement.service.impl;

import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.biz.settlement.service.FinanceSettlementService;
import com.ly.travel.car.carowner.biz.turbomq.model.FinancialSettlementDTO;
import com.ly.travel.car.carowner.biz.turbomq.producer.FinanceSettlementProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.UUID;

@Service
@Slf4j
public class FinanceSettlementServiceImpl implements FinanceSettlementService {
    @Autowired
    private FinanceSettlementProducer financeSettlementProducer;

    public void sendFreeCommissionSettlement(String orderNo,String mainOrderNo, BigDecimal freeSettlementAmount){
        log.info("订单调用推财务免佣结算信息,{},{}",orderNo,freeSettlementAmount);
        String tranId=UUID.randomUUID().toString();

        FinancialSettlementDTO financialSettlementDTO=new FinancialSettlementDTO();
        FinancialSettlementDTO.RebateItem rebateItem=new FinancialSettlementDTO.RebateItem(mainOrderNo,freeSettlementAmount);
        financialSettlementDTO.setOrderSerialId(mainOrderNo);
        financialSettlementDTO.setBusinessSerialId(tranId);
        financialSettlementDTO.setRebateList(Arrays.asList(rebateItem));
        try {
            financeSettlementProducer.sendMsg(financeSettlementProducer.getTopicName(),"transmission_supplier_rebate",tranId, FastJsonUtils.toJSONString(financialSettlementDTO));
        } catch (Exception e) {
            log.error("免佣卡使用后司机免佣结算信息推送财务失败,{},{}",orderNo,FastJsonUtils.toJSONString(financialSettlementDTO),e);
        }
    }
}
