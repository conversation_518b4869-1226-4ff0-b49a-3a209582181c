/**
 * RideOrderSettlementServiceImpl
 * This file is generated by <tt>toolkit(sof-batis-gen)</tt>, a DAL (Data Access Layer)
 *
 * @version Id : RideOrderSettlementServiceImpl, v 0.1
 */
package com.ly.travel.car.carowner.biz.settlement.service.impl;
import java.util.Date;

import java.math.BigDecimal;

import java.util.List;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.travel.car.carowner.biz.order.model.RideOrderInfoVO;
import com.ly.travel.car.carowner.biz.settlement.model.CommissionSettingVO;
import com.ly.travel.car.carowner.biz.order.model.PassengerOrderInfoVO;
import com.ly.travel.car.carowner.biz.settlement.service.CommissionSettingService;
import com.ly.travel.car.carowner.common.enums.OrderTypeEnum;
import com.ly.travel.car.carowner.dal.mapper.RideOrderSettlementMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ly.travel.car.carowner.dal.dataobject.*;

import com.ly.travel.car.carowner.biz.order.model.RideOrderSettlementVO;
import com.ly.travel.car.carowner.biz.settlement.service.RideOrderSettlementService;
import com.ly.travel.car.carowner.biz.settlement.mapper.RideOrderSettlementMapping;

@Service
public class RideOrderSettlementServiceImpl implements RideOrderSettlementService {

    @Resource
    private RideOrderSettlementMapping mapping;

    @Autowired
    private RideOrderSettlementMapper rideOrderSettlementMapper;

    @Autowired
    private CommissionSettingService commissionSettingService;


    @Override
    public List<RideOrderSettlementVO> queryByOrderNos(List<String> orderNos) {
        List<RideOrderSettlementDO> rideOrderSettlementDO = rideOrderSettlementMapper.selectList(new LambdaQueryWrapper<RideOrderSettlementDO>().in(RideOrderSettlementDO::getOrderNo, orderNos));
        return mapping.d2v(rideOrderSettlementDO);
    }
    @Override
    public RideOrderSettlementVO queryByOrderNo(String orderNo) {
        RideOrderSettlementDO rideOrderSettlementDO = rideOrderSettlementMapper.selectOne(new LambdaQueryWrapper<RideOrderSettlementDO>().eq(RideOrderSettlementDO::getOrderNo, orderNo),false);
        return mapping.d2v(rideOrderSettlementDO);
    }

    @Override
    public RideOrderSettlementVO calculationSettlement(PassengerOrderInfoVO passengerOrderInfoVO) {
        CommissionSettingVO commissionSettingVO=commissionSettingService.queryAvailableCommission();
        BigDecimal supplyMaidRate =null;
        BigDecimal SupplierSettlementPrice=null;
        BigDecimal orderAmount = passengerOrderInfoVO.getAmount();
        BigDecimal supplierLimitMaxAmount=null;
        if(passengerOrderInfoVO.getOrderType()== OrderTypeEnum.NOT_POOL.getType()){
            supplierLimitMaxAmount=commissionSettingVO.getCommLimitAll();
            supplyMaidRate=commissionSettingVO.getCommRateAll();//查抽佣金额
            SupplierSettlementPrice=orderAmount.multiply(supplyMaidRate).setScale(2, BigDecimal.ROUND_DOWN);
            if(SupplierSettlementPrice.compareTo(commissionSettingVO.getCommLimitAll())>0){
                SupplierSettlementPrice=commissionSettingVO.getCommLimitAll();
            }
        }else  if(passengerOrderInfoVO.getOrderType()== OrderTypeEnum.POOL.getType()){
            supplierLimitMaxAmount=commissionSettingVO.getCommLimitJoin();
            supplyMaidRate=commissionSettingVO.getCommRateJoin();//查抽佣金额
            SupplierSettlementPrice=orderAmount.multiply(supplyMaidRate).setScale(2, BigDecimal.ROUND_DOWN);
            if(SupplierSettlementPrice.compareTo(commissionSettingVO.getCommLimitJoin())>0){
                SupplierSettlementPrice=commissionSettingVO.getCommLimitJoin();
            }
        }

        RideOrderSettlementVO rideOrderSettlementVO = new RideOrderSettlementVO();
        rideOrderSettlementVO.setOrderNo(passengerOrderInfoVO.getOrderNo());
        rideOrderSettlementVO.setSupplierSettlementPrice(SupplierSettlementPrice);
        rideOrderSettlementVO.setSupplierLimitMaxAmount(supplierLimitMaxAmount);
        rideOrderSettlementVO.setPlatformSettlementPrice(orderAmount.multiply(passengerOrderInfoVO.getOrderCommissionRatio()).setScale(2, BigDecimal.ROUND_DOWN));
        rideOrderSettlementVO.setSupplierMaidRate(supplyMaidRate);
        rideOrderSettlementVO.setPlatformMaidRate(passengerOrderInfoVO.getOrderCommissionRatio());
        rideOrderSettlementVO.setDriverSettlementPrice(orderAmount.subtract(rideOrderSettlementVO.getPlatformSettlementPrice()).subtract(rideOrderSettlementVO.getSupplierSettlementPrice()));
        rideOrderSettlementVO.setOldDriverSettlementPrice(rideOrderSettlementVO.getDriverSettlementPrice());
        rideOrderSettlementVO.setDriverExtraSettlementPrice(BigDecimal.ZERO);
        return rideOrderSettlementVO;

    }

    @Override
    public void saveOrderSettlement(RideOrderSettlementVO rideOrderSettlementVO) {
        RideOrderSettlementDO orderSettlementDO=mapping.v2d(rideOrderSettlementVO);
        orderSettlementDO.setUpdateTime(new Date());
        if(rideOrderSettlementVO.getId()!=null&&rideOrderSettlementVO.getId()>0){
            orderSettlementDO.setOldDriverSettlementPrice(null);
            rideOrderSettlementMapper.updateById(orderSettlementDO);
        }else {
            orderSettlementDO.setOldDriverSettlementPrice(orderSettlementDO.getDriverSettlementPrice());
            rideOrderSettlementMapper.insert(orderSettlementDO);
        }

    }

    @Override
    public BigDecimal calculationDriverRefundSettlement(BigDecimal refund, RideOrderSettlementVO orderSettlementVO) {
        return refund.multiply(BigDecimal.ONE.subtract(orderSettlementVO.getPlatformMaidRate())
                .subtract(orderSettlementVO.getSupplierMaidRate()))
                .setScale(2, BigDecimal.ROUND_DOWN);

    }


}
