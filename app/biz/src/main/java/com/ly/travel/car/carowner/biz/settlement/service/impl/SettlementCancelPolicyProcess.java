package com.ly.travel.car.carowner.biz.settlement.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.travel.car.carowner.biz.driver.account.enums.ChangeDriverAmountTypeEnum;
import com.ly.travel.car.carowner.biz.driver.account.service.ChangeDriverAmountProcess;
import com.ly.travel.car.carowner.biz.driver.bill.mapper.DriverBillMapping;
import com.ly.travel.car.carowner.biz.driver.bill.model.DriverBillVO;
import com.ly.travel.car.carowner.biz.driver.bill.service.DriverBillService;
import com.ly.travel.car.carowner.biz.driver.coupon.service.DriverCouponService;
import com.ly.travel.car.carowner.biz.order.model.RideOrderInfoVO;
import com.ly.travel.car.carowner.biz.order.model.RideOrderSettlementVO;
import com.ly.travel.car.carowner.biz.settlement.service.RideOrderSettlementService;
import com.ly.travel.car.carowner.biz.settlement.service.SettlementPolicyProcess;
import com.ly.travel.car.carowner.biz.turbomq.model.DriverBillUnFreezeMqModel;
import com.ly.travel.car.carowner.biz.turbomq.model.DriverCouponPayload;
import com.ly.travel.car.carowner.biz.turbomq.producer.DriverBillUnFreezeProducer;
import com.ly.travel.car.carowner.common.enums.BillTypeEnum;
import com.ly.travel.car.carowner.common.enums.FreezeTypeEnum;
import com.ly.travel.car.carowner.common.enums.IsFreezeEnum;
import com.ly.travel.car.carowner.common.enums.VoucherTypeEnum;
import com.ly.travel.car.carowner.common.exception.OrderSettlementException;
import com.ly.travel.car.carowner.common.utils.DateUtil;
import com.ly.travel.car.carowner.common.utils.EnvUtil;
import com.ly.travel.car.carowner.dal.dataobject.DriverWithdrawalSettingDO;
import com.ly.travel.car.carowner.dal.mapper.DriverWithdrawalSettingMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component("settlementCancelPolicyProcess")
public class SettlementCancelPolicyProcess extends SettlementCompletedPolicyProcess {
    @Resource
    private DriverBillUnFreezeProducer driverBillUnFreezeProducer;
    @Autowired
    private RideOrderSettlementService rideOrderSettlementService;
    @Autowired
    private DriverBillService driverBillService;
    @Resource
    private DriverWithdrawalSettingMapper driverWithdrawalSettingMapper;
    @Autowired
    private DriverBillMapping driverBillMapping;
    @Autowired
    private DriverCouponService driverCouponService;

    @Override
    public RideOrderSettlementVO beforeProcess(RideOrderInfoVO rideOrderInfoVO, Map<String, Object> extend) {
        RideOrderSettlementVO old_orderSettlement = rideOrderSettlementService.queryByOrderNo(rideOrderInfoVO.getOrderNo());

        log.info("历史结算数据为:{}", JSON.toJSONString(old_orderSettlement));
        if(old_orderSettlement==null){
            log.warn("派单时结算数据不存在，不能取消结算，{}",rideOrderInfoVO.getOrderNo());
            throw new OrderSettlementException("派单时结算数据不存在，不能取消结算");
        }
        BigDecimal supplyMaidRate = old_orderSettlement.getSupplierMaidRate();
        BigDecimal SupplierSettlementPrice = null;
        BigDecimal orderAmount = rideOrderInfoVO.getAmount();
        BigDecimal cancelAmount = orderAmount; //目前是全费取消
        if (cancelAmount != null) {
            orderAmount = orderAmount.subtract(cancelAmount);
        }

        SupplierSettlementPrice = orderAmount.multiply(supplyMaidRate).setScale(2, BigDecimal.ROUND_DOWN);

        if (SupplierSettlementPrice.compareTo(old_orderSettlement.getSupplierLimitMaxAmount()) > 0) {
            SupplierSettlementPrice = old_orderSettlement.getSupplierLimitMaxAmount();
        }
        RideOrderSettlementVO orderSettlement = new RideOrderSettlementVO();
        orderSettlement.setId(old_orderSettlement.getId());
        orderSettlement.setOrderNo(rideOrderInfoVO.getOrderNo());
        orderSettlement.setSupplierSettlementPrice(SupplierSettlementPrice);
        orderSettlement.setDriverId(rideOrderInfoVO.getDriverId());
        orderSettlement.setPlatformSettlementPrice(orderAmount.multiply(rideOrderInfoVO.getOrderCommissionRatio()).setScale(2, BigDecimal.ROUND_DOWN));
        orderSettlement.setPlatformMaidRate(rideOrderInfoVO.getOrderCommissionRatio());
        orderSettlement.setDriverSettlementPrice(orderAmount.subtract(orderSettlement.getPlatformSettlementPrice()).subtract(orderSettlement.getSupplierSettlementPrice()));
        orderSettlement.setDriverExtraSettlementPrice(BigDecimal.ZERO);
        return orderSettlement;
    }

    public RideOrderSettlementVO afterProcess(RideOrderInfoVO rideOrderInfo, RideOrderSettlementVO orderSettlement, Map<String, Object> extend) {
        if(orderSettlement.getDriverExtraSettlementPrice()!=null||orderSettlement.getDriverExtraSettlementPrice().compareTo(BigDecimal.ZERO)>0){
            log.info("完单取消后，退还免佣卡，{},{}",rideOrderInfo.getOrderNo(),orderSettlement.getDriverExtraSettlementPrice());
            driverCouponService.unfreeze(new DriverCouponPayload(rideOrderInfo.getDriverId(),rideOrderInfo.getOrderNo()));
        }
        BigDecimal driverSettlementPrice=orderSettlement.getDriverSettlementPrice();
        if(driverSettlementPrice==null||driverSettlementPrice.compareTo(BigDecimal.ZERO)<=0) {
            log.warn("全损取消，不需要给司机收益，{}", rideOrderInfo.getOrderNo());
            return orderSettlement;
        }
        log.info("取消订单后收益,{}",rideOrderInfo.getOrderNo());
        return super.afterProcess(rideOrderInfo, orderSettlement, extend);
    }
}
