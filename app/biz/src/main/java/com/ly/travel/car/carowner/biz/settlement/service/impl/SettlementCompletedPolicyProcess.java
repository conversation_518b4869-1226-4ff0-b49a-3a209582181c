package com.ly.travel.car.carowner.biz.settlement.service.impl;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.travel.car.carowner.biz.driver.account.enums.ChangeDriverAmountTypeEnum;
import com.ly.travel.car.carowner.biz.driver.account.service.ChangeDriverAmountProcess;
import com.ly.travel.car.carowner.biz.driver.bill.mapper.DriverBillMapping;
import com.ly.travel.car.carowner.biz.driver.coupon.service.DriverCouponService;
import com.ly.travel.car.carowner.biz.settlement.service.FinanceSettlementService;
import com.ly.travel.car.carowner.biz.turbomq.model.DriverCouponPayload;
import com.ly.travel.car.carowner.biz.turbomq.producer.DriverBillUnFreezeProducer;
import com.ly.travel.car.carowner.common.cache.CacheUtils;
import com.ly.travel.car.carowner.common.enums.*;

import com.ly.travel.car.carowner.biz.driver.bill.model.DriverBillVO;
import com.ly.travel.car.carowner.biz.driver.bill.service.DriverBillService;
import com.ly.travel.car.carowner.biz.order.model.RideOrderInfoVO;
import com.ly.travel.car.carowner.biz.order.model.RideOrderSettlementVO;
import com.ly.travel.car.carowner.biz.settlement.service.RideOrderSettlementService;
import com.ly.travel.car.carowner.biz.settlement.service.SettlementPolicyProcess;
import com.ly.travel.car.carowner.common.exception.OrderSettlementException;
import com.ly.travel.car.carowner.common.utils.DateUtil;
import com.ly.travel.car.carowner.common.utils.EnvUtil;
import com.ly.travel.car.carowner.dal.dataobject.DriverWithdrawalSettingDO;
import com.ly.travel.car.carowner.dal.mapper.DriverWithdrawalSettingMapper;
import com.ly.travel.car.carowner.facade.request.UnfreezeDriverBillRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component("settlementCompletedPolicyProcess")
public class SettlementCompletedPolicyProcess extends SettlementPolicyProcess {

    @Resource
    private DriverBillUnFreezeProducer driverBillUnFreezeProducer;
    @Resource
    private DriverWithdrawalSettingMapper driverWithdrawalSettingMapper;
    @Autowired
    private RideOrderSettlementService rideOrderSettlementService;
    @Autowired
    private DriverBillMapping driverBillMapping;

    @Autowired
    private DriverBillService driverBillService;

    @Autowired
    private FinanceSettlementService financeSettlementService;
    @Autowired
    private DriverCouponService driverCouponService;

    @Override
    public RideOrderSettlementVO beforeProcess(RideOrderInfoVO rideOrderInfoVO, Map<String,Object> extend) {
        RideOrderSettlementVO old_orderSettlement = rideOrderSettlementService.queryByOrderNo(rideOrderInfoVO.getOrderNo());
        log.info("历史结算数据为:{}", JSON.toJSONString(old_orderSettlement));
        if(old_orderSettlement==null){
            log.warn("派单时结算数据不存在，不能完单结算，{}",rideOrderInfoVO.getOrderNo());
            throw new OrderSettlementException("派单时结算数据不存在，不能完单结算");
        }

        BigDecimal supplyMaidRate = old_orderSettlement.getSupplierMaidRate();
        BigDecimal supplierSettlementPrice=null;
        BigDecimal orderAmount = rideOrderInfoVO.getAmount();

        supplierSettlementPrice=orderAmount.multiply(supplyMaidRate).setScale(2, BigDecimal.ROUND_DOWN);

        if(supplierSettlementPrice.compareTo(old_orderSettlement.getSupplierLimitMaxAmount())>0){
            supplierSettlementPrice=old_orderSettlement.getSupplierLimitMaxAmount();
        }
        RideOrderSettlementVO orderSettlement = new RideOrderSettlementVO();
        orderSettlement.setId(old_orderSettlement.getId());
        orderSettlement.setOrderNo(rideOrderInfoVO.getOrderNo());
        orderSettlement.setSupplierSettlementPrice(supplierSettlementPrice);
        orderSettlement.setDriverId(rideOrderInfoVO.getDriverId());
        orderSettlement.setPlatformSettlementPrice(orderAmount.multiply(rideOrderInfoVO.getOrderCommissionRatio()).setScale(2, BigDecimal.ROUND_DOWN));
        orderSettlement.setPlatformMaidRate(rideOrderInfoVO.getOrderCommissionRatio());
        orderSettlement.setDriverSettlementPrice(orderAmount.subtract(orderSettlement.getPlatformSettlementPrice()).subtract(orderSettlement.getSupplierSettlementPrice()));
        orderSettlement.setDriverExtraSettlementPrice(old_orderSettlement.getDriverExtraSettlementPrice());
        return orderSettlement;
    }


    @Override
    public RideOrderSettlementVO afterProcess(RideOrderInfoVO rideOrderInfo, RideOrderSettlementVO orderSettlement, Map<String, Object> extend) {


        if(orderSettlement.getDriverSettlementPrice().compareTo(BigDecimal.ZERO)<=0){
            log.warn("正常完单，司机没有结算金额或小于0，异常,{},{}",rideOrderInfo.getOrderNo(),orderSettlement.getDriverSettlementPrice());
            return orderSettlement;
        }

        if(!CacheUtils.lockKey(CacheKeyEnum.CREATE_BILL_Z_LOCK.format(rideOrderInfo.getOrderNo()),"1", CacheKeyEnum.CREATE_BILL_Z_LOCK.getExpire())){
            log.warn("正常完单后，同一账单类型不能结算两次账单，异常,{},{}",rideOrderInfo.getOrderNo(),orderSettlement.getDriverSettlementPrice());
            return null;
        }


        createOrderBill(rideOrderInfo, orderSettlement);
        createCardOrderBill(rideOrderInfo, orderSettlement);


        return orderSettlement;
    }

    private void createOrderBill(RideOrderInfoVO rideOrderInfo, RideOrderSettlementVO orderSettlement) {
        DriverBillVO driverBillVO=new DriverBillVO();
        driverBillVO.setDriverId(orderSettlement.getDriverId());
        driverBillVO.setVoucherNo(rideOrderInfo.getOrderNo());
        driverBillVO.setVoucherType(VoucherTypeEnum.DRIVER_ORDER_SETTLEMENT);
        driverBillVO.setBillType(BillTypeEnum.INCOME);
        driverBillVO.setAmount(orderSettlement.getDriverSettlementPrice());
        driverBillVO.setIsFreeze(IsFreezeEnum.CORRECT);
        driverBillVO.setRemark(rideOrderInfo.getOrderNo()+"订单收益");

        DriverWithdrawalSettingDO withdrawalSetting= driverWithdrawalSettingMapper.selectOne(new LambdaQueryWrapper<DriverWithdrawalSettingDO>()
                .eq(DriverWithdrawalSettingDO::getEnv, EnvUtil.getEnv().getValue()),false);

        int limitHour =  withdrawalSetting.getLimitHour();
        if(limitHour>=0){
            driverBillVO.setUnfreezeTime(DateUtil.addHour(new Date(),limitHour));
        }
        driverBillVO.setBillNo(driverBillService.createDriverBill(driverBillVO));


        ChangeDriverAmountProcess.getInstance(ChangeDriverAmountTypeEnum.ORDER_COMPLETE).changeAmount_sync(orderSettlement.getDriverId(), orderSettlement.getDriverSettlementPrice() ,driverBillVO.getBillNo());


        driverBillService.insertOrUpdateDriverBillFreeze(driverBillMapping.v2d(driverBillVO), FreezeTypeEnum.NORMAL_FREEZE,driverBillVO.getUnfreezeTime(),null);

        UnfreezeDriverBillRequest driverBillUnFreezeMqModel=new UnfreezeDriverBillRequest();
        driverBillUnFreezeMqModel.setBillNos(Arrays.asList(driverBillVO.getBillNo()));
        driverBillUnFreezeMqModel.setUnFreezeTypes(Arrays.asList(FreezeTypeEnum.NORMAL_FREEZE.getType()));
        driverBillUnFreezeMqModel.setOperator("系统定时");

        String msg=driverBillUnFreezeProducer.convertMsg(driverBillUnFreezeMqModel);
        try {
            driverBillUnFreezeProducer.sendDelayMsg(driverBillUnFreezeProducer.getTopicName(),msg,limitHour>0?limitHour:0, TimeUnit.HOURS);
        } catch (Exception e) {
            log.error("延时解冻司机账单发送MQ失败，",e);
        }
    }
    private void createCardOrderBill(RideOrderInfoVO rideOrderInfo, RideOrderSettlementVO orderSettlement) {
        if(orderSettlement.getDriverExtraSettlementPrice()==null||orderSettlement.getDriverExtraSettlementPrice().compareTo(BigDecimal.ZERO)<=0){
            log.info("正常完单后，没有免佣卡结算，{},{}",rideOrderInfo.getOrderNo(),orderSettlement.getDriverExtraSettlementPrice());
            return;
        }
        log.info("订单完单时核销免佣卡,{}，{}",rideOrderInfo.getOrderNo(),orderSettlement.getDriverExtraSettlementPrice());

        driverCouponService.used(new DriverCouponPayload(rideOrderInfo.getDriverId(),rideOrderInfo.getOrderNo()));

        financeSettlementService.sendFreeCommissionSettlement(rideOrderInfo.getOrderNo(),rideOrderInfo.getDistributorMainOrderNo(),orderSettlement.getDriverExtraSettlementPrice());

        DriverBillVO driverBillVO=new DriverBillVO();
        driverBillVO.setDriverId(orderSettlement.getDriverId());
        driverBillVO.setVoucherNo(rideOrderInfo.getOrderNo());
        driverBillVO.setVoucherType(VoucherTypeEnum.DRIVER_ORDER_FREE_COMMISSION);
        driverBillVO.setBillType(BillTypeEnum.INCOME);
        driverBillVO.setAmount(orderSettlement.getDriverExtraSettlementPrice());
        driverBillVO.setIsFreeze(IsFreezeEnum.CORRECT);
        driverBillVO.setRemark(rideOrderInfo.getOrderNo()+"免佣卡订单收益");

        DriverWithdrawalSettingDO withdrawalSetting= driverWithdrawalSettingMapper.selectOne(new LambdaQueryWrapper<DriverWithdrawalSettingDO>()
                .eq(DriverWithdrawalSettingDO::getEnv, EnvUtil.getEnv().getValue()),false);

        int limitHour =  withdrawalSetting.getLimitHour();
        if(limitHour>=0){
            driverBillVO.setUnfreezeTime(DateUtil.addHour(new Date(),limitHour));
        }
        driverBillVO.setBillNo(driverBillService.createDriverBill(driverBillVO));


        ChangeDriverAmountProcess.getInstance(ChangeDriverAmountTypeEnum.CARD_ORDER_COMPLETE).changeAmount_sync(orderSettlement.getDriverId(), orderSettlement.getDriverExtraSettlementPrice(),driverBillVO.getBillNo());


        driverBillService.insertOrUpdateDriverBillFreeze(driverBillMapping.v2d(driverBillVO), FreezeTypeEnum.NORMAL_FREEZE,driverBillVO.getUnfreezeTime(),null);

        UnfreezeDriverBillRequest driverBillUnFreezeMqModel=new UnfreezeDriverBillRequest();
        driverBillUnFreezeMqModel.setBillNos(Arrays.asList(driverBillVO.getBillNo()));
        driverBillUnFreezeMqModel.setUnFreezeTypes(Arrays.asList(FreezeTypeEnum.NORMAL_FREEZE.getType()));
        driverBillUnFreezeMqModel.setOperator("系统定时");

        String msg=driverBillUnFreezeProducer.convertMsg(driverBillUnFreezeMqModel);
        try {
            driverBillUnFreezeProducer.sendDelayMsg(driverBillUnFreezeProducer.getTopicName(),msg,limitHour>0?limitHour:0, TimeUnit.HOURS);
        } catch (Exception e) {
            log.error("延时解冻司机账单-卡卷-发送MQ失败，",e);
        }
    }
}
