package com.ly.travel.car.carowner.biz.settlement.service.impl;

import com.alibaba.fastjson.JSON;
import com.ly.travel.car.carowner.biz.driver.coupon.service.DriverCouponService;
import com.ly.travel.car.carowner.biz.order.model.RideOrderInfoVO;
import com.ly.travel.car.carowner.biz.order.model.RideOrderSettlementVO;
import com.ly.travel.car.carowner.biz.order.service.RideOrderInfoService;
import com.ly.travel.car.carowner.biz.order.service.RideOrderTagsService;
import com.ly.travel.car.carowner.biz.settlement.model.CommissionSettingVO;
import com.ly.travel.car.carowner.biz.settlement.service.CommissionSettingService;
import com.ly.travel.car.carowner.biz.settlement.service.RideOrderSettlementService;
import com.ly.travel.car.carowner.biz.settlement.service.SettlementPolicyProcess;
import com.ly.travel.car.carowner.biz.turbomq.model.DriverCouponPayload;
import com.ly.travel.car.carowner.common.enums.OrderTagEnum;
import com.ly.travel.car.carowner.common.enums.OrderTypeEnum;
import com.ly.travel.car.carowner.facade.response.CarOwnerResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Map;

@Slf4j
@Component("settlementReceivedOrderPolicyProcess")
public class SettlementReceivedOrderPolicyProcess extends SettlementPolicyProcess {
    @Autowired
    private CommissionSettingService commissionSettingService;
    @Autowired
    private RideOrderSettlementService rideOrderSettlementService;
    @Autowired
    private RideOrderInfoService rideOrderInfoService;
    @Autowired
    private RideOrderTagsService rideOrderTagsService;
    @Autowired
    private DriverCouponService driverCouponService;

    @Override
    public RideOrderSettlementVO beforeProcess(RideOrderInfoVO rideOrderInfoVO, Map<String,Object> extend) {

        CommissionSettingVO commissionSettingVO=commissionSettingService.queryAvailableCommission();
        BigDecimal supplyMaidRate =null;
        BigDecimal supplierSettlementPrice=null;
        BigDecimal orderAmount = rideOrderInfoVO.getAmount();
        BigDecimal supplierLimitMaxAmount=null;
        if(rideOrderInfoVO.getOrderType()== OrderTypeEnum.NOT_POOL.getType()){
            supplyMaidRate=commissionSettingVO.getCommRateAll();//查抽佣金额
            supplierLimitMaxAmount=commissionSettingVO.getCommLimitAll();

            supplierSettlementPrice=orderAmount.multiply(supplyMaidRate).setScale(2, BigDecimal.ROUND_DOWN);
            if(supplierSettlementPrice.compareTo(commissionSettingVO.getCommLimitAll())>0){
                supplierSettlementPrice=commissionSettingVO.getCommLimitAll();
            }
        }else  if(rideOrderInfoVO.getOrderType()== OrderTypeEnum.POOL.getType()){
            supplyMaidRate=commissionSettingVO.getCommRateJoin();//查抽佣金额
            supplierLimitMaxAmount=commissionSettingVO.getCommLimitJoin();

            supplierSettlementPrice=orderAmount.multiply(supplyMaidRate).setScale(2, BigDecimal.ROUND_DOWN);
            if(supplierSettlementPrice.compareTo(commissionSettingVO.getCommLimitJoin())>0){
                supplierSettlementPrice=commissionSettingVO.getCommLimitJoin();
            }
        }
        BigDecimal platformSettlementPrice=orderAmount.multiply(rideOrderInfoVO.getOrderCommissionRatio()).setScale(2, BigDecimal.ROUND_DOWN);

        RideOrderSettlementVO orderSettlement = new RideOrderSettlementVO();
        orderSettlement.setOrderNo(rideOrderInfoVO.getOrderNo());
        orderSettlement.setSupplierSettlementPrice(supplierSettlementPrice);
        orderSettlement.setDriverId(rideOrderInfoVO.getDriverId());
        orderSettlement.setPlatformSettlementPrice(platformSettlementPrice);
        orderSettlement.setSupplierMaidRate(supplyMaidRate);
        orderSettlement.setSupplierLimitMaxAmount(supplierLimitMaxAmount);
        orderSettlement.setPlatformMaidRate(rideOrderInfoVO.getOrderCommissionRatio());
        orderSettlement.setDriverSettlementPrice(orderAmount.subtract(orderSettlement.getPlatformSettlementPrice()).subtract(orderSettlement.getSupplierSettlementPrice()));
        //占用卷，并且修改金额
        CarOwnerResponseDTO<Boolean>  cardFreezeResult= driverCouponService.freeze(rideOrderInfoVO.getOrderNo(),rideOrderInfoVO.getDriverId());
        if(cardFreezeResult!=null&&cardFreezeResult.getData()!=null&&cardFreezeResult.getData()) {
            log.info("订单接单时使用了免佣卡,{}，{}",rideOrderInfoVO.getOrderNo(),platformSettlementPrice);
            orderSettlement.setDriverExtraSettlementPrice(platformSettlementPrice);
            rideOrderTagsService.addTag(rideOrderInfoVO.getOrderNo(), OrderTagEnum.FREE_COMMISSION_CARD.getCode(), rideOrderInfoVO.getId());
        }

        log.info("订单:{},结果信息为,{},",rideOrderInfoVO.getOrderNo(), JSON.toJSONString(orderSettlement));
         return orderSettlement;
    }



    @Override
    public RideOrderSettlementVO afterProcess(RideOrderInfoVO rideOrderInfo, RideOrderSettlementVO orderSettlement, Map<String,Object> extend) {

        return orderSettlement;
    }
}
