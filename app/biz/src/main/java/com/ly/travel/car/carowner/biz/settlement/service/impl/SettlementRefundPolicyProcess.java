package com.ly.travel.car.carowner.biz.settlement.service.impl;

import com.alibaba.fastjson.JSON;
import com.ly.travel.car.carowner.biz.driver.account.enums.ChangeDriverAmountTypeEnum;
import com.ly.travel.car.carowner.biz.driver.account.service.ChangeDriverAmountProcess;
import com.ly.travel.car.carowner.biz.driver.bill.model.DriverBillVO;
import com.ly.travel.car.carowner.biz.driver.bill.service.DriverBillService;
import com.ly.travel.car.carowner.biz.driver.coupon.service.DriverCouponService;
import com.ly.travel.car.carowner.biz.order.model.RideOrderInfoVO;
import com.ly.travel.car.carowner.biz.order.model.RideOrderSettlementVO;
import com.ly.travel.car.carowner.biz.order.service.RideOrderLogService;
import com.ly.travel.car.carowner.biz.order.service.RideOrderRefundService;
import com.ly.travel.car.carowner.biz.settlement.service.RideOrderSettlementService;
import com.ly.travel.car.carowner.biz.settlement.service.SettlementPolicyProcess;
import com.ly.travel.car.carowner.common.cache.CacheUtils;
import com.ly.travel.car.carowner.common.enums.*;
import com.ly.travel.car.carowner.common.exception.OrderSettlementException;
import com.ly.travel.car.carowner.dal.dataobject.DriverBillDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
@Slf4j
@Component("settlementRefundPolicyProcess")
public class SettlementRefundPolicyProcess extends SettlementPolicyProcess {

    @Autowired
    private RideOrderSettlementService rideOrderSettlementService;
    @Autowired
    private RideOrderRefundService rideOrderRefundService;
    @Autowired
    private DriverBillService driverBillService;
    @Autowired
    private RideOrderLogService rideOrderLogService;

    @Override
    public RideOrderSettlementVO beforeProcess(RideOrderInfoVO rideOrderInfoVO, Map<String,Object> extend) {
        RideOrderSettlementVO old_orderSettlement = rideOrderSettlementService.queryByOrderNo(rideOrderInfoVO.getOrderNo());
        log.info("历史结算数据为:{}", JSON.toJSONString(old_orderSettlement));

        if(old_orderSettlement==null){
            log.warn("派单时结算数据不存在，不能退款结算，{}",rideOrderInfoVO.getOrderNo());
            throw new OrderSettlementException("派单时结算数据不存在，不能退款结算");
        }

        BigDecimal supplyMaidRate = old_orderSettlement.getSupplierMaidRate();
        BigDecimal SupplierSettlementPrice=null;
        BigDecimal orderAmount = rideOrderInfoVO.getAmount();
        BigDecimal refundAmount=rideOrderRefundService.queryRefundAmountByOrderNo(rideOrderInfoVO.getOrderNo());
        if(refundAmount==null||refundAmount.compareTo(BigDecimal.ZERO)<=0){
            log.error("退款金额不正确，不能结算,{},金额，{}",rideOrderInfoVO.getOrderNo(),refundAmount);
            throw new OrderSettlementException("退款金额不正确，不能结算");
        }
        extend.put(refundAmount_key, refundAmount);

        orderAmount=orderAmount.subtract(refundAmount);
        SupplierSettlementPrice=orderAmount.multiply(supplyMaidRate).setScale(2, BigDecimal.ROUND_DOWN);

        if(SupplierSettlementPrice.compareTo(old_orderSettlement.getSupplierLimitMaxAmount())>0){
            SupplierSettlementPrice=old_orderSettlement.getSupplierLimitMaxAmount();
        }
        RideOrderSettlementVO orderSettlement = new RideOrderSettlementVO();
        orderSettlement.setId(old_orderSettlement.getId());
        orderSettlement.setOrderNo(rideOrderInfoVO.getOrderNo());
        orderSettlement.setSupplierSettlementPrice(SupplierSettlementPrice);
        orderSettlement.setDriverId(rideOrderInfoVO.getDriverId());
        orderSettlement.setPlatformSettlementPrice(orderAmount.multiply(rideOrderInfoVO.getOrderCommissionRatio()).setScale(2, BigDecimal.ROUND_DOWN));
        orderSettlement.setPlatformMaidRate(rideOrderInfoVO.getOrderCommissionRatio());
        orderSettlement.setDriverSettlementPrice(orderAmount.subtract(orderSettlement.getPlatformSettlementPrice()).subtract(orderSettlement.getSupplierSettlementPrice()));

        if(old_orderSettlement.getDriverExtraSettlementPrice()!=null&&old_orderSettlement.getDriverExtraSettlementPrice().compareTo(BigDecimal.ZERO)>0){
            extend.put(driverExtraSettlement_key, old_orderSettlement.getDriverExtraSettlementPrice().negate());
            orderSettlement.setDriverExtraSettlementPrice(BigDecimal.ZERO);
        }
        return orderSettlement;
    }

    private final static String refundAmount_key="refundAmount";
    private final static String driverExtraSettlement_key="driverExtraSettlement";

    @Override
    public RideOrderSettlementVO afterProcess(RideOrderInfoVO rideOrderInfo, RideOrderSettlementVO orderSettlement, Map<String, Object> extend) {
        BigDecimal refund_driverSettlementPrice=getDriverAccountChangedAmount(orderSettlement);
        if(refund_driverSettlementPrice==null||refund_driverSettlementPrice.compareTo(BigDecimal.ZERO)>=0){
            log.warn("不需要从司机扣款，{}",rideOrderInfo.getOrderNo());
            return orderSettlement;
        }
        if(!CacheUtils.lockKey(CacheKeyEnum.CREATE_BILL_F_LOCK.format(rideOrderInfo.getOrderNo()),"1", CacheKeyEnum.CREATE_BILL_F_LOCK.getExpire())){
            log.error("正常完单后，同一账单类型不能结算两次账单，异常,{},{}",rideOrderInfo.getOrderNo(),orderSettlement.getDriverSettlementPrice());
            return null;
        }

        //扣司机钱
        DriverBillVO driverBillVO=new DriverBillVO();
        driverBillVO.setDriverId(orderSettlement.getDriverId());
        driverBillVO.setVoucherNo(rideOrderInfo.getOrderNo());
        driverBillVO.setVoucherType(VoucherTypeEnum.DRIVER_ORDER_SETTLEMENT);
        driverBillVO.setBillType(BillTypeEnum.EXPENSES);
        driverBillVO.setAmount(refund_driverSettlementPrice);
        driverBillVO.setIsFreeze(IsFreezeEnum.DENY);
        //不需要冻结
        driverBillVO.setRemark(rideOrderInfo.getOrderNo()+"订单发生退款");
        driverBillService.createDriverBill(driverBillVO);

        ChangeDriverAmountProcess.getInstance(ChangeDriverAmountTypeEnum.ORDER_REFUND).changeAmount_sync(orderSettlement.getDriverId(),refund_driverSettlementPrice,driverBillVO.getBillNo());

        BigDecimal driverExtraSettlementPrice=(BigDecimal)extend.get(driverExtraSettlement_key);

        if(driverExtraSettlementPrice!=null){
            log.info("订单退款时退掉免佣金额,{}，{}",rideOrderInfo.getOrderNo(),driverExtraSettlementPrice);

            //扣司机钱
            DriverBillVO driverBillVO_Free=new DriverBillVO();
            driverBillVO_Free.setDriverId(orderSettlement.getDriverId());
            driverBillVO_Free.setVoucherNo(rideOrderInfo.getOrderNo());
            driverBillVO_Free.setVoucherType(VoucherTypeEnum.DRIVER_ORDER_FREE_COMMISSION);
            driverBillVO_Free.setBillType(BillTypeEnum.EXPENSES);
            driverBillVO_Free.setAmount(driverExtraSettlementPrice);
            driverBillVO_Free.setIsFreeze(IsFreezeEnum.DENY);
            //不需要冻结
            driverBillVO_Free.setRemark(rideOrderInfo.getOrderNo()+"订单免佣卡发生退款");
            driverBillService.createDriverBill(driverBillVO_Free);

            ChangeDriverAmountProcess.getInstance(ChangeDriverAmountTypeEnum.CARD_ORDER_REFUND).changeAmount_sync(orderSettlement.getDriverId(),driverExtraSettlementPrice,driverBillVO_Free.getBillNo());

            rideOrderLogService.addLog(rideOrderInfo.getOrderNo(), OrderLogTypeEnum.CARD_REFUND_ORDER,OrderLogTypeEnum.CARD_REFUND_ORDER.formatRemark(driverExtraSettlementPrice),"sys");
        }

        return orderSettlement;
    }


    private BigDecimal getDriverAccountChangedAmount(RideOrderSettlementVO orderSettlement) {
        BigDecimal driverSettlementPrice = orderSettlement.getDriverSettlementPrice();
        String voucherNo=orderSettlement.getOrderNo();
        List<DriverBillDO> driverBillList =
                driverBillService.findDriverBillByOrderNo(voucherNo);
        BigDecimal totalBillAmount =
                driverBillList.stream().filter(item->item.getVoucherType()==VoucherTypeEnum.DRIVER_ORDER_SETTLEMENT.getType()).map(item -> item.getAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (totalBillAmount.compareTo(BigDecimal.ZERO) <= 0) {
            log.error("订单：{}流水总金额：{}不正确，没有司机收入流水", voucherNo, totalBillAmount);
//            throw new OrderSettlementException("订单：" + voucherNo + "，流水总金额：" + totalBillAmount + "不正确");
        }
        if (driverSettlementPrice.compareTo(totalBillAmount) >= 0) {
            log.warn("订单：{}，司机结算价：{}不小于流水总金额：{}，无需冲正", voucherNo, driverSettlementPrice, totalBillAmount);
        }

        BigDecimal accountChangedAmount = driverSettlementPrice.subtract(totalBillAmount);
        log.info("订单：{}，司机结算价：{}，流水总金额：{}，账户变动金额：{}", voucherNo, driverSettlementPrice, totalBillAmount,
                accountChangedAmount);
        return accountChangedAmount;
    }
}
