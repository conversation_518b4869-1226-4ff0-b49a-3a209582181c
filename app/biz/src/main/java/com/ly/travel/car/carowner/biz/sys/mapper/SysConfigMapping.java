/**
* SysConfigMapping
* This file is generated by <tt>toolkit(sof-batis-gen)</tt>, a DAL (Data Access Layer)
* @version Id : SysConfigMapping, v 0.1
*/
package com.ly.travel.car.carowner.biz.sys.mapper;

import com.ly.flight.toolkit.converter.BooleanConverter;
import com.ly.flight.toolkit.converter.FullDateConverter;
import com.ly.travel.car.carowner.biz.sys.model.SysConfigVO;
import com.ly.travel.car.carowner.dal.dataobject.SysConfigDO;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueMappingStrategy;

import java.util.Collection;
import java.util.List;

@Mapper(componentModel = "spring", nullValueMappingStrategy = NullValueMappingStrategy.RETURN_NULL, uses = { FullDateConverter.class, BooleanConverter.class })
public interface SysConfigMapping {

SysConfigDO v2d(SysConfigVO v);

SysConfigVO d2v(SysConfigDO d);

List<SysConfigVO> d2v(Collection<SysConfigDO> d);

List<SysConfigDO> v2d(Collection<SysConfigVO> v);

}
