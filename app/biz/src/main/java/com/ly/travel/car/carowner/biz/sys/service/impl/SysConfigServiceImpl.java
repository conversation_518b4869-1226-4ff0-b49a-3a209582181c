/**
 * SysConfigServiceImpl
 * This file is generated by <tt>toolkit(sof-batis-gen)</tt>, a DAL (Data Access Layer)
 *
 * @version Id : SysConfigServiceImpl, v 0.1
 */
package com.ly.travel.car.carowner.biz.sys.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ly.spat.dsf.client.utils.EnvUtils;
import com.ly.travel.car.carowner.biz.sys.mapper.SysConfigMapping;
import com.ly.travel.car.carowner.biz.sys.model.SysConfigVO;
import com.ly.travel.car.carowner.biz.sys.service.SysConfigService;
import com.ly.travel.car.carowner.common.cache.CacheUtils;
import com.ly.travel.car.carowner.common.enums.CacheKeyEnum;
import com.ly.travel.car.carowner.common.enums.EnvEnum;
import com.ly.travel.car.carowner.common.enums.StatusEnum;
import com.ly.travel.car.carowner.common.enums.SysConfigEnum;
import com.ly.travel.car.carowner.common.utils.EnvUtil;
import com.ly.travel.car.carowner.dal.dataobject.SysConfigDO;
import com.ly.travel.car.carowner.dal.mapper.SysConfigMapper;
import com.ly.travel.car.carowner.integration.wx.IAccessTokenFunction;
import com.ly.travel.car.carowner.integration.wxwork.IGetWxWorkUrl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

@Service
public class SysConfigServiceImpl implements SysConfigService, IAccessTokenFunction , IGetWxWorkUrl {

    private static final Logger logger = LoggerFactory.getLogger(SysConfigService.class);

    @Autowired
    private SysConfigMapper sysConfigMapper;
    @Autowired
    private SysConfigMapping mapping;


    /**
     * 从系统配置表 获取最终检查点的时间间隔，没有的话使用枚举中的默认值
     *
     * @return Double
     */
    public Double getDoubleCachedCfgValue(SysConfigEnum cfgEnum) {
        String limitTimeConfigValue = getCachedConfigValue_0(cfgEnum.getKey());
        return StringUtils.isBlank(limitTimeConfigValue) ? Double.valueOf(cfgEnum.getValue()) : Double.valueOf(limitTimeConfigValue);
    }

    /**
     * 从系统配置表 获取最终检查点的时间间隔，没有的话使用枚举中的默认值
     *
     * @return
     */
    public Integer getIntCachedCfgValue(SysConfigEnum cfgEnum) {
        String limitTimeConfigValue = getCachedConfigValue_0(cfgEnum.getKey());
        return StringUtils.isBlank(limitTimeConfigValue) ? Integer.valueOf(cfgEnum.getValue()) : Integer.valueOf(limitTimeConfigValue);
    }

    /**
     * 从系统配置表 获取最终检查点的时间间隔，没有的话使用枚举中的默认值
     *
     * @return
     */
    public String getStringCachedCfgValue(SysConfigEnum cfgEnum) {
        String limitTimeConfigValue = getCachedConfigValue_0(cfgEnum.getKey());
        return StringUtils.isBlank(limitTimeConfigValue) ? cfgEnum.getValue() : limitTimeConfigValue;
    }

    public int updateSysConfig(SysConfigEnum cfgEnum,String cfgvalue) {

        int r = sysConfigMapper.update(new LambdaUpdateWrapper<SysConfigDO>()
                .set(SysConfigDO::getCfgValue,cfgvalue)
                .set(SysConfigDO::getUpdateTime,new Date())
                .set(SysConfigDO::getUpdateUser,"sys")
                .eq(SysConfigDO::getCfgKey,cfgEnum.getKey())
                .eq(SysConfigDO::getEnv,EnvUtil.getEnv().getValue())
        );
        CacheUtils.delKey(CacheKeyEnum.SYSCONFIG_CACHE.format(cfgEnum.getKey()));
        return r;
    }

    public SysConfigVO getConfigValue(String key) {
        List<SysConfigDO> configBeans = sysConfigMapper.selectList(new LambdaQueryWrapper<SysConfigDO>()
                .select(SysConfigDO::getCfgKey, SysConfigDO::getCfgValue)
                .eq(SysConfigDO::getEnv, EnvUtil.getEnv().getValue())
                .eq(SysConfigDO::getCfgKey, key)
                .eq(SysConfigDO::getStatus, StatusEnum.ENABLED.getValue()));
        if (CollectionUtils.isEmpty(configBeans)) {
            return null;
        }
        return mapping.d2v(configBeans.get(0));
    }


    public String getDbConfigValue(String key) {
        List<SysConfigDO> configBeans = sysConfigMapper.selectList(new LambdaQueryWrapper<SysConfigDO>()
                .select(SysConfigDO::getCfgKey, SysConfigDO::getCfgValue)
                .eq(SysConfigDO::getEnv, EnvUtil.getEnv().getValue())
                .eq(SysConfigDO::getCfgKey, key)
                .eq(SysConfigDO::getStatus, StatusEnum.ENABLED.getValue()));
        if (CollectionUtils.isEmpty(configBeans)) {
            return null;
        }
        return configBeans.get(0).getCfgValue();
    }



    private String getCachedConfigValue_0(String key) {

        String redisKey = CacheKeyEnum.SYSCONFIG_CACHE.format(key);

        // 从redis缓存获取
        SysConfigVO configBean = getFromRedisCache(redisKey);

        // 没有缓存数据，从DB中获取
        if (null == configBean) {
            // 从DB中获取
            configBean = getConfigValue(key);
            if (null != configBean) {
                // 生产环境缓存10分钟， 测试环境1分钟
                if (environmentIsProduct()) {
                    CacheUtils.setCacheValue(redisKey, JSON.toJSONString(configBean), CacheKeyEnum.SYSCONFIG_CACHE.getExpire());
                } else {
                    CacheUtils.setCacheValue(redisKey, JSON.toJSONString(configBean), 60);
                }
                logger.info("从DB加载数据，写入redis缓存成功！configKey:{} value:{}", redisKey, configBean);
            }
        } else {
            logger.info("从缓存加载数据configKey:{} value:{}", redisKey, configBean);
        }
        return ((configBean == null) ? "" : configBean.getCfgValue());
    }

    private SysConfigVO getFromRedisCache(String redisKey) {
        // 生产环境下使用缓存
        SysConfigVO configBean = null;
        String redisCacheValue = CacheUtils.getCacheValue(redisKey);
        if (StringUtils.isBlank(redisCacheValue)) {
            try {
                configBean = JSON.parseObject(redisCacheValue, SysConfigVO.class);
            } catch (Exception e) {
                logger.error("configKey:{} value:{}, redis缓存数据反序列到SysConfig对象失败！", redisKey, redisCacheValue,
                        e);
            }
        }
        return configBean;
    }

    /**
     * 是否生产环境
     *
     * @return true/false
     */
    private boolean environmentIsProduct() {
        EnvEnum env = EnvUtil.getEnv();

        // 生产环境只判断一次就可以结束了
        if (env == EnvEnum.PRODUCT) {
            return true;
        }

        return false;
    }


    @Override
    public String getAccessToken(String appid) {
        return getCachedConfigValue_0(SysConfigEnum.WXMIN_ACCESSTOKEN.getKey()+appid);
    }
    @Override
    public String getJSAccessToken(String appid) {
        return getCachedConfigValue_0(SysConfigEnum.WXMIN_JS_ACCESSTOKEN.getKey()+appid);
    }

    @Override
    public String getSysConfigWxWorkUrl(String sysconfigKey) {
        return getCachedConfigValue_0(sysconfigKey);
    }
}
