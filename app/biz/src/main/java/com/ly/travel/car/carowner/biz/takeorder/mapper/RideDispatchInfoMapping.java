/**
* RideDispatchInfoMapping
* This file is generated by <tt>toolkit(sof-batis-gen)</tt>, a DAL (Data Access Layer)
* @version Id : RideDispatchInfoMapping, v 0.1
*/
package com.ly.travel.car.carowner.biz.takeorder.mapper;

import com.ly.flight.toolkit.converter.BooleanConverter;
import com.ly.flight.toolkit.converter.FullDateConverter;
import com.ly.sof.utils.page.Paginator;

import com.ly.travel.car.carowner.biz.takeorder.model.RideDispatchInfoVO;
import com.ly.travel.car.carowner.dal.dataobject.*;
import com.ly.travel.car.carowner.dal.operation.ridedispatchinfo.*;

import com.ly.travel.car.carowner.biz.takeorder.model.RideDispatchInfoVO;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.NullValueMappingStrategy;

import java.util.Collection;
import java.util.List;

@Mapper(componentModel = "spring", nullValueMappingStrategy = NullValueMappingStrategy.RETURN_NULL, uses = { FullDateConverter.class, BooleanConverter.class })
public interface RideDispatchInfoMapping {

RideDispatchInfoDO v2d(RideDispatchInfoVO v);

RideDispatchInfoVO d2v(RideDispatchInfoDO d);

List<RideDispatchInfoVO> d2v(Collection<RideDispatchInfoDO> d);

List<RideDispatchInfoDO> v2d(Collection<RideDispatchInfoVO> v);
}
