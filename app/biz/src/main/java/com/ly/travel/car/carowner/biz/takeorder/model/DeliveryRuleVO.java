package com.ly.travel.car.carowner.biz.takeorder.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class DeliveryRuleVO implements Serializable {

    /**
     * 规则ID
     */
    private Long id;

    /**
     * 跨城订单不超过
     */
    private Integer crossCityLimit;

    /**
     * 市内订单不超过
     */
    private Integer cityLimit;

    /**
     * 总订单不超过
     */
    private Integer totalLimit;

    /**
     * 每日取消不超过
     */
    private Integer erverdayCancelShold;

    /**
     * 规则状态(0停用/1启用)
     */
    private Integer status;

    /**
     * 每日不可接单时间
     */
    private String everdayDown;

    /**
     * 每日不可接单时间
     */
    private String everdayUp;

    /**
     * 环境标识
     */
    private String env;

    /**
     * 城市
     */
    private List<CityVO> cityList;

    /**
     * 每日禁止接单时间区间
     */
    private String everDayInterval;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 城市列表
     */
    private String cityListStr;
}

