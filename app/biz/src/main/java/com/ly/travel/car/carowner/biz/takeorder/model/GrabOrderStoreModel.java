package com.ly.travel.car.carowner.biz.takeorder.model;

import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;

@Data
public class GrabOrderStoreModel {
    private String orderNo;
    private String driverTripNo;
    private Long driverId;
    private String driverName;
    private String driverMobile;
    private String vehicleNo;
    private Integer hitchPercent;
    /**
     * 自动接单
     */
    private Integer acceptType;
}
