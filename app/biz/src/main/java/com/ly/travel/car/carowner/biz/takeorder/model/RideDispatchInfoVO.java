/**
* RideDispatchInfoVO
* This file is generated by <tt>toolkit(sof-batis-gen)</tt>, a DAL (Data Access Layer)
* @version Id : RideDispatchInfoVO, v 0.1
*/
package com.ly.travel.car.carowner.biz.takeorder.model;

import java.util.Date;
import lombok.Data;
import com.ly.flight.toolkit.pojo.BaseFormVO;

import com.ly.flight.toolkit.annoations.Diff;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ly.flight.toolkit.converter.DateTimeJsonSerializer;
import com.ly.flight.toolkit.converter.JsonDateDeserializer;
import lombok.EqualsAndHashCode;

@Data
public class RideDispatchInfoVO   {

    /** 订单号 */
    private String              orderNo;

    /** 状态，1报单中  */
    private Integer              status;

    /** 举手时间 */
                @JsonSerialize(using = DateTimeJsonSerializer.class)
        @JsonDeserialize(using = JsonDateDeserializer.class)
    private Date              acceptTime;




    /** 车牌 */
    private String              vehicleNo;

    /** 司机姓名 */
    private String              driverName;

    /** 司机电话 */
    private String              driverMobile;

    /** 司机行程号 */
    private String              driverTripNo;

    /** 司机id */
    private Long              driverId;

    /** 顺路度 */
    private Integer              hitchPercent;

}
