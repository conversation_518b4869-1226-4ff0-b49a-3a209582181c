package com.ly.travel.car.carowner.biz.takeorder.service;

import com.ly.travel.car.carowner.facade.request.ValidateAcceptOrderRequest;
import com.ly.travel.car.carowner.facade.response.CarOwnerResponseDTO;

public interface AcceptOrderConfigService {

    /**
     * 校验司机接单配置
     *
     * @param request
     * @return
     */
    CarOwnerResponseDTO validateAcceptOrder(ValidateAcceptOrderRequest request);
}
