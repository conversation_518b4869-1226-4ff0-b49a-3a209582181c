/**
* RideDispatchInfoService
* This file is generated by <tt>toolkit(sof-batis-gen)</tt>, a DAL (Data Access Layer)
* @version Id : RideDispatchInfoService, v 0.1
*/
package com.ly.travel.car.carowner.biz.takeorder.service;

import com.ly.flight.toolkit.service.FormService;
import java.util.List;

import com.ly.sof.utils.page.PageList;
import com.ly.sof.utils.page.Paginator;

import com.ly.travel.car.carowner.biz.takeorder.model.GrabOrderStoreModel;
import com.ly.travel.car.carowner.biz.takeorder.model.RideDispatchInfoVO;

public interface RideDispatchInfoService  {
    Long addDispatchInfo(GrabOrderStoreModel grabOrderStoreModel) ;
    boolean updateDispatch(Long id,Integer status);
    GrabOrderStoreModel getGrabOrderStore(String orderNo) ;
    }
