package com.ly.travel.car.carowner.biz.takeorder.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.biz.sys.service.SysConfigService;
import com.ly.travel.car.carowner.biz.takeorder.model.CityVO;
import com.ly.travel.car.carowner.biz.takeorder.model.DeliveryRuleVO;
import com.ly.travel.car.carowner.biz.takeorder.service.AcceptOrderConfigService;
import com.ly.travel.car.carowner.common.cache.CacheUtils;
import com.ly.travel.car.carowner.common.enums.CacheKeyEnum;
import com.ly.travel.car.carowner.common.enums.OrderStatusEnum;
import com.ly.travel.car.carowner.common.enums.SysConfigEnum;
import com.ly.travel.car.carowner.common.exception.BusinessException;
import com.ly.travel.car.carowner.common.utils.DateUtil;
import com.ly.travel.car.carowner.common.utils.EnvUtils;
import com.ly.travel.car.carowner.dal.dataobject.DeliveryCityDO;
import com.ly.travel.car.carowner.dal.dataobject.DeliveryRuleDO;
import com.ly.travel.car.carowner.dal.dataobject.RideOrderInfoDO;
import com.ly.travel.car.carowner.dal.mapper.DeliveryCityMapper;
import com.ly.travel.car.carowner.dal.mapper.DeliveryRuleMapper;
import com.ly.travel.car.carowner.dal.mapper.RideOrderInfoMapper;
import com.ly.travel.car.carowner.facade.request.ValidateAcceptOrderRequest;
import com.ly.travel.car.carowner.facade.response.CarOwnerResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AcceptOrderConfigServiceImpl implements AcceptOrderConfigService {

    @Resource
    private DeliveryCityMapper deliveryCityMapper;

    @Resource
    private DeliveryRuleMapper deliveryRuleMapper;

    @Resource
    private SysConfigService sysConfigService;

    @Resource
    private RideOrderInfoMapper rideOrderInfoMapper;

    @Override
    public CarOwnerResponseDTO validateAcceptOrder(ValidateAcceptOrderRequest request) {

        try {
            // 城市接单规则
            List<DeliveryRuleVO> deliveryRule = getDeliveryRule(request.getStartCityId());

            // 校验接单时间限制
            validateAcceptTimeLimit(deliveryRule);

            // 校验平台接单限制
            validateAcceptOrderLimit(deliveryRule, request);

            // 校验当日取消限制
            validateCancelOrderLimit(request.getDriverId());

        } catch (BusinessException e) {
            return CarOwnerResponseDTO.fail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("系统异常，请稍后重试", e);
            return CarOwnerResponseDTO.fail("LY0510030000", "系统异常，请稍后重试");
        }

        return CarOwnerResponseDTO.succeed(null);
    }

    /**
     * 校验接单时间限制
     *
     * @param deliveryRule
     * @throws BusinessException
     */
    private void validateAcceptTimeLimit(List<DeliveryRuleVO> deliveryRule) throws BusinessException {
        deliveryRule.stream().forEach(deliveryRuleVO -> {
            if (StringUtils.isNotBlank(deliveryRuleVO.getEverdayUp()) && StringUtils.isNotBlank(deliveryRuleVO.getEverdayDown())) {
                Date today = new Date();
                String startLimit = DateUtil.getY4M2d2WebString(today) + " " + deliveryRuleVO.getEverdayUp();
                String endLimit = DateUtil.getY4M2d2WebString(today) + " " + deliveryRuleVO.getEverdayDown();
                log.info("接单时间限制校验，{},{}", startLimit, endLimit);
                if (today.after(DateUtil.parseAutomatic(startLimit)) && today.before(DateUtil.parseAutomatic(endLimit))) {
                    throw new BusinessException("LY0510000007", "当前时间禁止接单");
                }
            }
        });
    }

    /**
     * 校验平台接单限制
     *
     * @param deliveryRule
     * @throws BusinessException
     */
    private void validateAcceptOrderLimit(List<DeliveryRuleVO> deliveryRule, ValidateAcceptOrderRequest request) throws BusinessException {
        deliveryRule.stream().forEach(deliveryRuleVO -> {
            int maxCount = deliveryRuleVO.getCityLimit();
            int crossCityLimit = deliveryRuleVO.getCrossCityLimit();
            int maxTotalCount = deliveryRuleVO.getTotalLimit();
            log.info("司机接单数量校验，限制:cityCount={},crossCityLimit={},maxTotalCount={}", maxCount, crossCityLimit, maxTotalCount);
            Date gmtUsage = request.getStartingTime();
            String startUsageY4M2d2 = DateUtil.format(gmtUsage, DateUtil.DATE_PATTERN_YYYY_MM_DD);
            String endUsageY4M2d2 = DateUtil.format(DateUtil.addDay(gmtUsage, 1), DateUtil.DATE_PATTERN_YYYY_MM_DD);
            Date startDate = DateUtil.parseY4M2d2WebString(DateUtil.format(gmtUsage, DateUtil.DATE_PATTERN_YYYY_MM_DD));
            Date endDate = DateUtil.parseY4M2d2WebString(DateUtil.format(DateUtil.addDay(gmtUsage, 1), DateUtil.DATE_PATTERN_YYYY_MM_DD));
            Map<String, Long> acceptOrderCount = queryDriverAcceptSuccessCount(request.getDriverId(), startDate, endDate);
            long cityOrderCount = acceptOrderCount.getOrDefault("cityOrderCount", 0L);
            long crossCityOrderCount = acceptOrderCount.getOrDefault("crossCityOrderCount", 0L);
            log.info("司机接单数量校验，日期={},{},已接单数量:市内orderCount={},跨城orderCount={}，", startUsageY4M2d2, endUsageY4M2d2, cityOrderCount, crossCityOrderCount);
            if (!Objects.equals(request.getStartCityId(), request.getEndCityId())) {
                if (crossCityOrderCount >= crossCityLimit) {
                    log.warn("司机接单数量校验 跨城超过 maxCount={}", crossCityLimit);
                    throw new BusinessException("LY0510000008", "你的接单数量超过上限");
                }
            } else {
                if (cityOrderCount >= maxCount) {
                    log.warn("司机接单数量校验 市内超过 maxCount={}", maxCount);
                    throw new BusinessException("LY0510000008", "你的接单数量超过上限");
                }
            }
            if ((cityOrderCount + crossCityOrderCount) >= maxTotalCount) {
                log.warn("司机接单数量校验 总超过 maxCount={}", maxTotalCount);
                throw new BusinessException("LY0510000008", "你的接单数量超过上限");
            }
        });
    }

    private Map<String, Long> queryDriverAcceptSuccessCount(long driverId, Date gmtUsageStart, Date gmtUsageEnd) {
        Map<String, Long> result = new HashMap<>();
        result.put("crossCityOrderCount", 0L);//跨城已接单订单量
        result.put("cityOrderCount", 0L);// 市内已接单订单量
        result.put("cancelOrderCount", 0L);//取消订单量
        LambdaQueryWrapper<RideOrderInfoDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RideOrderInfoDO::getDriverId, driverId);
        queryWrapper.ge(RideOrderInfoDO::getStartingTime, gmtUsageStart);
        queryWrapper.le(RideOrderInfoDO::getStartingTime, gmtUsageEnd);
        queryWrapper.eq(RideOrderInfoDO::getEnv, EnvUtils.getEnvCode());

        List<RideOrderInfoDO> list = rideOrderInfoMapper.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(list)) {
            long cityCount = list.stream().filter(ro -> ro.getStatus() != OrderStatusEnum.CANCELLED.getStatus() && ro.getStartCityId().compareTo(ro.getEndCityId()) == 0).count();
            result.put("cityOrderCount", cityCount);

            long crossCityCount = list.stream().filter(ro -> ro.getStatus() != OrderStatusEnum.CANCELLED.getStatus() && ro.getStartCityId().compareTo(ro.getEndCityId()) != 0).count();
            result.put("crossCityOrderCount", crossCityCount);

            long cancelOrderCount = list.stream().filter(ro -> ro.getStatus() == OrderStatusEnum.CANCELLED.getStatus()).count();
            result.put("cancelOrderCount", cancelOrderCount);
        }
        return result;
    }

    /**
     * 校验每日取消次数
     *
     * @param driverId
     * @throws BusinessException
     */
    private void validateCancelOrderLimit(Long driverId) throws BusinessException {
        int cancelCountLimit = sysConfigService.getIntCachedCfgValue(SysConfigEnum.DRIVER_DAY_ORDER_CANCEL);
        Date startDate = DateUtil.parseY4M2d2WebString(DateUtil.format(new Date(), DateUtil.DATE_PATTERN_YYYY_MM_DD));
        Date endDate = DateUtil.parseY4M2d2WebString(DateUtil.format(DateUtil.addDay(new Date(), 1), DateUtil.DATE_PATTERN_YYYY_MM_DD));
        Map<String, Long> orderCount = queryDriverAcceptSuccessCount(driverId, startDate, endDate);
        long cancelOrderCount = orderCount.getOrDefault("cancelOrderCount", 0L);
        log.info("司机取消数量校验，已接单数量{}", cancelOrderCount);
        if (cancelOrderCount >= cancelCountLimit) {
            log.warn("司机取消数量校验 超过 maxCountLimit={}", cancelCountLimit);
            throw new BusinessException("LY0510000009", "你的取消订单数量超过上限");
        }
    }

    private List<DeliveryRuleVO> getDeliveryRule(Long cityId) {
        List<DeliveryRuleVO> resultList = new ArrayList<>();

        // 从缓存中获取城市接单规则
        List<DeliveryRuleVO> deliveryRuleVOS = getDeliveryRulesFromCache();
        if (CollectionUtils.isEmpty(deliveryRuleVOS)) {
            log.warn("城市接单规则缓存为空，尝试刷新缓存后重新获取");
            deliveryRuleVOS = refreshConfigCache();
            if (CollectionUtils.isEmpty(deliveryRuleVOS)) {
                log.warn("刷新缓存后仍未获取到城市接单规则");
                return null;
            }
        }

        log.info("获取城市接单规则: deliveryRuleVOS={}", FastJsonUtils.toJSONString(deliveryRuleVOS));

        // 匹配对应城市的接单规则
        DeliveryRuleVO matchedRule = findAndLogMatchingRule(deliveryRuleVOS, cityId);
        if (matchedRule != null) {
            resultList.add(matchedRule);
        }

        // 匹配全国兜底规则
        DeliveryRuleVO fallbackRule = findAndLogMatchingRule(deliveryRuleVOS, -1L);
        if (fallbackRule != null) {
            resultList.add(fallbackRule);
        }

        log.warn("匹配的接单规则: cityId={},resultList={}", cityId, FastJsonUtils.toJSONString(resultList));
        return resultList;
    }

    private List<DeliveryRuleVO> getDeliveryRulesFromCache() {
        String cacheValue = CacheUtils.getCacheValue(CacheKeyEnum.CITY_DELIVERY_RULE.getFormat());
        if (StringUtils.isBlank(cacheValue)) {
            log.warn("城市接单规则缓存为空");
            return null;
        }

        List<DeliveryRuleVO> deliveryRuleVOS = FastJsonUtils.fromJSON2List(cacheValue, DeliveryRuleVO.class);
        if (CollectionUtils.isEmpty(deliveryRuleVOS)) {
            log.warn("城市接单规则解析为空");
            return null;
        }

        return deliveryRuleVOS;
    }

    private DeliveryRuleVO findAndLogMatchingRule(List<DeliveryRuleVO> deliveryRuleVOS, Long cityId) {
        DeliveryRuleVO matchedRule = findDeliveryRuleByCityId(deliveryRuleVOS, cityId);
        if (matchedRule != null) {
            log.info("匹配成功：cityId={},DeliveryRuleDO={}", cityId, FastJsonUtils.toJSONString(matchedRule));
        }
        return matchedRule;
    }

    private List<DeliveryRuleVO> refreshConfigCache() {
        List<DeliveryRuleDO> deliveryRuleDOS = deliveryRuleMapper.selectList(
                new LambdaQueryWrapper<DeliveryRuleDO>()
                        .eq(DeliveryRuleDO::getEnv, EnvUtils.getEnvCode())
        );

        if (CollectionUtils.isEmpty(deliveryRuleDOS)) {
            log.warn("无要缓存的接单规则");
            return null;
        }

        List<DeliveryRuleVO> deliveryRuleVOS = deliveryRuleDOS.stream().map(deliveryRuleDO -> {
            DeliveryRuleVO deliveryRuleVO = new DeliveryRuleVO();
            BeanUtils.copyProperties(deliveryRuleDO, deliveryRuleVO);

            List<DeliveryCityDO> cityDOList = deliveryCityMapper.selectList(new LambdaQueryWrapper<DeliveryCityDO>().eq(DeliveryCityDO::getConfigId, deliveryRuleDO.getId()));
            deliveryRuleVO.setCityList(cityDOList.stream()
                    .map(cityDO -> {
                        CityVO cityVO = new CityVO();
                        cityVO.setCityName(cityDO.getCityName());
                        cityVO.setCityId(cityDO.getCityId());
                        return cityVO;
                    }).collect(Collectors.toList()));
            return deliveryRuleVO;
        }).collect(Collectors.toList());

        CacheUtils.setCacheValue(CacheKeyEnum.CITY_DELIVERY_RULE.getFormat(), FastJsonUtils.toJSONString(deliveryRuleVOS));

        return deliveryRuleVOS;
    }

    private DeliveryRuleVO findDeliveryRuleByCityId(List<DeliveryRuleVO> deliveryRuleVOS, Long cityId) {
        if (CollectionUtils.isEmpty(deliveryRuleVOS) || cityId == null) {
            return null;
        }

        return deliveryRuleVOS.stream()
                .filter(deliveryRuleVO -> CollectionUtils.isNotEmpty(deliveryRuleVO.getCityList())
                        && deliveryRuleVO.getCityList().stream()
                        .anyMatch(cityVO -> Objects.equals(cityVO.getCityId(), cityId.intValue())))
                .findFirst()
                .orElse(null);
    }
}
