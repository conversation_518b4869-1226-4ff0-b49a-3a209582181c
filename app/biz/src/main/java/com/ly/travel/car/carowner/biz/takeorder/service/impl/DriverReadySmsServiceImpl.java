package com.ly.travel.car.carowner.biz.takeorder.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.biz.order.model.RideOrderInfoVO;
import com.ly.travel.car.carowner.biz.takeorder.service.DriverReadySmsService;
import com.ly.travel.car.carowner.common.enums.PayStateEnum;
import com.ly.travel.car.carowner.common.utils.DateUtil;
import com.ly.travel.car.carowner.common.utils.EnvUtil;
import com.ly.travel.car.carowner.dal.dataobject.DriverInfoDO;
import com.ly.travel.car.carowner.dal.dataobject.VehicleInfoDO;
import com.ly.travel.car.carowner.dal.mapper.DriverInfoMapper;
import com.ly.travel.car.carowner.dal.mapper.VehicleInfoMapper;
import com.ly.travel.car.carowner.integration.sms.SmsComponent;
import com.ly.travel.car.carowner.integration.sms.SmsTemplatesDefinedList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @desc DriverReadySmsService
 */
@Slf4j
@Service
public class DriverReadySmsServiceImpl implements DriverReadySmsService {

    @Resource
    private VehicleInfoMapper vehicleInfoMapper;
    @Resource
    private DriverInfoMapper driverInfoMapper;
    @Resource
    private SmsComponent smsComponent;

    @Override
    public void sendSms(RideOrderInfoVO rideOrderInfoVO) {
        String orderNo = rideOrderInfoVO.getOrderNo();
        log.info("订单[{}]发送行程前出发通知信息", orderNo);
        Long driverId = rideOrderInfoVO.getDriverId();

        if (Objects.equals(rideOrderInfoVO.getPayStatus(), PayStateEnum.NO_PAY.getCode())) {
            log.info("订单未支付不发送行程前通知");
            return;
        }

        boolean verify = isVerify(driverId);

        String startCity = rideOrderInfoVO.getStartCity();
        String endCity = rideOrderInfoVO.getEndCity();
        if (startCity != null && !startCity.endsWith("市")) {
            startCity = startCity + "市";
        }

        if (endCity != null && !endCity.endsWith("市")) {
            endCity = endCity + "市";
        }

        if (verify) {
            smsComponent.sendTemplateMsg(SmsTemplatesDefinedList.DRIVER_READY_VERIFY_TEMPLATE
                    .setTemplateParams(DateUtil.date2String(rideOrderInfoVO.getStartingTime(), DateUtil.DATE_PATTERN_MONTH_DAY_HH_MM),
                            startCity + rideOrderInfoVO.getStartDistrict() + rideOrderInfoVO.getStartingAddDetail(),
                            endCity + rideOrderInfoVO.getEndDistrict() + rideOrderInfoVO.getEndingAddDetail(),
                            rideOrderInfoVO.getOrderNo(), rideOrderInfoVO.getDriverTripNo()).setMobile(rideOrderInfoVO.getDriverMobile()));
        } else {
            smsComponent.sendTemplateMsg(SmsTemplatesDefinedList.DRIVER_READY_UN_VERIFY_TEMPLATE
                    .setTemplateParams(DateUtil.date2String(rideOrderInfoVO.getStartingTime(), DateUtil.DATE_PATTERN_MONTH_DAY_HH_MM),
                            startCity + rideOrderInfoVO.getStartDistrict() + rideOrderInfoVO.getStartingAddDetail(),
                            endCity + rideOrderInfoVO.getEndDistrict() + rideOrderInfoVO.getEndingAddDetail(),
                            rideOrderInfoVO.getOrderNo(), rideOrderInfoVO.getDriverTripNo()).setMobile(rideOrderInfoVO.getDriverMobile()));
        }
    }

    /**
     * 未车辆识别/人脸识别未通过
     *
     * @param driverId
     * @return
     */
    private boolean isVerify(Long driverId) {
        VehicleInfoDO vehicleInfoDO = vehicleInfoMapper.selectOne(new LambdaQueryWrapper<VehicleInfoDO>()
                .eq(VehicleInfoDO::getDriverId, driverId)
                .eq(VehicleInfoDO::getEnv, EnvUtil.getEnv().getValue()));
        DriverInfoDO driverInfoDO = driverInfoMapper.selectOne(new LambdaQueryWrapper<DriverInfoDO>()
                .eq(DriverInfoDO::getId, driverId)
                .eq(DriverInfoDO::getEnv, EnvUtil.getEnv().getValue()));
        if (Objects.isNull(vehicleInfoDO) || Objects.isNull(driverInfoDO)) {
            log.error("未找到车辆信息或司机信息, driverId：{}", driverId);
            return false;
        }
        log.info("司机信息：{}", FastJsonUtils.toJSONString(driverInfoDO));
        log.info("车辆信息：{}", FastJsonUtils.toJSONString(vehicleInfoDO));
        if (Objects.isNull(vehicleInfoDO.getVehiclePictureStartDate()) || Objects.isNull(vehicleInfoDO.getVehicleValidPeriod())) {
            log.warn("未车辆识别, driverId：{}", driverId);
            return false;
        }
        Date vehiclePictureStartDate = DateUtil.addHour(vehicleInfoDO.getVehiclePictureStartDate(), vehicleInfoDO.getVehicleValidPeriod());
        if (vehiclePictureStartDate.before(new Date())) {
            log.warn("车辆识别未通过, driverId：{}", driverId);
            return false;
        }
        if (Objects.isNull(driverInfoDO.getFaceDate()) || Objects.isNull(driverInfoDO.getFaceValidPeriod())) {
            log.warn("未人脸识别, driverId：{}", driverId);
            return false;
        }
        Date faceDate = DateUtil.addHour(driverInfoDO.getFaceDate(), driverInfoDO.getFaceValidPeriod());
        if (faceDate.before(new Date())) {
            log.warn("人脸识别未通过, driverId：{}", driverId);
            return false;
        }
        return true;
    }
}
