/**
 * RideDispatchInfoServiceImpl
 * This file is generated by <tt>toolkit(sof-batis-gen)</tt>, a DAL (Data Access Layer)
 *
 * @version Id : RideDispatchInfoServiceImpl, v 0.1
 */
package com.ly.travel.car.carowner.biz.takeorder.service.impl;

import java.util.Date;

import javax.annotation.Resource;

import com.alibaba.fastjson2.JSON;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.biz.takeorder.mapper.RideDispatchInfoMapping;
import com.ly.travel.car.carowner.biz.takeorder.model.GrabOrderStoreModel;
import com.ly.travel.car.carowner.biz.takeorder.service.RideDispatchInfoService;
import com.ly.travel.car.carowner.common.cache.CacheUtils;
import com.ly.travel.car.carowner.common.enums.CacheKeyEnum;
import com.ly.travel.car.carowner.common.enums.RideDispatchStatusTypeEnum;
import com.ly.travel.car.carowner.common.exception.TakeOrderException;
import com.ly.travel.car.carowner.dal.mapper.RideDispatchInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ly.travel.car.carowner.dal.dataobject.*;
import org.springframework.util.StringUtils;

@Slf4j
@Service
public class RideDispatchInfoServiceImpl implements RideDispatchInfoService {

    @Resource
    private RideDispatchInfoMapping mapping;
    @Autowired
    private RideDispatchInfoMapper rideDispatchInfoMapper;

    @Override
    public Long addDispatchInfo(GrabOrderStoreModel grabOrderStoreModel)   {
        log.info("添加确认同行时派车信息,{}",FastJsonUtils.toJSONString(grabOrderStoreModel));
        RideDispatchInfoDO rideDispatchInfoDO=new RideDispatchInfoDO();

        rideDispatchInfoDO.setOrderNo(grabOrderStoreModel.getOrderNo());
        rideDispatchInfoDO.setAcceptType(grabOrderStoreModel.getAcceptType());
        rideDispatchInfoDO.setVehicleNo(grabOrderStoreModel.getOrderNo());
        rideDispatchInfoDO.setDriverName(grabOrderStoreModel.getDriverName());
        rideDispatchInfoDO.setDriverMobile(grabOrderStoreModel.getDriverMobile());
        rideDispatchInfoDO.setDriverTripNo(grabOrderStoreModel.getDriverTripNo());
        rideDispatchInfoDO.setDriverId(grabOrderStoreModel.getDriverId());
        rideDispatchInfoDO.setHitchPercent(grabOrderStoreModel.getHitchPercent());
        rideDispatchInfoDO.setStatus(RideDispatchStatusTypeEnum.DEFAULT.getType());
        rideDispatchInfoDO.setAcceptTime(new Date());
        rideDispatchInfoDO.setCreateTime(new Date());

        try {
            if (rideDispatchInfoMapper.insert(rideDispatchInfoDO) > 0) {
                CacheUtils.setCacheValue(CacheKeyEnum.GRAB_ORDER_STORE.format(grabOrderStoreModel.getOrderNo()), FastJsonUtils.toJSONString(grabOrderStoreModel), CacheKeyEnum.GRAB_ORDER_STORE.getExpire());
                return rideDispatchInfoDO.getId();
            }
        }catch (Exception ex) {
            log.error("添加派单中信息时异常,{}",FastJsonUtils.toJSONString(grabOrderStoreModel),ex);
        }
        return null;
    }

    public GrabOrderStoreModel getGrabOrderStore(String orderNo) {
        String cacheValue = CacheUtils.getCacheValue(CacheKeyEnum.GRAB_ORDER_STORE.format(orderNo));

        GrabOrderStoreModel grabOrderStoreModel = null;
        try {
            if (!StringUtils.hasText(cacheValue)) {
                log.warn("订单抢单信息不存在，可能已失效。{}", orderNo);
                return null;
            }
            grabOrderStoreModel = JSON.parseObject(cacheValue, GrabOrderStoreModel.class);
        } catch (Exception ex) {
            log.error("订单抢单信息不存在，可能已失效。{},cacheValue:{}", orderNo, cacheValue, ex);
        }
        return grabOrderStoreModel;
    }

    public boolean updateDispatch(Long id,Integer status){
        log.info("修改确认同行时派车信息,{},{}",id,status);

        RideDispatchInfoDO dispatchInfoDO=new RideDispatchInfoDO();
        dispatchInfoDO.setId(id);
        dispatchInfoDO.setStatus(status);
        return rideDispatchInfoMapper.updateById(dispatchInfoDO )>0;
    }


}
