package com.ly.travel.car.carowner.biz.takeorder.service.impl;
import java.util.Date;

import com.alibaba.fastjson2.JSON;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.biz.order.model.PassengerOrderInfoVO;
import com.ly.travel.car.carowner.biz.order.model.RideOrderInfoVO;
import com.ly.travel.car.carowner.biz.order.service.PassengerOrderInfoService;
import com.ly.travel.car.carowner.biz.order.service.RideOrderInfoService;
import com.ly.travel.car.carowner.biz.takeorder.model.GrabOrderStoreModel;
import com.ly.travel.car.carowner.biz.takeorder.service.DriverReadySmsService;
import com.ly.travel.car.carowner.biz.takeorder.service.RideDispatchInfoService;
import com.ly.travel.car.carowner.biz.takeorder.service.TakeOrderService;
import com.ly.travel.car.carowner.biz.turbomq.mq.DriverReadySender;
import com.ly.travel.car.carowner.common.cache.CacheUtils;
import com.ly.travel.car.carowner.common.enums.CacheKeyEnum;
import com.ly.travel.car.carowner.common.enums.OrderStatusEnum;
import com.ly.travel.car.carowner.common.enums.PassengerOrderStatusEnum;
import com.ly.travel.car.carowner.common.enums.PayStateEnum;
import com.ly.travel.car.carowner.common.exception.TakeOrderException;
import com.ly.travel.car.carowner.common.thread.MTThreadPoolEnum;
import com.ly.travel.car.carowner.common.thread.MTThreadPoolExecutors;
import com.ly.travel.car.carowner.common.utils.DateUtil;
import com.ly.travel.car.carowner.dal.mapper.PassengerOrderInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class TakeOrderServiceImpl implements TakeOrderService {

    @Autowired
    private PassengerOrderInfoService passengerOrderInfoService;
    @Autowired
    private RideOrderInfoService rideOrderInfoService;
    @Resource
    private DriverReadySender driverReadySender;
    @Resource
    private DriverReadySmsService driverReadySmsService;
    @Resource
    private RideDispatchInfoService rideDispatchInfoService;

    @Override
    public GrabOrderStoreModel grabOrderSuccess(String orderNo) {

        GrabOrderStoreModel  grabOrderStoreModel = rideDispatchInfoService.getGrabOrderStore(orderNo);

        if(grabOrderStoreModel==null) {
            throw new TakeOrderException("订单抢单信息不存在，或可能已失效.");
        }

        RideOrderInfoVO oldrideOrderInfoVO=rideOrderInfoService.queryOrderInfo(orderNo,null);
        if(oldrideOrderInfoVO!=null){
            log.warn("订单已存在，接单成功!!重复确认,{}",orderNo);
            return grabOrderStoreModel;
        }
        PassengerOrderInfoVO passengerOrderInfoVO=passengerOrderInfoService.queryByOrderNo(orderNo);
        if(passengerOrderInfoVO==null){
            log.warn("订单不存在，接单成功确认异常失败{}",orderNo);
            throw new TakeOrderException("订单不存在!");
        }
        RideOrderInfoVO rideOrderInfoVO=new RideOrderInfoVO();
        rideOrderInfoVO.setEnv(passengerOrderInfoVO.getEnv());
        rideOrderInfoVO.setOrderNo(passengerOrderInfoVO.getOrderNo());
        rideOrderInfoVO.setDistributorOrderNo(passengerOrderInfoVO.getDistributorOrderNo());
        rideOrderInfoVO.setDistributorMainOrderNo(passengerOrderInfoVO.getDistributorMainOrderNo());
        rideOrderInfoVO.setDriverTripNo(grabOrderStoreModel.getDriverTripNo());
        rideOrderInfoVO.setSource(passengerOrderInfoVO.getSource());
        rideOrderInfoVO.setHitchPercent(grabOrderStoreModel.getHitchPercent());
        rideOrderInfoVO.setStartCityId(passengerOrderInfoVO.getStartCityId());
        rideOrderInfoVO.setEndCityId(passengerOrderInfoVO.getEndCityId());
        rideOrderInfoVO.setStartCity(passengerOrderInfoVO.getStartCity());
        rideOrderInfoVO.setEndCity(passengerOrderInfoVO.getEndCity());
        rideOrderInfoVO.setStartDistrictCode(passengerOrderInfoVO.getStartDistrictCode());
        rideOrderInfoVO.setStartDistrict(passengerOrderInfoVO.getStartDistrict());
        rideOrderInfoVO.setEndDistrictCode(passengerOrderInfoVO.getEndDistrictCode());
        rideOrderInfoVO.setEndDistrict(passengerOrderInfoVO.getEndDistrict());
        rideOrderInfoVO.setStartingAdd(passengerOrderInfoVO.getStartingAdd());
        rideOrderInfoVO.setEndingAdd(passengerOrderInfoVO.getEndingAdd());
        rideOrderInfoVO.setStartingLon(passengerOrderInfoVO.getStartingLon());
        rideOrderInfoVO.setStartingLat(passengerOrderInfoVO.getStartingLat());
        rideOrderInfoVO.setEndingLon(passengerOrderInfoVO.getEndingLon());
        rideOrderInfoVO.setEndingLat(passengerOrderInfoVO.getEndingLat());
        rideOrderInfoVO.setStartingTime(passengerOrderInfoVO.getStartingTime());
        rideOrderInfoVO.setLastStartingTime(passengerOrderInfoVO.getLastStartingTime());
        rideOrderInfoVO.setTelephone(passengerOrderInfoVO.getTelephone());
        rideOrderInfoVO.setRealTelephone(passengerOrderInfoVO.getRealTelephone());
        rideOrderInfoVO.setPassengerCount(passengerOrderInfoVO.getPassengerCount());
        rideOrderInfoVO.setAmount(passengerOrderInfoVO.getAmount());
        rideOrderInfoVO.setPayPrice(passengerOrderInfoVO.getPayPrice());
        rideOrderInfoVO.setStatus(OrderStatusEnum.DISPATCHED.getStatus());
        rideOrderInfoVO.setPayStatus(passengerOrderInfoVO.getPayStatus());
        rideOrderInfoVO.setPayType(passengerOrderInfoVO.getPayType());
        rideOrderInfoVO.setRefId(passengerOrderInfoVO.getRefId());
        rideOrderInfoVO.setOrderType(passengerOrderInfoVO.getOrderType());
        rideOrderInfoVO.setMemberId(passengerOrderInfoVO.getMemberId());
        rideOrderInfoVO.setVehicleNo(grabOrderStoreModel.getVehicleNo());
        rideOrderInfoVO.setDriverName(grabOrderStoreModel.getDriverName());
        rideOrderInfoVO.setDriverMobile(grabOrderStoreModel.getDriverMobile());
        rideOrderInfoVO.setDriverId(grabOrderStoreModel.getDriverId());
        rideOrderInfoVO.setBookTime(passengerOrderInfoVO.getCreateTime());
        rideOrderInfoVO.setPayTime(passengerOrderInfoVO.getPayTime());
        rideOrderInfoVO.setCreateTime(new Date());
        rideOrderInfoVO.setUpdateTime(new Date());
        rideOrderInfoVO.setRemark(passengerOrderInfoVO.getRemark());
        rideOrderInfoVO.setStartingAddDetail(passengerOrderInfoVO.getStartingAddDetail());
        rideOrderInfoVO.setEndingAddDetail(passengerOrderInfoVO.getEndingAddDetail());
        rideOrderInfoVO.setDistance(passengerOrderInfoVO.getDistance());
        rideOrderInfoVO.setDuration(passengerOrderInfoVO.getDuration());
        rideOrderInfoVO.setOrderCommissionRatio(passengerOrderInfoVO.getOrderCommissionRatio());
        rideOrderInfoVO.setOrderCommissionCap(passengerOrderInfoVO.getOrderCommissionCap());

        passengerOrderInfoVO.setStatus(PassengerOrderStatusEnum.SENT.getCode());

        if(rideOrderInfoService.saveRideOrder(rideOrderInfoVO)>0 && passengerOrderInfoService.updatePassengerOrderStatus(orderNo,PassengerOrderStatusEnum.SENT.getCode(),"grabOrderSuccess")) {

            syncRidePayStatus(orderNo, rideOrderInfoVO);
            //发送短信

            MTThreadPoolExecutors.create(MTThreadPoolEnum.SEND_SMS).execute(()->{
                Date notifyTime = DateUtil.addHour(rideOrderInfoVO.getStartingTime(), -1);
                if (System.currentTimeMillis() >= notifyTime.getTime()) {
                    //距离出发不满一小时，直接发送短信
                    driverReadySmsService.sendSms(rideOrderInfoVO);
                } else {
                    //距离出发超过一小时，发送MQ
                    driverReadySender.sendDelayMsg(orderNo, notifyTime.getTime());
                }
            });
            return grabOrderStoreModel;
        }

        return null;
    }

    private void syncRidePayStatus(String orderNo, RideOrderInfoVO rideOrderInfoVO) {
        if(rideOrderInfoVO.getPayStatus() == PayStateEnum.NO_PAY.getCode()) {
            MTThreadPoolExecutors.create(MTThreadPoolEnum.UPDATE_PAY_STATUS).scheduleRun(() -> {
                PassengerOrderInfoVO passengerOrderInfoVO_now = passengerOrderInfoService.queryByOrderNo(orderNo);
                if(passengerOrderInfoVO_now.getPayStatus()==PayStateEnum.NO_PAY.getCode()){
                    log.info("延时更新主订单支付状态，抛弃，不处理,{}",orderNo);
                    return;
                }
                RideOrderInfoVO rideOrderInfoVO_new=rideOrderInfoService.queryOrderInfo(orderNo,null);
                if(rideOrderInfoVO_new.getPayStatus()!=passengerOrderInfoVO_now.getPayStatus()
                        ||rideOrderInfoVO_new.getPayPrice().compareTo(passengerOrderInfoVO_now.getPayPrice())!=0){
                    log.warn("订单支付状态不同步，重新修改，{}", FastJsonUtils.toJSONString(passengerOrderInfoVO_now));
                    RideOrderInfoVO rideOrderInfoUpdate=new RideOrderInfoVO();
                    rideOrderInfoUpdate.setOrderNo(orderNo);
                    rideOrderInfoUpdate.setPayStatus(passengerOrderInfoVO_now.getPayStatus());
                    rideOrderInfoUpdate.setPayPrice(passengerOrderInfoVO_now.getPayPrice());
                    rideOrderInfoUpdate.setPayTime(passengerOrderInfoVO_now.getPayTime());
                    rideOrderInfoUpdate.setCreateUser("updatePayStatus");
                    rideOrderInfoUpdate.setUpdateTime(new Date());
                    try {
                        rideOrderInfoService.updateRideOrder(rideOrderInfoUpdate);
                    }catch (Exception ex){
                        log.error("延时更新主订单支付状态失败,{}",FastJsonUtils.toJSONString(rideOrderInfoUpdate),ex);
                    }
                }

            },2, TimeUnit.SECONDS);
        }
    }
}
