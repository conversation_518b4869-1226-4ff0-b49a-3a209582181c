package com.ly.travel.car.carowner.biz.turbomq.consumer;

import com.ly.sof.api.exception.MQException;
import com.ly.sof.api.mq.common.UniformEvent;
import com.ly.sof.api.mq.common.UniformEventContext;
import com.ly.sof.api.mq.consumer.UniformEventMessageListener;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.biz.order.model.RideOrderInfoVO;
import com.ly.travel.car.carowner.biz.risk.OrderRiskService;
import com.ly.travel.car.carowner.biz.utils.MQUtils;
import com.ly.travel.car.carowner.common.enums.RiskSceneEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component("completeRiskListener")
@Slf4j
public class CompleteRiskListener implements UniformEventMessageListener {

    @Resource
    private OrderRiskService orderRiskService;

    @Override
    public boolean onUniformEvent(UniformEvent event, UniformEventContext context) throws MQException {
        String msgBody = MQUtils.getMsgBody(event);
        log.info("订单完单风控接收mq消息: msgBody={},message{}", msgBody, FastJsonUtils.toJSONString(event.getMessage()));
        if (StringUtils.isBlank(msgBody)) {
            log.warn("Uniform event is null");
            return true;
        }

        RideOrderInfoVO rideOrderInfoVO = FastJsonUtils.fromJSONString(msgBody, RideOrderInfoVO.class);
        orderRiskService.orderRisk(rideOrderInfoVO, RiskSceneEnum.ORDER_COMPLETE);
        return true;
    }

    @Override
    public ListenerTypeEnum getListenerType() {
        return ListenerTypeEnum.CONCURRENTLY;
    }
}
