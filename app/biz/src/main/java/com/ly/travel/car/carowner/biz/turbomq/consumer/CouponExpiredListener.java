package com.ly.travel.car.carowner.biz.turbomq.consumer;

import com.alibaba.fastjson2.JSONObject;
import com.ly.sof.api.exception.MQException;
import com.ly.sof.api.mq.common.UniformEvent;
import com.ly.sof.api.mq.common.UniformEventContext;
import com.ly.sof.api.mq.consumer.UniformEventMessageListener;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.biz.driver.coupon.service.DriverCouponService;
import com.ly.travel.car.carowner.biz.turbomq.model.JobCouponExpiredPayload;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @version CouponExpiredListener, 2025/9/11 13:36
 */
@Slf4j
@Service("couponExpiredListener")
public class CouponExpiredListener implements UniformEventMessageListener {

    @Resource
    private DriverCouponService driverCouponService;

    @Override
    public boolean onUniformEvent(UniformEvent event, UniformEventContext uniformEventContext) throws MQException {
        log.info("[CouponExpiredListener]");
        try {
            JobCouponExpiredPayload payload = validate(event);
            process(payload);
        } catch (Exception e) {
            log.error("[CouponExpiredListener][onUniformEvent] 券过期消费异常,{}", e.getMessage(), e);
        }
        return true;
    }

    /**
     * 从多种数据格式中提取Payload字符串
     *
     * @param event mq event
     * @return payload string
     */
    private String getPayloadString(UniformEvent event) {
        if (event.getPayload() instanceof String) {
            log.info("[CouponRechargeListener][getPayloadString] payload is String {}", event.getPayload().toString());
            return event.getPayload().toString();
        }

        if (event.getPayload() instanceof byte[]) {
            log.info("[CouponRechargeListener][getPayloadString] payload is byte");
            return new String((byte[]) event.getPayload(), StandardCharsets.UTF_8);
        }

        if (event.getPayload() instanceof JSONObject || event.getPayload() instanceof com.alibaba.fastjson.JSONObject) {
            log.info("[CouponRechargeListener][getPayloadString] payload is JSONObject");
            return event.getPayload().toString();
        }
        log.warn("[CouponRechargeListener][getPayloadString] payload is EMPTY");
        return StringUtils.EMPTY;
    }

    /**
     * 校验并返回消息体
     *
     * @param event 回写数据
     * @return 消息体
     */
    protected JobCouponExpiredPayload validate(UniformEvent event) {
        if (event.getPayload() == null) {
            log.warn("[CouponExpiredListener][validate] payload cannot be null");
            return null;
        }
        String payload = getPayloadString(event);
        log.info("[CouponExpiredListener][validate] payload={}", payload);
        if (StringUtils.isEmpty(payload)) {
            log.warn("[CouponExpiredListener][validate] invalid payload");
        }
        return FastJsonUtils.fromJSONString(payload, JobCouponExpiredPayload.class);
    }

    /**
     * 处理流程
     * @param payload
     */
    protected void process(JobCouponExpiredPayload payload) {
        driverCouponService.expired(payload);
    }

    @Override
    public ListenerTypeEnum getListenerType() {
        return ListenerTypeEnum.CONCURRENTLY;
    }
}
