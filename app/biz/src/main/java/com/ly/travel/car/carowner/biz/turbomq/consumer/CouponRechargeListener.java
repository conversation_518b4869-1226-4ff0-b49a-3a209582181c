package com.ly.travel.car.carowner.biz.turbomq.consumer;

import com.alibaba.fastjson2.JSONObject;
import com.ly.sof.api.exception.MQException;
import com.ly.sof.api.mq.common.UniformEvent;
import com.ly.sof.api.mq.common.UniformEventContext;
import com.ly.sof.api.mq.consumer.UniformEventMessageListener;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.biz.driver.coupon.service.DriverCouponService;
import com.ly.travel.car.carowner.biz.turbomq.model.CouponRechargePayload;
import com.ly.travel.car.carowner.common.cache.CacheUtils;
import com.ly.travel.car.carowner.common.enums.CacheKeyEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @version CouponListener, 2025/9/9 17:53
 */
@Slf4j
@Service("couponRechargeListener")
public class CouponRechargeListener implements UniformEventMessageListener {

    @Resource
    private DriverCouponService driverCouponService;

    @Override
    public boolean onUniformEvent(UniformEvent event, UniformEventContext uniformEventContext) throws MQException {
        log.info("[CouponRechargeListener]");
        boolean holdLock = false;
        String traceId = StringUtils.EMPTY;
        try {
            CouponRechargePayload payload = validate(event);
            traceId = payload.getTraceId();
            String lockKey = CacheKeyEnum.RECHARGE_COUPON.format(traceId);
            log.info("[CouponRechargeListener][onUniformEvent] 锁KEY={}", lockKey);
            holdLock = CacheUtils.setnx(lockKey, payload.getTraceId(), CacheKeyEnum.RECHARGE_COUPON.getExpire());
            if (!holdLock) {
                log.warn("[CouponRechargeListener][onUniformEvent] 获取锁失败,{}", lockKey);
                return true;
            }
            process(payload);
        } catch (Exception e) {
            log.error("[CouponRechargeListener][onUniformEvent] 发券消费异常,{}:{}", traceId, e.getMessage(), e);
        } finally {
            if (holdLock) {
                CacheUtils.delKey(CacheKeyEnum.RECHARGE_COUPON.format(traceId));
            }
        }
        return true;
    }

    /**
     * 从多种数据格式中提取Payload字符串
     *
     * @param event mq event
     * @return payload string
     */
    private String getPayloadString(UniformEvent event) {
        if (event.getPayload() instanceof String) {
            log.info("[CouponRechargeListener][getPayloadString] payload is String {}", event.getPayload().toString());
            return event.getPayload().toString();
        }

        if (event.getPayload() instanceof byte[]) {
            log.info("[CouponRechargeListener][getPayloadString] payload is byte");
            return new String((byte[]) event.getPayload(), StandardCharsets.UTF_8);
        }

        if (event.getPayload() instanceof JSONObject || event.getPayload() instanceof com.alibaba.fastjson.JSONObject) {
            log.info("[CouponRechargeListener][getPayloadString] payload is JSONObject");
            return event.getPayload().toString();
        }
        log.warn("[CouponRechargeListener][getPayloadString] payload is EMPTY");
        return StringUtils.EMPTY;
    }

    /**
     * 校验并返回订单
     *
     * @param event 回写数据
     * @return 消息体
     */
    protected CouponRechargePayload validate(UniformEvent event) {
        if (event.getPayload() == null) {
            log.warn("[CouponRechargeListener][validate] payload cannot be null");
            return null;
        }
        String payload = getPayloadString(event);
        log.info("[CouponRechargeListener][validate] payload={}", payload);
        if (StringUtils.isEmpty(payload)) {
            log.warn("[CouponRechargeListener][validate] invalid payload");
        }
        return FastJsonUtils.fromJSONString(payload, CouponRechargePayload.class);
    }

    /**
     * 处理流程
     * @param payload
     */
    protected void process(CouponRechargePayload payload) {
        driverCouponService.recharge(payload);
    }

    @Override
    public ListenerTypeEnum getListenerType() {
        return ListenerTypeEnum.CONCURRENTLY;
    }
}
