package com.ly.travel.car.carowner.biz.turbomq.consumer;

import com.ly.sof.api.exception.MQException;
import com.ly.sof.api.mq.common.UniformEvent;
import com.ly.sof.api.mq.common.UniformEventContext;
import com.ly.sof.api.mq.consumer.UniformEventMessageListener;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.biz.driver.bill.service.DriverBillService;
import com.ly.travel.car.carowner.biz.utils.MQUtils;
import com.ly.travel.car.carowner.facade.request.UnfreezeDriverBillRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component("driverBillUnfreezeConsumerListener")
@Slf4j
public class DriverBillUnfreezeConsumerListener implements UniformEventMessageListener {
    @Resource
    private DriverBillService driverBillService;

    @Override
    public boolean onUniformEvent(UniformEvent uniformEvent, UniformEventContext uniformEventContext) throws MQException {
        String msgBody = MQUtils.getMsgBody(uniformEvent);
        log.info("账单解冻mq监听信息: msgBody={},message={}", msgBody, FastJsonUtils.toJSONString(uniformEvent.getMessage()));
        if (StringUtils.isBlank(msgBody)) {
            log.warn("Uniform event is null");
            return true;
        }
        // 司机账单解冻
        driverBillService.unfreezeDriverBill(FastJsonUtils.fromJSONString(msgBody, UnfreezeDriverBillRequest.class));
        return true;
    }

    @Override
    public ListenerTypeEnum getListenerType() {
        return ListenerTypeEnum.CONCURRENTLY;
    }
}
