package com.ly.travel.car.carowner.biz.turbomq.consumer;

import com.alibaba.fastjson2.JSON;
import com.ly.sof.api.exception.MQException;
import com.ly.sof.api.mq.common.UniformEvent;
import com.ly.sof.api.mq.common.UniformEventContext;
import com.ly.sof.api.mq.consumer.UniformEventMessageListener;
import com.ly.travel.car.carowner.common.enums.BusinessBizEnum;
import com.ly.travel.car.carowner.biz.retry.BusinessMsgPayload;
import com.ly.travel.car.carowner.biz.retry.BusinessRetryMqHandle;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

@Component("retryableListener")
@Slf4j
public class RetryableListener implements UniformEventMessageListener {

    @Resource
    private BusinessRetryMqHandle businessRetryMqHandle;

    @Override
    public boolean onUniformEvent(UniformEvent event, UniformEventContext uniformEventContext) throws MQException {
        try {
            BusinessMsgPayload payload = validate(event);
            if (Objects.isNull(payload)) {
                log.error("消息体为空");
                return true;
            }
            String orderNo = payload.getOrderNo();
            if (StringUtils.isBlank(orderNo)) {
                log.error("订单号为空");
                return true;
            }
            BusinessBizEnum businessBizEnum = payload.getBusinessBizEnum();
            switch (businessBizEnum) {
                //接单回写C端重试
                case DRIVER_TRIP_NOTIFY_C:
                    log.info("[RetryableListener]当前订单[{}],判断回写重试发送,businessBizEnum={},重试次数={}", orderNo, businessBizEnum, payload.getRetryCount());
                    businessRetryMqHandle.driverTripRetry(orderNo, businessBizEnum, payload.getMsgJsonStr(), payload.getRetryCount());
                    break;
            }
        } catch (Exception e) {
            log.error("[retryableListener]", e);
        }
        return true;
    }

    /**
     * 消息体校验
     *
     * @param event
     * @return
     */
    private BusinessMsgPayload validate(UniformEvent event) {
        Object payload = event.getPayload();
        if (payload == null) {
            return null;
        }
        return JSON.parseObject(payload.toString(), BusinessMsgPayload.class);
    }

    @Override
    public ListenerTypeEnum getListenerType() {
        return ListenerTypeEnum.CONCURRENTLY;
    }
}
