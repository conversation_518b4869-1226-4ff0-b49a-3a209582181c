package com.ly.travel.car.carowner.biz.turbomq.consumer;

import com.ly.sof.api.exception.MQException;
import com.ly.sof.api.mq.common.UniformEvent;
import com.ly.sof.api.mq.common.UniformEventContext;
import com.ly.sof.api.mq.consumer.UniformEventMessageListener;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.biz.risk.OrderRiskService;
import com.ly.travel.car.carowner.biz.utils.MQUtils;
import com.ly.travel.car.carowner.common.enums.RiskTagEnum;
import com.ly.travel.car.carowner.integration.client.api.risk.model.response.UnifyCheckResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component("riskUpdateFreezeTimeListener")
@Slf4j
public class RiskUpdateFreezeTimeListener implements UniformEventMessageListener {

    @Resource
    private OrderRiskService orderRiskService;

    @Override
    public boolean onUniformEvent(UniformEvent event, UniformEventContext context) throws MQException {
        String msgBody = MQUtils.getMsgBody(event);
        log.info("风控更新冻结时间接收mq消息: msgBody={},tag={},message{}", msgBody, event.getEventCode(), FastJsonUtils.toJSONString(event.getMessage()));
        if (StringUtils.isBlank(msgBody)) {
            log.warn("Uniform event is null");
            return true;
        }

        RiskTagEnum riskTagEnum = RiskTagEnum.getByTag(event.getEventCode());
        if (riskTagEnum == null) {
            log.info("RiskTagEnum is null");
            return true;
        }

        switch (riskTagEnum) {
            case ORDER_FREEZE_TIME_UPDATE_TAG:
                UnifyCheckResponse unifyCheckResponse = FastJsonUtils.fromJSONString(msgBody, UnifyCheckResponse.class);
                orderRiskService.updateOrderFreezeTime(unifyCheckResponse);
                break;
        }

        return true;
    }

    @Override
    public ListenerTypeEnum getListenerType() {
        return ListenerTypeEnum.CONCURRENTLY;
    }
}
