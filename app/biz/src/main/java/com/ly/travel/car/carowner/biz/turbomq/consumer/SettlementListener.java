package com.ly.travel.car.carowner.biz.turbomq.consumer;

import com.ly.sof.api.exception.MQException;
import com.ly.sof.api.mq.common.UniformEvent;
import com.ly.sof.api.mq.common.UniformEventContext;
import com.ly.sof.api.mq.consumer.UniformEventMessageListener;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.biz.driver.coupon.service.DriverCouponService;
import com.ly.travel.car.carowner.biz.order.model.RideOrderInfoVO;
import com.ly.travel.car.carowner.biz.order.service.RideOrderInfoService;
import com.ly.travel.car.carowner.biz.retry.OrderEventPayload;
import com.ly.travel.car.carowner.biz.settlement.enums.SettlementTypeEnum;
import com.ly.travel.car.carowner.biz.settlement.service.SettlementPolicyProcess;
import com.ly.travel.car.carowner.biz.turbomq.producer.OrderCompleteProducer;
import com.ly.travel.car.carowner.biz.utils.MQUtils;
import com.ly.travel.car.carowner.common.enums.EventTypeEnum;
import com.ly.travel.car.carowner.common.enums.OrderStatusEnum;
import com.ly.travel.car.carowner.common.utils.CarownerConfigCenterUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Component("settlementListener")
@Slf4j
public class SettlementListener implements UniformEventMessageListener {

    @Resource
    private OrderCompleteProducer orderCompleteProducer;

    @Resource
    private RideOrderInfoService rideOrderInfoService;
    @Resource
    private DriverCouponService driverCouponService;

    @Override
    public boolean onUniformEvent(UniformEvent uniformEvent, UniformEventContext uniformEventContext) throws MQException {
        String msgBody = MQUtils.getMsgBody(uniformEvent);
        log.info("接收MQ信息:{},{}", msgBody,uniformEvent.getMessage());
        if (StringUtils.isBlank(msgBody)) {
            log.warn("Uniform event is null");
            return true;
        }
        OrderEventPayload orderEventPayload=FastJsonUtils.fromJSONString(msgBody,OrderEventPayload.class);

        if (EventTypeEnum.UPDATE_ORDER_STATE.getCode().equals(orderEventPayload.getEventType())) {
            if(OrderStatusEnum.DISPATCHED.getStatus()==orderEventPayload.getState()){

            }else if(OrderStatusEnum.CANCELLED.getStatus()==orderEventPayload.getState()){
                if(orderEventPayload.getPreState()!=null&&orderEventPayload.getPreState()!=OrderStatusEnum.DISPATCHED.getStatus()){
                    log.info("未抢单成功订单，忽略掉{}",orderEventPayload.getOrderNo());
                    return true;
                }
                SettlementPolicyProcess.getInstance(SettlementTypeEnum.CANCEL).processing(orderEventPayload.getOrderNo());
            }else if(OrderStatusEnum.COMPLETED.getStatus()==orderEventPayload.getState()){
                SettlementPolicyProcess.getInstance(SettlementTypeEnum.COMPLETED_ORDER).processing(orderEventPayload.getOrderNo());
                sendOrderCompleteRiskMq(orderEventPayload.getOrderNo());
                // 完单发券
                driverCouponService.sendFinishOrderRecharge(orderEventPayload.getOrderNo());
            }
        }else  if (EventTypeEnum.REFUND.getCode().equals(orderEventPayload.getEventType())) {
            SettlementPolicyProcess.getInstance(SettlementTypeEnum.REFUND).processing(orderEventPayload.getOrderNo());
        }


        return true;
    }

    private void sendOrderCompleteRiskMq(String orderNo) {
        RideOrderInfoVO rideOrderInfoVO = rideOrderInfoService.queryOrderInfo(orderNo, "");
        if (rideOrderInfoVO == null) {
            log.warn("订单不存在: orderNo={}", orderNo);
            return;
        }
        try {
            orderCompleteProducer.sendDelayMsg(orderCompleteProducer.getTopicName(), FastJsonUtils.toJSONString(rideOrderInfoVO), CarownerConfigCenterUtils.orderCompleteDelayLevel, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("订单完成风控mq发送异常: orderNo={}", orderNo, e);
        }
    }

    @Override
    public ListenerTypeEnum getListenerType() {
        return ListenerTypeEnum.CONCURRENTLY;
    }
}
