package com.ly.travel.car.carowner.biz.turbomq.model;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version CouponRechargePayload, 2025/9/9 19:30
 */
@Data
public class CouponRechargePayload {

    /**
     * traceId
     */
    private String  traceId;

    /**
     * 司机ID
     */
    private Long    driverId;

    /**
     * 场景类型(1-车主认证 2-车主完单)
     */
    private Integer sceneType;

    /**
     * 券类型
     */
    private Integer couponType;

    /**
     * 业务流水号(orderNo)
     */
    private String  orderSerialNo;

    /**
     * 领取时间
     */
    private Date    gmtReceived;
}
