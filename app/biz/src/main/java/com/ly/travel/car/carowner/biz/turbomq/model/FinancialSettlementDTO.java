package com.ly.travel.car.carowner.biz.turbomq.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.common.utils.DateUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class FinancialSettlementDTO {

    @JSONField(name="OrderSerialId")
    private String OrderSerialId;
    @JSONField(name="BusinessSerialId")
    private String BusinessSerialId;
    @JSONField(name="RebateList")
    private List<RebateItem> RebateList;

    @Data
    public static class RebateItem {
        public RebateItem(String orderNo,BigDecimal amount) {
            OrderSerialId=orderNo;
            CustomerOrderSerialId=orderNo;

            Amount = amount;
            CurrencyAmount = amount;
            RecordDate = DateUtil.date2String(new Date());
            CreateDate = DateUtil.date2String(new Date());
        }
        @JSONField(name="RebateType")
        private String RebateType = "***********";
        @JSONField(name="OrderSerialId")
        private String OrderSerialId;
        @JSONField(name="CustomerOrderSerialId")
        private String CustomerOrderSerialId;
        @JSONField(name="SupplierName")
        private String SupplierName = "马达顺风车";
        @JSONField(name="SupplierId")
        private String SupplierId = "52343552S574762";
        @JSONField(name="AccountingCompanyId")
        private String AccountingCompanyId = "1589";
        @JSONField(name="ProjectId")
        private String ProjectId = "***********";
        @JSONField(name="SecondProjectId")
        private String SecondProjectId = "70309";
        @JSONField(name="ProductType")
        private String ProductType = "C4218";
        @JSONField(name="Currency")
        private String Currency = "CNY";
        @JSONField(name="Amount")
        private BigDecimal Amount;
        @JSONField(name="CurrencyAmount")
        private BigDecimal CurrencyAmount;
        @JSONField(name="CreateDate")
        private String CreateDate;
        @JSONField(name="IsComplete")
        private Integer IsComplete = 0;
        @JSONField(name="PayChannel")
        private String PayChannel;
        @JSONField(name="PayProduct")
        private String PayProduct;
        @JSONField(name="AccountId")
        private String AccountId;
        @JSONField(name="RecordDate")
        private String RecordDate;
        @JSONField(name="ComplainNo")
        private String ComplainNo;
        @JSONField(name="PushType")
        private String PushType = "***********";
//        @JSONField(name="VersionNo")
//        private Integer VersionNo;
        @JSONField(name="IsReject")
        private Boolean IsReject = false;

    }


}
