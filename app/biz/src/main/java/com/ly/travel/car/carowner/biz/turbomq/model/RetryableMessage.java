package com.ly.travel.car.carowner.biz.turbomq.model;

import com.google.common.collect.Maps;
import com.ly.sof.api.mq.common.SerializeEnum;
import lombok.Data;

import java.util.Date;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 可重试MQ payload
 *
 * <AUTHOR>
 * @version Id: RetryableMessage, v 0.1 2024/3/5 10:30 zj Exp $
 */
@Data
public class RetryableMessage {

    /**
     * topic
     */
    private String topic;

    /**
     * tag
     */
    private String tag;

    /**
     * 重试次数
     */
    private final AtomicInteger retry;
    /**
     * 最大城市次数
     */

    private int retryMax = 5;

    /**
     * 实际 MQ payload
     */
    private Object payload;

    /**
     * 序列化方式
     */
    private SerializeEnum serialize;

    /**
     * MQ message Properties
     */
    private Map<String, String> properties = Maps.newHashMap();

    /**
     * 延迟消息级别
     */
    private Integer messageDelayLevel;

    /**
     * 设置发送时间，指定时间发送
     */
    private Date delayDate;

    public RetryableMessage(String topic, String tag, Object payload, SerializeEnum serialize) {
        this.topic = topic;
        this.tag = tag;
        this.payload = payload;
        this.serialize = serialize;
        retry = new AtomicInteger();
    }

    public RetryableMessage(String topic, String tag, Object payload) {
        this.topic = topic;
        this.tag = tag;
        this.payload = payload;
        this.serialize = SerializeEnum.FASTJSON;
        this.retry = new AtomicInteger();
    }

    public RetryableMessage(String topic, String tag, Object payload, int retryMax) {
        this.topic = topic;
        this.tag = tag;
        this.payload = payload;
        this.serialize = SerializeEnum.FASTJSON;
        this.retry = new AtomicInteger();
        this.retryMax = retryMax;
    }

    /**
     * put message property
     *
     * @param key   key
     * @param value value
     */
    public void putProperty(String key, String value) {
        if (properties == null) {
            properties = Maps.newHashMap();
        }
        properties.put(key, value);
    }

    /**
     * 是否固定延迟消息
     *
     * @return true 固定延迟消息
     */
    public boolean isDelayLevelMessage() {
        return this.messageDelayLevel != null && this.messageDelayLevel > 0;
    }

    /**
     * 是否延迟消息
     *
     * @return true 延迟消息
     */
    public boolean isDelayMessage() {
        return this.delayDate != null;
    }
}
