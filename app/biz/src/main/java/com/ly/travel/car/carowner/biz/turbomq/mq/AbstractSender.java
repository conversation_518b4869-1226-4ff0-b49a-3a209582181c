package com.ly.travel.car.carowner.biz.turbomq.mq;

import com.ly.travel.car.carowner.common.enums.RocketDelayLevelEnum;

/**
 * <AUTHOR>
 * @version Id: AbstractSender, v 0.1 2024/3/15 17:08 zss48204 Exp $
 */
public abstract class AbstractSender {

    protected RocketDelayLevelEnum delayLevel(Long endTime) {
        if (endTime - System.currentTimeMillis() > 2 * 60 * 60 * 1000) {
            return RocketDelayLevelEnum.HOUR_2;
        }
        if (endTime - System.currentTimeMillis() > 60 * 60 * 1000) {
            return RocketDelayLevelEnum.HOUR_1;
        }
        if (endTime - System.currentTimeMillis() > 30 * 60 * 1000) {
            return RocketDelayLevelEnum.MINUTE_30;
        }
        if (endTime - System.currentTimeMillis() > 20 * 60 * 1000) {
            return RocketDelayLevelEnum.MINUTE_20;
        }
        if (endTime - System.currentTimeMillis() > 10 * 60 * 1000) {
            return RocketDelayLevelEnum.MINUTE_10;
        }
        if (endTime - System.currentTimeMillis() > 9 * 60 * 1000) {
            return RocketDelayLevelEnum.MINUTE_9;
        }
        if (endTime - System.currentTimeMillis() > 8 * 60 * 1000) {
            return RocketDelayLevelEnum.MINUTE_8;
        }
        if (endTime - System.currentTimeMillis() > 7 * 60 * 1000) {
            return RocketDelayLevelEnum.MINUTE_7;
        }
        if (endTime - System.currentTimeMillis() > 6 * 60 * 1000) {
            return RocketDelayLevelEnum.MINUTE_6;
        }
        if (endTime - System.currentTimeMillis() > 5 * 60 * 1000) {
            return RocketDelayLevelEnum.MINUTE_5;
        }
        if (endTime - System.currentTimeMillis() > 4 * 60 * 1000) {
            return RocketDelayLevelEnum.MINUTE_4;
        }
        if (endTime - System.currentTimeMillis() > 3 * 60 * 1000) {
            return RocketDelayLevelEnum.MINUTE_3;
        }
        if (endTime - System.currentTimeMillis() > 2 * 60 * 1000) {
            return RocketDelayLevelEnum.MINUTE_2;
        }
        if (endTime - System.currentTimeMillis() > 60 * 1000) {
            return RocketDelayLevelEnum.MINUTE_1;
        }
        if (endTime - System.currentTimeMillis() > 30 * 1000) {
            return RocketDelayLevelEnum.SECOND_30;
        }
        if (endTime - System.currentTimeMillis() > 10 * 1000) {
            return RocketDelayLevelEnum.SECOND_10;
        }
        if (endTime - System.currentTimeMillis() > 5 * 1000) {
            return RocketDelayLevelEnum.SECOND_5;
        }
        return RocketDelayLevelEnum.SECOND_1;
    }
}