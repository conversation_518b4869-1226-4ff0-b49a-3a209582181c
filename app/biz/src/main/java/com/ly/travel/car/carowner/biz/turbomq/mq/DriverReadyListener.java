package com.ly.travel.car.carowner.biz.turbomq.mq;

import com.alibaba.fastjson2.JSON;
import com.ly.sof.api.exception.MQException;
import com.ly.sof.api.mq.common.UniformEvent;
import com.ly.sof.api.mq.common.UniformEventContext;
import com.ly.sof.api.mq.consumer.UniformEventMessageListener;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.biz.order.model.RideOrderInfoVO;
import com.ly.travel.car.carowner.biz.order.service.RideOrderInfoService;
import com.ly.travel.car.carowner.biz.takeorder.service.DriverReadySmsService;
import com.ly.travel.car.carowner.biz.turbomq.model.DriverReadyPayload;
import com.ly.travel.car.carowner.common.enums.OrderStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version Id: DecisionListener, v 0.1 2024/3/7 13:46 zss48204 Exp $
 */
@Slf4j
@Service("driverReadyListener")
public class DriverReadyListener implements UniformEventMessageListener {

    @Resource
    private DriverReadySender driverReadySender;
    @Resource
    private DriverReadySmsService driverReadySmsService;
    @Resource
    private RideOrderInfoService rideOrderInfoService;

    @Override
    public boolean onUniformEvent(UniformEvent uniformEvent, UniformEventContext uniformEventContext) throws MQException {
        DriverReadyPayload payload = validate(uniformEvent);
        if (payload == null) {
            log.error("[DriverReadyListener]消息体为空");
            return true;
        }
        log.info("[DriverReadyListener]消费MQ：{}", FastJsonUtils.toJSONString(payload));
        String orderNo = payload.getOrderNo();
        RideOrderInfoVO rideOrderInfoVO = rideOrderInfoService.queryOrderInfo(orderNo, null);
        if (Objects.isNull(rideOrderInfoVO)) {
            log.error("[DriverReadyListener]订单不存在：{}", orderNo);
            return true;
        }
        if (rideOrderInfoVO.getStatus().equals(OrderStatusEnum.COMPLETED.getStatus())) {
            log.warn("[DriverReadyListener]订单已完结：{}", orderNo);
            return true;
        }
        if (rideOrderInfoVO.getStatus().equals(OrderStatusEnum.CANCELLED.getStatus())) {
            log.warn("[DriverReadyListener]订单已取消：{}", orderNo);
            return true;
        }
        //到通知时间了，发送短信
        Long notifyTime = payload.getNotifyTime();
        if (System.currentTimeMillis() >= notifyTime) {
            driverReadySmsService.sendSms(rideOrderInfoVO);
        } else {
            //没有到时间。重新发起MQ
            log.info("[DriverReadyListener]没有到通知时间，重新发送MQ：{}", orderNo);
            driverReadySender.reSendDelayMsg(payload);
            return true;
        }
        return true;
    }

    @Override
    public ListenerTypeEnum getListenerType() {
        return ListenerTypeEnum.CONCURRENTLY;
    }

    /**
     * 消息体校验
     *
     * @param event
     * @return
     */
    private DriverReadyPayload validate(UniformEvent event) {
        Object payload = event.getPayload();
        if (payload == null) {
            return null;
        }
        return JSON.parseObject(payload.toString(), DriverReadyPayload.class);
    }
}