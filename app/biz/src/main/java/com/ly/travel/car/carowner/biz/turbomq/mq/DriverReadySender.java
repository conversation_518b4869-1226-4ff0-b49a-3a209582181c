package com.ly.travel.car.carowner.biz.turbomq.mq;

import com.ly.sof.api.mq.common.SerializeEnum;
import com.ly.travel.car.carowner.biz.turbomq.model.DriverReadyPayload;
import com.ly.travel.car.carowner.biz.turbomq.model.RetryableMessage;
import com.ly.travel.car.carowner.common.enums.RocketDelayLevelEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class DriverReadySender extends AbstractSender {

    public static final String tag = "*";

    @Value("${driver.ready.topic}")
    private String topic;

    @Resource
    private NormalProducer normalProducer;

    /**
     * 国际接送业务订单状态检测.
     *
     * @param endTime mq订阅时间戳
     */
    public void sendDelayMsg(String orderNo, Long endTime) {
        if (endTime > System.currentTimeMillis()) {
            // delay
            DriverReadyPayload payload = new DriverReadyPayload();
            payload.setOrderNo(orderNo);
            payload.setNotifyTime(endTime);
            RetryableMessage retryableMessage = new RetryableMessage(topic, tag, payload, 3);
            RocketDelayLevelEnum delayLevel = delayLevel(payload.getNotifyTime());
            if (delayLevel != null) {
                retryableMessage.setMessageDelayLevel(delayLevel.getLevel());
            }
            retryableMessage.setSerialize(SerializeEnum.FASTJSON);
            normalProducer.send(retryableMessage);
        } else {
            log.info("发送Q endTime是{}>=系统当前时间了 不发送", endTime);
        }
    }

    /**
     * 发送消息
     *
     * @param payload
     */
    public void reSendDelayMsg(DriverReadyPayload payload) {
        RetryableMessage retryableMessage = new RetryableMessage(topic, tag, payload, 3);
        retryableMessage.setSerialize(SerializeEnum.FASTJSON);
        RocketDelayLevelEnum delayLevel = delayLevel(payload.getNotifyTime());
        if (delayLevel != null) {
            retryableMessage.setMessageDelayLevel(delayLevel.getLevel());
        }
        normalProducer.send(retryableMessage);
    }
}