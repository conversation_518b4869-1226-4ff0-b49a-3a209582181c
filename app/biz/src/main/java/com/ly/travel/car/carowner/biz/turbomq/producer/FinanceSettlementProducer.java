/**
 * Creation Date:2016年3月9日-下午4:57:12
 *
 * Copyright 2008-2016 © 同程网 Inc. All Rights Reserved
 */
package com.ly.travel.car.carowner.biz.turbomq.producer;

import com.alibaba.rocketmq.client.producer.DefaultMQProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class FinanceSettlementProducer extends TurboMqProducer {

    private String producerGroup;

    @Value("${finance.settlement.group}")
    public void setProducerGroup(String producerGroup) {
        this.producerGroup = producerGroup;
        try {
            start();
            log.info("免佣卡结算信息推送财务Producer 启动成功！groupName:{} ", producerGroup);
        } catch (Exception e) {
            log.error("免佣卡结算信息推送财务 FinancialSettlementProducer 启动报错：  " + e.getMessage());
        }
    }

    private String topicName;

    @Override
    public String getProducerGroup() {
        return producerGroup;
    }

    public String getTopicName() {
        return topicName;
    }

    @Value("${finance.settlement.topic}")
    public void setTopicName(String topicName) {
        this.topicName = topicName;
    }

    /**
     * 启动客户端
     *
     * @throws Exception
     */
    @Override
    public void start() throws Exception {
        setProducer(new DefaultMQProducer(getProducerGroup()));
        getProducer().setNamesrvAddr(getNameserver());
        getProducer().start();
    }

    /**
     * 关闭客户端
     */
    @Override
    public void shutdown() {
        getProducer().shutdown();
    }
}
