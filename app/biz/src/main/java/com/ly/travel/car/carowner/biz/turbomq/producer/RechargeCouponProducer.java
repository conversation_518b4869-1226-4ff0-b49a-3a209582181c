package com.ly.travel.car.carowner.biz.turbomq.producer;

import com.alibaba.rocketmq.client.producer.DefaultMQProducer;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.biz.turbomq.model.CouponRechargePayload;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version RechargeCouponProducer, 2025/9/10 14:36
 */
@Component
@Slf4j
public class RechargeCouponProducer extends TurboMqProducer {

    private String producerGroup;

    @Value("${coupon.recharge.topic_group}")
    public void setProducerGroup(String producerGroup) {
        this.producerGroup = producerGroup;
        try {
            start();
            log.info("发券Producer 启动成功！groupName:{} ", producerGroup);
        } catch (Exception e) {
            log.error("RechargeCouponProducer启动报错：  " + e.getMessage());
        }
    }

    private String topicName;

    @Override
    public String getProducerGroup() {
        return producerGroup;
    }

    public String getTopicName() {
        return topicName;
    }

    @Value("${coupon.recharge.topic}")
    public void setTopicName(String topicName) {
        this.topicName = topicName;
    }

    /**
     * 启动客户端
     *
     * @throws Exception
     */
    @Override
    public void start() throws Exception {
        setProducer(new DefaultMQProducer(getProducerGroup()));
        getProducer().setNamesrvAddr(getNameserver());
        getProducer().start();
    }

    /**
     * 关闭客户端
     */
    @Override
    public void shutdown() {
        getProducer().shutdown();
    }

    /**
     * 
     * @param payload
     */
    public void send(CouponRechargePayload payload) {
        try {
            sendMsg(getTopicName(), FastJsonUtils.toJSONString(payload));
        } catch (Exception e) {
            log.error("[RechargeCouponProducer]发送MQ失败，msg：{}", FastJsonUtils.toJSONString(payload));
        }
    }
}
