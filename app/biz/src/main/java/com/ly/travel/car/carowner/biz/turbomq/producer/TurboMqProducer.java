package com.ly.travel.car.carowner.biz.turbomq.producer;

import com.alibaba.fastjson.JSON;
import com.alibaba.rocketmq.client.exception.MQBrokerException;
import com.alibaba.rocketmq.client.exception.MQClientException;
import com.alibaba.rocketmq.client.producer.DefaultMQProducer;
import com.alibaba.rocketmq.client.producer.MessageQueueSelector;
import com.alibaba.rocketmq.client.producer.SendCallback;
import com.alibaba.rocketmq.client.producer.SendResult;
import com.alibaba.rocketmq.common.message.Message;
import com.alibaba.rocketmq.remoting.exception.RemotingException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

import java.nio.charset.Charset;
import java.util.concurrent.TimeUnit;

/**
 * TurboMQ生产者组件
 */
public class TurboMqProducer {
    Logger log= LoggerFactory.getLogger(this.getClass());

    public static final Charset SYSTEM_CHARSET_UTF_8 = Charset.forName("UTF-8");
    private static final int ONE_HOUR_SECONDS = 60 * 60;

    /**
     * 生产者组名
     */
    private String producerGroup;

    /**
     * nameserver地址
     */
    private String nameserver;

    /**
     * 生成者客户端
     */
    private DefaultMQProducer producer;

    /**
     * 启动客户端
     *
     * @throws MQClientException
     */
    public void start() throws Exception {
        this.producer = new DefaultMQProducer(producerGroup);
        this.producer.setNamesrvAddr(nameserver);
        this.producer.start();
    }

    /**
     * 同步发送消息
     *
     * @param topic
     * @param msg
     * @return
     * @throws MQClientException
     * @throws RemotingException
     * @throws MQBrokerException
     * @throws InterruptedException
     */
    public SendResult sendMsg(String topic, String msg)
            throws MQClientException, RemotingException, MQBrokerException, InterruptedException {
        SendResult sendResult=null;
        try {
            sendResult= this.producer.send(new Message(topic, msg.getBytes(SYSTEM_CHARSET_UTF_8)));
            log.info("发送MQ消息,topic:{},msg:{}，返回结果:{}",topic,msg,sendResult);
        }catch (Exception ex){
            log.error("发送MQ消息,topic:{},msg:{},失败",topic,msg,ex);
        }
        return sendResult;
    }

    /**
     * Oneway发送消息
     *
     * @param topic
     * @param msg
     * @return
     * @throws MQClientException
     * @throws RemotingException
     * @throws MQBrokerException
     * @throws InterruptedException
     */
    public void sendOneWayMsg(String topic, String key, String msg)
            throws MQClientException, RemotingException, MQBrokerException, InterruptedException {
        this.producer.sendOneway(new Message(topic, "", key, msg.getBytes(SYSTEM_CHARSET_UTF_8)));
    }

    /**
     * 1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h
     *
     * @param topic
     * @param msg
     * @return
     * @throws MQClientException
     * @throws RemotingException
     * @throws MQBrokerException
     * @throws InterruptedException
     */
    public SendResult sendDelayMsg(String topic, String msg, long delayTime, TimeUnit timeUnit) throws Exception {
        Message message = new Message(topic, msg.getBytes(SYSTEM_CHARSET_UTF_8));
        if(delayTime>0) {
            message.setDelayTime(delayTime, timeUnit);
        }
        SendResult sendResult=null;
        try {
            sendResult = this.producer.send(message);
            log.info("发送延时MQ消息,topic:{},msg:{},延时:{}，返回结果:{}",topic,msg,timeUnit.toSeconds(delayTime),sendResult);
        }catch (Exception ex){
            log.error("发送延时MQ消息,topic:{},msg:{},延时:{}，失败",topic,msg,timeUnit.toSeconds(delayTime),ex);
        }
        return sendResult;
    }

    public SendResult sendDelayMsg(String topic, String tags, String msg, long delayTime, TimeUnit timeUnit) throws Exception {
        Message message = new Message(topic, tags, msg.getBytes(SYSTEM_CHARSET_UTF_8));
        message.setDelayTime(delayTime, timeUnit);
        return this.producer.send(message);
    }

    public SendResult sendDelayMsg(String topic, String tags, String keys, String msg, long delayTime, TimeUnit timeUnit) throws Exception {
        Message message = new Message(topic, tags, keys, msg.getBytes(SYSTEM_CHARSET_UTF_8));
        if (delayTime > 0) {
            message.setDelayTime(delayTime, timeUnit);
        }
        return this.producer.send(message);
    }

    public SendResult sendMsg(String topic, byte[] msg) throws Exception {
        return this.producer.send(new Message(topic, msg));
    }

    /**
     * tag
     *
     * @param topic
     * @param msg
     * @return
     * @throws Exception
     */
    public SendResult sendMsg(String topic, String tags, String keys, String msg) throws Exception {
        SendResult sendResult=null;
        try {
            sendResult = this.producer.send(new Message(topic, tags, keys, msg.getBytes(SYSTEM_CHARSET_UTF_8)));
            log.info("发送MQ消息,topic:{},msg:{}，返回结果:{}",topic,msg,sendResult);
        }catch (Exception ex){
            log.error("发送MQ消息,topic:{},msg:{}，失败",topic,msg,ex);
        }
        return sendResult;
    }

    public void sendOnewayWithRetry(String topic, String tags, String keys, String msg) throws Exception {
        this.producer.sendOnewayWithRetry(new Message(topic, tags, keys, msg.getBytes(SYSTEM_CHARSET_UTF_8)));
    }

    /**
     * 按照指定的路由策略，顺序发送消息
     *
     * @param topic         主题
     * @param msg           消息内容
     * @param queueSelector 路由选择器
     * @return
     * @throws Exception
     */
    public SendResult orderlySendMsg(String topic, String msg, MessageQueueSelector queueSelector, Object openId)
            throws Exception {
        return this.producer.send(new Message(topic, msg.getBytes(SYSTEM_CHARSET_UTF_8)), queueSelector, openId);
    }

    public SendResult orderlySendMsg(String topic, String tag, String msg, MessageQueueSelector queueSelector,
                                     Object openId) throws Exception {
        return this.producer.send(new Message(topic, tag, msg.getBytes(SYSTEM_CHARSET_UTF_8)), queueSelector, openId);
    }

    public SendResult orderlySendMsg(String topic, String tag, String uniqueKey, String msg,
                                     MessageQueueSelector queueSelector, Object openId) throws Exception {
        return this.producer.send(new Message(topic, tag, uniqueKey, msg.getBytes(SYSTEM_CHARSET_UTF_8)), queueSelector,
                openId);
    }

    /**
     * 按顺序延迟发送消息 level: 1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h
     *
     * @param topic
     * @param tag
     * @param uniqueKey
     * @param msg
     * @param queueSelector
     * @param openId
     * @return
     * @throws Exception
     */
    public SendResult orderlyDelaySendMsg(String topic, String tag, String uniqueKey, String msg,
                                          MessageQueueSelector queueSelector, Object openId, int delayLevel) throws Exception {
        Message message = new Message(topic, tag, uniqueKey, msg.getBytes(SYSTEM_CHARSET_UTF_8));
        // 设置延迟级别
        message.setDelayTimeLevel(delayLevel);
        return this.producer.send(message, queueSelector, openId);
    }

    /**
     * 按照指定的路由策略，顺序发送消息
     *
     * @param topic
     * @param msg           消息内容
     * @param queueSelector 路由选择器
     * @param openId        路由计算使用的信息
     * @return
     * @throws Exception
     */
    public SendResult orderlySendMsg(String topic, byte[] msg, MessageQueueSelector queueSelector, Object openId)
            throws Exception {
        return this.producer.send(new Message(topic, msg), queueSelector, openId);
    }

    /**
     * 按照指定的路由策略，顺序发送消息
     *
     * @param topic         主题
     * @param tag           标签
     * @param msg           消息内容
     * @param queueSelector 路由选择器
     * @param openId        路由计算使用的信息
     * @return
     * @throws Exception
     */
    public SendResult orderlySendMsg(String topic, String tag, byte[] msg, MessageQueueSelector queueSelector,
                                     Object openId) throws Exception {
        return this.producer.send(new Message(topic, tag, msg), queueSelector, openId);
    }

    /**
     * 按照指定的路由策略，顺序发送消息
     *
     * @param topic         主题
     * @param tag           标签
     * @param uniqueKey     唯一key
     * @param msg           消息内容
     * @param queueSelector 路由选择器
     * @param serviceId     路由计算使用的信息
     * @return
     * @throws Exception
     */
    public SendResult orderlySendMsg(String topic, String tag, String uniqueKey, byte[] msg,
                                     MessageQueueSelector queueSelector, Object serviceId) throws Exception {
        return this.producer.send(new Message(topic, tag, uniqueKey, msg), queueSelector, serviceId);
    }

    /**
     * 异步发送消息
     *
     * @param topic
     * @param msg
     * @param callBack
     * @throws MQClientException
     * @throws RemotingException
     * @throws InterruptedException
     */
    public void sendMsg(String topic, String msg, SendCallback callBack) throws Exception {
        this.producer.send(new Message(topic, msg.getBytes(SYSTEM_CHARSET_UTF_8)), callBack);
    }

    public void sendMsg(String topic, byte[] msg, SendCallback callBack) throws Exception {
        this.producer.send(new Message(topic, msg), callBack);
    }

    /**
     * 关闭客户端
     */
    public void shutdown() {
        this.producer.shutdown();
    }


    public String convertMsg(Object msg){
        return JSON.toJSONString(msg);
    }


    public String getProducerGroup() {
        return producerGroup;
    }

    public void setProducerGroup(String producerGroup) {
        this.producerGroup = producerGroup;
    }

    public String getNameserver() {
        return nameserver;
    }

    @Value("${mq.nameSrvAddress}")
    public void setNameserver(String nameserver) {
        this.nameserver = nameserver;
    }

    public DefaultMQProducer getProducer() {
        return producer;
    }

    public void setProducer(DefaultMQProducer producer) {
        this.producer = producer;
    }
}
