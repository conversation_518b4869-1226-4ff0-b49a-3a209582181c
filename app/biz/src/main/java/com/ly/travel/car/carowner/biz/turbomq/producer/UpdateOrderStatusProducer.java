package com.ly.travel.car.carowner.biz.turbomq.producer;

import com.alibaba.rocketmq.client.producer.DefaultMQProducer;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.biz.retry.OrderEventPayload;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @desc UpdateOrderStatusProducer
 */
@Slf4j
@Component
public class UpdateOrderStatusProducer extends TurboMqProducer {

    private String producerGroup;

    @Value("${update.status.group}")
    public void setProducerGroup(String producerGroup) {
        this.producerGroup = producerGroup;
        try {
            start();
            log.info("修改订单状态Producer 启动成功！groupName:{} ", producerGroup);
        } catch (Exception e) {
            log.error("UpdateOrderStatusProducer启动报错: " + e.getMessage());
        }
    }

    private String topicName;

    @Override
    public String getProducerGroup() {
        return producerGroup;
    }

    public String getTopicName() {
        return topicName;
    }

    @Value("${update.status.topic}")
    public void setTopicName(String topicName) {
        this.topicName = topicName;
    }

    /**
     * 启动客户端
     *
     * @throws Exception
     */
    @Override
    public void start() throws Exception {
        setProducer(new DefaultMQProducer(getProducerGroup()));
        getProducer().setNamesrvAddr(getNameserver());
        getProducer().start();
    }

    /**
     * 关闭客户端
     */
    @Override
    public void shutdown() {
        getProducer().shutdown();
    }

    public void send(OrderEventPayload payload) {
        try {
            sendMsg(getTopicName(), FastJsonUtils.toJSONString(payload));
        } catch (Exception e) {
            log.error("[UpdateOrderStatusProducer]发送MQ失败，msg：{}", FastJsonUtils.toJSONString(payload));
        }
    }
}
