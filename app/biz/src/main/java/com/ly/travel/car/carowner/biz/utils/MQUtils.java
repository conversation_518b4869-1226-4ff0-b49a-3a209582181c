package com.ly.travel.car.carowner.biz.utils;

import com.ly.sof.api.mq.common.UniformEvent;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.Charset;

@Slf4j
public class MQUtils {
    public static String getMsgBody(UniformEvent event) {
        Object obj = event.getPayload();
        LoggerUtils.info(log, "payLoad = {}", FastJsonUtils.toJSONString(obj));
        if (obj instanceof byte[]) {
            return new String((byte[]) obj, Charset.forName("UTF-8"));
        } else {
            return FastJsonUtils.toJSONString(obj);
        }
    }
}
