<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:sof="http://schema.ly.com/schema/sof"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
         http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
         http://schema.ly.com/schema/sof http://schema.ly.com/schema/sof.xsd"

       default-autowire="byName">
    <context:component-scan base-package="com.ly.flight.toolkit"/>
    <context:component-scan base-package="com.ly.travel.car.carowner"></context:component-scan>

    <sof:publisher id="uniformEventPublisher" group="${driver.ready.group}" nameSrvAddress="${mq.nameSrvAddress}"
                   failover="true" retry="3" timeout="3000"
                   poolSize="8"/>
    <!-- 生产者 -->
    <bean id="sofProducer" class="com.ly.sof.api.mq.producer.DefaultProducer">
        <property name="uniformEventPublisher" ref="uniformEventPublisher"/>
    </bean>
</beans>
