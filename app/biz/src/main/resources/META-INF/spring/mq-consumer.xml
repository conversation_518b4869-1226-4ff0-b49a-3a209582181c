<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:sof="http://schema.ly.com/schema/sof"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
         http://schema.ly.com/schema/sof http://schema.ly.com/schema/sof.xsd"
       default-autowire="byName">

    <sof:consumer id="driverBillUnfreezeConsumer" group="${driver.bill.unfreeze.group}"
                  nameSrvAddress="${mq.nameSrvAddress}" consumeThreadMax="16" consumeThreadMin="4"
                  pullThresholdForQueue="1024">
        <sof:listener ref="driverBillUnfreezeConsumerListener"/>
        <sof:channels>
            <sof:channel topic="${driver.bill.unfreeze.topic}">
                <sof:event eventCode="*"/>
            </sof:channel>
        </sof:channels>
    </sof:consumer>
    <sof:consumer id="retryableListenerConsumer" group="${api.call.retry.group}"
                  nameSrvAddress="${mq.nameSrvAddress}" consumeThreadMax="16" consumeThreadMin="4"
                  pullThresholdForQueue="1024">
        <sof:listener ref="retryableListener"/>
        <sof:channels>
            <sof:channel topic="${api.call.retry.topic}">
                <sof:event eventCode="*"/>
            </sof:channel>
        </sof:channels>
    </sof:consumer>
    <sof:consumer id="driverReadyListenerConsumer" group="${driver.ready.group}"
                  nameSrvAddress="${mq.nameSrvAddress}" consumeThreadMax="16" consumeThreadMin="4"
                  pullThresholdForQueue="1024">
        <sof:listener ref="driverReadyListener"/>
        <sof:channels>
            <sof:channel topic="${driver.ready.topic}">
                <sof:event eventCode="*"/>
            </sof:channel>
        </sof:channels>
    </sof:consumer>
    <sof:consumer id="settlementListenerConsumer" group="${order.settlement.group}"
                  nameSrvAddress="${mq.nameSrvAddress}" consumeThreadMax="16" consumeThreadMin="4"
                  pullThresholdForQueue="1024">
        <sof:listener ref="settlementListener"/>
        <sof:channels>
            <sof:channel topic="${update.status.topic}">
                <sof:event eventCode="*"/>
            </sof:channel>
        </sof:channels>
    </sof:consumer>

    <!--发券-->
    <sof:consumer id="couponRechargeListenerConsumer" group="${coupon.recharge.group}"
                  nameSrvAddress="${mq.nameSrvAddress}" consumeThreadMax="16" consumeThreadMin="4"
                  pullThresholdForQueue="1024">
        <sof:listener ref="couponRechargeListener"/>
        <sof:channels>
            <sof:channel topic="${coupon.recharge.topic}">
                <sof:event eventCode="*"/>
            </sof:channel>
        </sof:channels>
    </sof:consumer>

    <sof:consumer id="completeRiskListenerConsumer" group="${order.complete.risk.group}"
                  nameSrvAddress="${mq.nameSrvAddress}" consumeThreadMax="16" consumeThreadMin="4"
                  pullThresholdForQueue="1024">
        <sof:listener ref="completeRiskListener"/>
        <sof:channels>
            <sof:channel topic="${order.complete.risk.topic}">
                <sof:event eventCode="*"/>
            </sof:channel>
        </sof:channels>
    </sof:consumer>

    <!--发券-->
    <sof:consumer id="couponExpiredListenerConsumer" group="${coupon.expired.group}"
                  nameSrvAddress="${mq.nameSrvAddress}" consumeThreadMax="16" consumeThreadMin="4"
                  pullThresholdForQueue="1024">
        <sof:listener ref="couponExpiredListener"/>
        <sof:channels>
            <sof:channel topic="${coupon.expired.topic}">
                <sof:event eventCode="*"/>
            </sof:channel>
        </sof:channels>
    </sof:consumer>

    <!--风控更新冻结时间-->
    <sof:consumer id="riskUpdateFreezeTimeListenerConsumer" group="${order.risk.update.freeze.time.group}"
                  nameSrvAddress="${mq.nameSrvAddress}" consumeThreadMax="16" consumeThreadMin="4"
                  pullThresholdForQueue="1024">
        <sof:listener ref="riskUpdateFreezeTimeListener"/>
        <sof:channels>
            <sof:channel topic="${order.risk.update.freeze.time.topic}">
                <sof:event eventCode="*"/>
            </sof:channel>
        </sof:channels>
    </sof:consumer>
</beans>
