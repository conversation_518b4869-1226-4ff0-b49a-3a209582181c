package com.ly.travel.car.carowner.common.cache;

import com.ly.tcbase.cacheclient.CacheClientHA;

import java.util.Map;

public class CacheUtils {

    public CacheUtils() {
    }

    private static final CacheClientHA cacheClient = new CacheClientHA("carowner", false);

    public static CacheClientHA getCacheClient() {
        return cacheClient;
    }

    /**
     * 插入缓存
     *
     * @param key               the key
     * @param value             the value
     * @param secondsForExpired 失效时长(s)
     * @return the cache value
     */
    public static Boolean setCacheValue(String key, String value, long secondsForExpired) {
        return cacheClient.String().setex(key, secondsForExpired, value);
    }

    /**
     * 插入缓存
     *
     * @param key               the key
     * @param value             the value
     * @param secondsForExpired 失效时长(s)
     * @return the cache value bit
     */
    public static Boolean setCacheValueBit(String key, byte[] value, long secondsForExpired) {
        return cacheClient.String().setexBit(key, secondsForExpired, value);
    }

    /**
     * Sets cache value.
     *
     * @param key   the key
     * @param value the value
     * @return the cache value
     */
    public static Boolean setCacheValue(String key, String value) {
        return cacheClient.String().set(key, value);
    }

    /**
     * Sets cache value.
     *
     * @param key   the key
     * @param value the value
     * @return the cache value bit
     */
    public static Boolean setCacheValueBit(String key, byte[] value) {
        return cacheClient.String().setBit(key, value);
    }

    /**
     * Gets cache value.
     *
     * @param key the key
     * @return the cache value
     */
    public static String getCacheValue(String key) {
        return cacheClient.String().get(key);
    }

    /**
     * Gets cache value.
     *
     * @param key the key
     * @return the cache value
     */
    public static byte[] getCacheValueBit(String key) {
        return cacheClient.String().getBit(key);
    }

    /**
     * Gets cache expire.
     *
     * @param key the key
     * @return the cache expire
     */
    public static Long getExpire(String key) {
        return cacheClient.Key().ttl(key);
    }

    /**
     * expire.
     *
     * @param key           the key
     * @param expireSeconds the expire seconds
     * @return true /false
     */
    public static Boolean expire(String key, int expireSeconds) {
        return cacheClient.Key().expire(key, expireSeconds);
    }

    /**
     * Del key.
     *
     * @param key the key
     */
    public static void delKey(String key) {
        cacheClient.Key().del(key);
    }


    /**
     * HMSET 同时将多个 field-value (域-值)对设置到哈希表 key 中。 此命令会覆盖哈希表中已存在的域。 如果 key 不存在，一个空哈希表被创建并执行 HMSET 操作。 时间复杂度：O(N)， N 为
     * field-value 对的数量。
     */
    public static Boolean hmset(String key, Map<String, String> map) {
        return cacheClient.Hash().hmset(key, map);
    }


    public static Boolean hset(String key, String field, String val) {
        return cacheClient.Hash().hset(key, field, val);
    }

    /**
     * HGETALL 返回哈希表 key 中，所有的域和值。 在返回值里，紧跟每个域名(field name)之后是域的值(value)，所以返回值的长度是哈希表大小的两倍。 时间复杂度：O(N)， N 为哈希表的大小。
     * 返回值：以列表形式返回哈希表的域和域的值。若 key 不存在，返回空列表。
     */
    public static Map<String, String> hgetall(String key) {
        return cacheClient.Hash().hgetall(key);
    }

    /**
     * HGET 返回哈希表 key 中给定域 field 的值。 时间复杂度：O(1)
     *
     * @return 返回值:给定域的值。当给定域不存在或是给定 key 不存在时，返回 null 。
     */
    public static String hget(String key, String field) {
        return cacheClient.Hash().hget(key, field);
    }

    public static boolean lockKey(String key, String value, int seconds) {
        return cacheClient.String().setnx(key, seconds, value);
    }

    public static boolean setnx(String key, String value, int seconds) {
        // 如果已经存在key
        return cacheClient.String().setnx(key, seconds, value);
    }

    public static boolean setBit(String key, Long offset, int value) {
        cacheClient.String().setbit(key, offset, value);
        return true;
    }

    public static long getBit(String key, Long offset) {
        return cacheClient.String().getbit(key, offset);
    }
}
