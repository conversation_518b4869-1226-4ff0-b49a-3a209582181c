package com.ly.travel.car.carowner.common.configcenter;

import com.ly.tcbase.config.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.*;

public class ConfigCenterUtils {
    private static final String CENTERCONFIG_DEFAULT_APPUK = "CENTERCONFIG_DEFAULT_APPUK";
    private static final Logger logger = LoggerFactory.getLogger(ConfigCenterUtils.class);
    private static final MyConfigChanged configListener = new MyConfigChanged();
    private static String DEFAULT_APPUK = AppProfile.getAppName();

    public ConfigCenterUtils() {
    }

    public static void onConfigChanged(ConfigChanged configChanged) {
        ConfigCenterClient.onConfigChanged(configChanged);
    }

    public static String getConfigValue(String key) {
        return !StringUtils.isBlank(DEFAULT_APPUK) && !DEFAULT_APPUK.equals(AppProfile.getAppName()) ? getConfigValue(DEFAULT_APPUK, key) : getConfigValueFromSelfConfig(key);
    }

    public static String getConfigValueFromSelfConfig(String key) {
        try {
            return ConfigCenterClient.get(key);
        } catch (Exception var2) {
            return null;
        }
    }

    public static String getConfigValue(String appUk, String key) {
        try {
            return ConfigCenterClient.get(appUk, key);
        } catch (Exception var3) {
            return null;
        }
    }

    public static void changeDefaultAppUk(String defaultAppUk) {
        DEFAULT_APPUK = defaultAppUk;
    }

    public static void addConfigCenterMoniter(Class<?> moniter) {
        configListener.addConfigCenterMoniter(moniter);
    }

    public static void addConfigCenterMoniterCollection(Collection<Class<?>> moniterCollection) {
        configListener.addConfigCenterMoniterCollection(moniterCollection);
    }

    public static void main(String[] args) throws Exception {
        try {
            System.out.println(getConfigValue("test"));
        } catch (Exception var2) {
            var2.printStackTrace();
        }

        addConfigCenterMoniter(TestClass.class);

        while(true) {
            System.out.println("[ZG]\t" + TestClass.Test_Config + "\t" + TestClass.Test_Config2 + "\t" + TestClass.Test_Config3);
            Thread.sleep(5000L);
        }
    }
    static {
        ConfigCenterClient.init();
        String changedAppUk = getConfigValueFromSelfConfig("ChangedAppUk");
        if (StringUtils.isNotEmpty(changedAppUk)) {
            logger.info("从自己的统一配置中读取Changed的AppUk： " + changedAppUk);
            changeDefaultAppUk(changedAppUk);
        }

        ConfigCenterClient.onConfigChanged(configListener);
        String centerConfigDefaultAppUk = getConfigValueFromSelfConfig("CENTERCONFIG_DEFAULT_APPUK");
        if (StringUtils.isNotBlank(centerConfigDefaultAppUk)) {
            changeDefaultAppUk(centerConfigDefaultAppUk);
        }

    }

    static class TestClass implements ConfigCenterMoniter {
        @ConfigCenterField(
                configKey = "Cache_Expired_Seconds"
        )
        private static Integer Test_Config = 0;
        @ConfigCenterField(
                configKey = "Test_Key2",
                needSetEmptyWhenRemove = true
        )
        private static String Test_Config2 = null;
        @ConfigCenterField(
                appUk = "dsf.bus.ticket.order",
                configKey = "test",
                needSetEmptyWhenRemove = true
        )
        private static String Test_Config3="";

        @ConfigCenterField(
                configKey = "test2",
                needSetEmptyWhenRemove = true,
                defaultValue = "f"
        )
        public static  void aaTest_Config3(String value){
            Test_Config3=value;
        }

        TestClass() {
        }
    }

    @Target({ElementType.FIELD,ElementType.METHOD})
    @Retention(RetentionPolicy.RUNTIME)
    public @interface ConfigCenterField {
        String configKey();

        boolean needSetEmptyWhenRemove() default true;

        /**
         * 如果配置中心删除key时，会使用此默认配置
         * @return
         */
        String defaultValue() default "";

        String appUk() default "";

        boolean userSelfAppUk() default false;
    }



    public interface ConfigCenterMoniter {
        default void addIntoConfigCenterUtil() {
            ConfigCenterUtils.addConfigCenterMoniter(this.getClass());
        }
    }


    private interface IMoniterObject {
        boolean invokeObjectSetValue(String configValue);
        boolean needSetEmptyWhenRemove() ;
        String getDefaultValue() ;
    }

    private static class MoniterMethod implements IMoniterObject{
        private Method method;
        private boolean needSetEmptyWhenRemove;
        private String defaultValue;
        private Class<?> param0Class;

        public MoniterMethod(Method method, boolean needSetEmptyWhenRemove, String defaultValue) {
            this.method = method;
            this.needSetEmptyWhenRemove = needSetEmptyWhenRemove;
            this.defaultValue = defaultValue;
            Class<?>[] classArrays=method.getParameterTypes();
            if(classArrays!=null&&classArrays.length==1){
                param0Class=method.getParameterTypes()[0];
            }else{
                ConfigCenterUtils.logger.warn("ConfigCenterUtils 统一配置监控方法失败，方法参数必需为1个. " + method.getDeclaringClass().getName()+"." + method.getName());
                throw new RuntimeException(method.getDeclaringClass().getName()+"."+method.getName()+"无法接入统一配置");
            }
        }

        public boolean invokeObjectSetValue(String value) {
            if (value == null) {
                value = defaultValue;
            }
            try {
                method.setAccessible(true);
                method.invoke((Object)null, value);
            } catch (Exception var9) {
                ConfigCenterUtils.logger.warn("ConfigCenterUtils change method failed. " + method.getDeclaringClass().getName()+"." + method.getName());
                return false;
            }
            return true;
        }

        public boolean needSetEmptyWhenRemove() {
            return this.needSetEmptyWhenRemove;
        }

        public String getDefaultValue() {
            return this.defaultValue;
        }
    }
    private static class MoniterField implements IMoniterObject{
        private Field field;
        private boolean needSetEmptyWhenRemove;
        private String defaultValue;


        public MoniterField(Field field, boolean needSetEmptyWhenRemove, String defaultValue) {
            this.field = field;
            this.needSetEmptyWhenRemove = needSetEmptyWhenRemove;
            this.defaultValue = defaultValue;
        }

        public boolean invokeObjectSetValue(String value) {
            if (value == null) {
                value = defaultValue;
            }
            try {
                field.setAccessible(true);
                if (field.getType().equals(String.class)) {
                    field.set((Object)null, value);
                } else if (field.getType().equals(Long.TYPE)||field.getType().equals(Long.class)) {
                    long l = 0L;

                    try {
                        l = Long.parseLong(value);
                    } catch (Exception var8) {
                        ConfigCenterUtils.logger.warn("ConfigCenterUtils change field parse long failed. " + field.getDeclaringClass().getName() + field.getName());
                        return false;
                    }

                    field.set((Object)null, l);
                } else if (field.getType().equals(Integer.TYPE)||field.getType().equals(Integer.class)) {
                    int i = 0;
                    try {
                        i = Integer.parseInt(value);
                    } catch (Exception var7) {
                        ConfigCenterUtils.logger.warn("ConfigCenterUtils change field parse int failed. " + field.getDeclaringClass().getName() + field.getName());
                        return false;
                    }

                    field.set((Object)null, i);
                } else if (field.getType().equals(Double.TYPE)||field.getType().equals(Double.class)) {
                    double d = 0.0D;

                    try {
                        d = Double.parseDouble(value);
                    } catch (Exception var6) {
                        ConfigCenterUtils.logger.warn("ConfigCenterUtils change field parse double failed. " + field.getDeclaringClass().getName() + field.getName());
                        return false;
                    }

                    field.set((Object)null, d);
                } else if (field.getType().equals(Boolean.TYPE)||field.getType().equals(Boolean.class)) {
                    field.set((Object)null, "true".equalsIgnoreCase(value));
                }
            } catch (Exception var9) {
                ConfigCenterUtils.logger.warn("ConfigCenterUtils change field failed. " + field.getDeclaringClass().getName() + field.getName());
                return false;
            }
            return true;
        }

        public boolean needSetEmptyWhenRemove() {
            return this.needSetEmptyWhenRemove;
        }

        public String getDefaultValue() {
            return this.defaultValue;
        }
    }

    private static class MyConfigChanged extends ConfigChanged {
        private List<Class<? extends Object>> moniterList;
        private Map<String, List<IMoniterObject>> moniterMap;

        private MyConfigChanged() {
            this.moniterList = new ArrayList(20);
            this.moniterMap = new HashMap();
        }

        public void configChanged(ChangeConfigData data) {
            ConfigCenterUtils.logger.info("统一配置有配置变化推送： " + data.getVersion());
            this.processAddOrUpdateChange(data.getProjectName(), data.getAddList());
            this.processAddOrUpdateChange(data.getProjectName(), data.getUpdateList());
            this.processRemoveChange(data.getProjectName(), data.getRemoveList());
        }

        void addConfigCenterMoniter(Class<?> moniter) {
            if (!this.moniterList.contains(moniter)) {
                this.moniterList.add(moniter);
                addMoniterFields(moniter);
                addMoniterMethods(moniter);
            }
        }

        private void addMoniterFields(Class<?> moniter) {
            Field[] fields = moniter.getDeclaredFields();
            if (fields != null && fields.length > 0) {
                ConfigCenterField configAnnotation = null;
                List<IMoniterObject> moniterFieldList = null;
                String value = null;
                String monitorKeyStr = null;
                String configAppUk = null;
                int var9 = fields.length;
                IMoniterObject moniterObject=null;
                for (int i = 0; i < var9; i++) {
                    Field field = fields[i];
                    configAnnotation = field.getAnnotation(ConfigCenterField.class);
                    if (configAnnotation != null && Modifier.isStatic(field.getModifiers()) && !Modifier.isFinal(field.getModifiers())) {
                        monitorKeyStr = this.getMonitorMapKey(configAnnotation.userSelfAppUk() ? AppProfile.getAppName() : configAnnotation.appUk(), configAnnotation.configKey());
                        moniterFieldList =  this.moniterMap.get(monitorKeyStr);
                        if (moniterFieldList == null) {
                            moniterFieldList = new ArrayList(5);
                            this.moniterMap.put(monitorKeyStr, moniterFieldList);
                        }
                        moniterObject=new MoniterField(field, configAnnotation.needSetEmptyWhenRemove(), configAnnotation.defaultValue());
                        moniterFieldList.add(moniterObject);
                        configAppUk = configAnnotation.appUk();
                        if (StringUtils.isBlank(configAppUk)) {
                            configAppUk = ConfigCenterUtils.DEFAULT_APPUK;
                        }

                        if (!AppProfile.getAppName().equals(configAppUk) && !configAnnotation.userSelfAppUk()) {
                            value = ConfigCenterUtils.getConfigValue(configAppUk, configAnnotation.configKey());
                        } else {
                            value = ConfigCenterUtils.getConfigValue(configAnnotation.configKey());
                        }
                        moniterObject.invokeObjectSetValue(value);

                    }
                }
            }
        }
        private void addMoniterMethods(Class<?> moniter) {
            Method[] methods = moniter.getDeclaredMethods();
            if (methods != null && methods.length > 0) {
                ConfigCenterField configAnnotation = null;
                List<IMoniterObject> moniterFieldList = null;
                String value = null;
                String monitorKeyStr = null;
                String configAppUk = null;
                IMoniterObject moniterObject=null;

                int len = methods.length;
                for (int i = 0; i < len; i++) {
                    Method method = methods[i];
                    configAnnotation =  method.getAnnotation(ConfigCenterField.class);
                    if (configAnnotation != null && Modifier.isStatic(method.getModifiers())) {
                        monitorKeyStr = this.getMonitorMapKey(configAnnotation.userSelfAppUk() ? AppProfile.getAppName() : configAnnotation.appUk(), configAnnotation.configKey());
                        moniterFieldList = this.moniterMap.get(monitorKeyStr);
                        if (moniterFieldList == null) {
                            moniterFieldList = new ArrayList(5);
                            this.moniterMap.put(monitorKeyStr, moniterFieldList);
                        }
                        moniterObject=new MoniterMethod(method, configAnnotation.needSetEmptyWhenRemove(), configAnnotation.defaultValue());
                        moniterFieldList.add(moniterObject);
                        configAppUk = configAnnotation.appUk();
                        if (StringUtils.isBlank(configAppUk)) {
                            configAppUk = ConfigCenterUtils.DEFAULT_APPUK;
                        }

                        if (!AppProfile.getAppName().equals(configAppUk) && !configAnnotation.userSelfAppUk()) {
                            value = ConfigCenterUtils.getConfigValue(configAppUk, configAnnotation.configKey());
                        } else {
                            value = ConfigCenterUtils.getConfigValue(configAnnotation.configKey());
                        }

                        if (value == null) {
                            value = configAnnotation.defaultValue();
                        }
                        moniterObject.invokeObjectSetValue(value);
                    }
                }
            }
        }

        String getMonitorMapKey(String appUk, String key) {
            StringBuilder monitorKey = new StringBuilder();
            if (StringUtils.isNotBlank(appUk)) {
                monitorKey.append(appUk);
            } else {
                monitorKey.append(ConfigCenterUtils.DEFAULT_APPUK);
            }

            monitorKey.append("|");
            monitorKey.append(key);
            return monitorKey.toString();
        }

        void addConfigCenterMoniterCollection(Collection<Class<?>> moniterCollection) {
            if (moniterCollection != null && moniterCollection.size() > 0) {
                Iterator moniterIterator = moniterCollection.iterator();

                while(moniterIterator.hasNext()) {
                    this.addConfigCenterMoniter((Class)moniterIterator.next());
                }

            }
        }


        private void processAddOrUpdateChange(String projectName, List<ConfigData> dataList) {
            if(dataList==null){
                return;
            }
            List<IMoniterObject> moniterFieldList = null;
            ConfigData data = null;
            Iterator dataIterator = dataList.iterator();

            while(true) {
                do {
                    if (!dataIterator.hasNext()) {
                        return;
                    }

                    data = (ConfigData)dataIterator.next();
                    moniterFieldList = this.moniterMap.get(this.getMonitorMapKey(projectName, data.getKey()));
                } while(moniterFieldList == null);

                for(int i = 0; i < moniterFieldList.size(); ++i) {
                    moniterFieldList.get(i).invokeObjectSetValue(data.getValue());
                }
            }
        }

        private void processRemoveChange(String projectName, List<ConfigData> dataList) {
            if(dataList==null){
                return;
            }
            List<IMoniterObject> moniterFieldList = null;
            ConfigData data = null;
            IMoniterObject moniterObject = null;
            Iterator dataIterator = dataList.iterator();

            while(true) {
                do {
                    if (!dataIterator.hasNext()) {
                        return;
                    }

                    data = (ConfigData)dataIterator.next();
                    moniterFieldList = this.moniterMap.get(this.getMonitorMapKey(projectName, data.getKey()));
                } while(moniterFieldList == null);

                for(int i = 0; i < moniterFieldList.size(); ++i) {
                    moniterObject = moniterFieldList.get(i);
                    if (moniterObject.needSetEmptyWhenRemove()) {
                        //如果配置在统一配置里删掉了，则使用默认配置
                        moniterObject.invokeObjectSetValue(null);
                    }
                }
            }
        }
    }

}
