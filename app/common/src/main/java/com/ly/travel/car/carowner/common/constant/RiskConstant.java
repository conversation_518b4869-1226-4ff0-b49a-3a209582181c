package com.ly.travel.car.carowner.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 风控相关
 */
public interface RiskConstant {
    String APP_ID = "groundtravel.shared.mobility.carowner.core";
    String PRODUCT_LINE = "MDSFC";
    String RISK_CHANNEL = "10327";

    @AllArgsConstructor
    @Getter
    enum RiskReturnCodeEnum {

        SUCCESS(0, "成功"),

        HIT_RISK_CONTROL(1, "命中风控"),
        ;

        private int code;

        private String desc;
    }
}