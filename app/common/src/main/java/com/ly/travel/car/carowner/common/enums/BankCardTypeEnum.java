package com.ly.travel.car.carowner.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

@AllArgsConstructor
@Getter
public enum BankCardTypeEnum {

    DEBIT("DEBIT", "DEBIT_CARD", "储蓄卡"),

    CREDIT("CREDIT", "CREDIT_CARD", "信用卡"),

    CORPORATE("CORPORATE", "PUBLIC_CARD", "对公银行账户");

    public String code;

    public String remitCode;

    public String desc;

    public static String getDescByCode(String code) {
        BankCardTypeEnum categoryEnum = Arrays.stream(values()).filter(s -> s.code.equals(code)).findFirst().orElse(null);
        if (categoryEnum != null) {
            return categoryEnum.desc;
        }
        return StringUtils.EMPTY;
    }

    public static String getRemitCodeByCode(String code) {
        BankCardTypeEnum categoryEnum = Arrays.stream(values()).filter(s -> s.code.equals(code)).findFirst().orElse(null);
        if (categoryEnum != null) {
            return categoryEnum.remitCode;
        }
        return StringUtils.EMPTY;
    }
}
