package com.ly.travel.car.carowner.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BillTypeEnum {

    INCOME(1, "收入"),

    EXPENSES(2, "支出"),
    ;

    private int type;

    private String desc;

    public static BillTypeEnum getByType(Integer value) {
        for (BillTypeEnum typeEnum : BillTypeEnum.values()) {
            if (typeEnum.type == value) {
                return typeEnum;
            }
        }
        return null;
    }
}
