package com.ly.travel.car.carowner.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum BusinessBizEnum {

    DRIVER_TRIP_NOTIFY_C("1", 5, 20, "司机修改订单状态回调供应链重试"),
    ACCEPT_ORDER_NOTIFY_C("2", 2, 5, "接单回调供应链重试"),
    CANCEL_ORDER_NOTIFY_C("3", 2, 5, "取消订单回调供应链重试"),

    ;

    /**
     * 枚举编码
     */
    private String code;
    /**
     * 最大重试次数
     */
    private int retryMax;
    /**
     * 延迟多少m发送mq
     */
    private int seconds;
    /**
     * 枚举中文名称
     */
    private String name;

    /**
     * 获取枚举类型
     *
     * @param code 枚举码
     * @return BizTypeEnum
     */

    public static BusinessBizEnum getEnumByCode(String code) {
        for (BusinessBizEnum e : BusinessBizEnum.values()) {
            if (StringUtils.equals(e.getCode(), code)) {
                return e;
            }
        }
        return null;
    }
}
