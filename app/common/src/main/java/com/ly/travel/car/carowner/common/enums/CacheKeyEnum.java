package com.ly.travel.car.carowner.common.enums;


import java.util.Random;


public enum CacheKeyEnum {

    /**
     * 缓存key枚举
     */
    COMMISSION_CONFIG("core:commission_config", 60 * 10),

    //抢单时缓存数据
    GRAB_ORDER_STORE("core:grab_order_store:%s", 60 * 5),
    CHANGE_DRIVER_AMOUNT("core:change_driver_amount:%s:%s", 60 * 1),

    SYSCONFIG_CACHE("sysConfig:cache:%s", 600),

    CREATE_BILL_Z_LOCK("create_bill:lock_z:%s", 600),
    CREATE_BILL_F_LOCK("create_bill:lock_f:%s", 600),

    WX_JUM_PAGE("driver:wx_jump_page:%s", 60 * 5),

    DRIVER_WITHDRAWAL("core:driverWithdrawal:%s", 60 * 5),

    DRIVER_BILL("core:driverBill:%s", 60 * 5),

    /**
     * 接单城市配置
     */
    CITY_DELIVERY_RULE("city:delivery:rule", 60 * 60),//

    RECHARGE_COUPON("recharge:coupon:%s", 60 * 5),//

    ORDER_RISK_LOCK("core:order:risk:lock:%s", 60 * 5),

    DRIVER_COUPON_FREEZE("core:driver_coupon_freeze:%s", 60 * 5),
    FINISH_ORDER_RECHARGE_COUPON("finish:order:recharge:coupon:%s", 60 * 5),//
    DRIVER_BILL_THROTTLE("core:driver_bill_throttle:%s", 60 * 5);
    ;


    private String format;

    private int expire;

    private Class clazz;

    CacheKeyEnum(String format, int expire) {
        this.format = format;
        this.expire = expire;
    }

    CacheKeyEnum(String format, Class clazz, int expire) {
        this.format = format;
        this.expire = expire;
        this.clazz = clazz;
    }

    public String getFormat() {
        return format;
    }

    public String format(Object... values) {
        return String.format(format, values);
    }

    public int getExpire() {
        return expire;
    }

    public int getRandomExpire() {
        Random random = new Random(System.currentTimeMillis());
        return expire + random.nextInt(300);
    }

    public static CacheKeyEnum getByClass(Class clazz) {
        if (clazz == null) {
            return null;
        }
        for (CacheKeyEnum cacheKeyEnum : CacheKeyEnum.values()) {
            if (clazz.equals(cacheKeyEnum.clazz)) {
                return cacheKeyEnum;
            }
        }
        return null;
    }
}
