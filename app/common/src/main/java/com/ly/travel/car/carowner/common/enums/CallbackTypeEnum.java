package com.ly.travel.car.carowner.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @desc CallbackTypeEnum
 */
@Getter
@AllArgsConstructor
public enum CallbackTypeEnum {

    ACCEPT_ORDER(20, 4, "司机接单"),
    REASSIGN_ORDER(30, 10, "司机改派订单"),
    DRIVER_DEPARTURE(50, 8, "司机出发接乘客"),
    ARRIVE_DEPARTURE(100, 9,"司机到达乘客起点"),
    SERVICE_ON(200, 6,"乘客已上车"),
    ARRIVE_DESTINATION(260, 7,"司机到达目的地"),
    FINISH(300, 5,"行程结束"),
    CANCELLED(1000, 1,"取消订单"),

    ;

    private int status;

    private int callbackType;

    private String desc;

    public static CallbackTypeEnum getByStatus(int status) {
        for (CallbackTypeEnum value : CallbackTypeEnum.values()) {
            if (value.status == status) {
                return value;
            }
        }
        return null;
    }
}
