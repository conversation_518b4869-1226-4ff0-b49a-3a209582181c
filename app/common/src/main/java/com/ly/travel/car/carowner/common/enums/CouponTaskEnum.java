package com.ly.travel.car.carowner.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version CouponTaskEnum, 2025/9/9 23:02
 */
@Getter
@AllArgsConstructor
public enum CouponTaskEnum {

                            //1-车主认证 2-车主完单
                            /**
                             * 未知
                             */
                            UNKNOWN(0, "未知"),
                            /**
                             * 车主认证
                             */
                            DRIVER_CERTIFICATION(1, "车主认证"),
                            /**
                             * 车主完单
                             */
                            ORDER_FINISH(2, "车主完单"),;

    /**
     * code
     */
    private final int                                 code;

    /**
     * 描述
     */
    private final String                              desc;

    private static final Map<Integer, CouponTaskEnum> ENUMS = new HashMap<>();

    static {
        for (CouponTaskEnum value : values()) {
            ENUMS.put(value.code, value);
        }
    }

    /**
     * 根据code获取枚举
     *
     * @param code code
     * @return BusinessType
     */
    public static CouponTaskEnum of(Integer code) {
        if (code == null || !ENUMS.containsKey(code)) {
            return UNKNOWN;
        }
        return ENUMS.getOrDefault(code, UNKNOWN);
    }
}
