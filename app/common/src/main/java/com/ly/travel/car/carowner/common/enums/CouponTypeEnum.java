package com.ly.travel.car.carowner.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version CouponTypeEnum, 2025/9/9 22:28
 */
@Getter
@AllArgsConstructor
public enum CouponTypeEnum {

                            UNKNOWN(0, "未知"), //未知
                            COMMISSION_FREE(1, "免佣卡"),

    ;

    /**
     * code
     */
    private final int                                 code;

    /**
     * 描述
     */
    private final String                              desc;

    private static final Map<Integer, CouponTypeEnum> ENUMS = new HashMap<>();

    static {
        for (CouponTypeEnum value : values()) {
            ENUMS.put(value.code, value);
        }
    }

    /**
     * 根据code获取枚举
     *
     * @param code code
     * @return BusinessType
     */
    public static CouponTypeEnum of(Integer code) {
        if (code == null || !ENUMS.containsKey(code)) {
            return UNKNOWN;
        }
        return ENUMS.getOrDefault(code, UNKNOWN);
    }
}
