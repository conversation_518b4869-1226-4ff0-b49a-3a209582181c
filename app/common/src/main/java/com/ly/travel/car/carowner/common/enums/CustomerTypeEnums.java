package com.ly.travel.car.carowner.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum CustomerTypeEnums {

    USER_ID(1, "用户/ID"),

    USER_MOBILE(2, "用户/手机号"),

    USER_EQUIPMENT_NO(3, "用户/手机号"),

    USER_UNION_ID(4, "用户/unionId"),

    USER_PAY_NO(5, "用户/支付账号"),

    DRIVER_PLATE_NUMBER(6, "司机/车牌号"),

    DRIVER_ID(7, "司机/司机id"),

    DRIVER_MOBILE(8, "司机/手机号"),

    DRIVER_ID_NUMBER(9, "司机/身份证号"),

    USER_ID_NUMBER(10, "用户/证件号"),
    ;

    private Integer code;

    private String desc;
}