package com.ly.travel.car.carowner.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version DriverStatusEnum, 2025/9/10 00:28
 */
@Getter
@AllArgsConstructor
public enum DriverStatusEnum {

                              UNKNOWN(0, "未知"), //未知
                              UN_CERTIFIED(1, "未认证"), //未认证
                              WAIT_SYSTEM_CERTIFIED(2, "待系统认证"), //待系统认证
                              WAIT_PERSON_CERTIFIED(3, "待人工认证"), //待人工认证
                              CERTIFIED_SUCCESS(4, "认证通过"), //认证通过
                              CERTIFIED_FAIL(5, "认证不通过"),//认证不通过

    ;

    /** 枚举编码 */
    private final int                                   code;
    /** negativeOrder */
    /** 枚举描述 */
    private final String                                desc;

    private static final Map<Integer, DriverStatusEnum> ENUMS           = new HashMap<>();

    private static final Map<Integer, DriverStatusEnum> UN_CREATE_ORDER = new HashMap<>();

    static {
        for (DriverStatusEnum value : values()) {
            ENUMS.put(value.code, value);
        }
        UN_CREATE_ORDER.put(DriverStatusEnum.CERTIFIED_FAIL.code, DriverStatusEnum.CERTIFIED_FAIL);
    }

    /**
     * 根据code获取枚举
     *
     * @param code code
     * @return BusinessType
     */
    public static DriverStatusEnum getByCode(Integer code) {
        if (code == null || !ENUMS.containsKey(code)) {
            return UNKNOWN;
        }
        return ENUMS.get(code);
    }
}
