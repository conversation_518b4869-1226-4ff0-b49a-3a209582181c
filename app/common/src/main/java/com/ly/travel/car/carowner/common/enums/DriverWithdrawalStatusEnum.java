package com.ly.travel.car.carowner.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum DriverWithdrawalStatusEnum {

    SUCCESS(1, "提现成功"),

    FAILURE(2, "提现失败"),

    WITHDRAWING(3, "提现中"),

    EXCEPTION(4, "提现异常"),

    PENDING_REVIEW(5, "待审核"),
    ;

    private int status;

    private String name;

    public static String getNameByStatus(int status) {
        for (DriverWithdrawalStatusEnum withdrawalStatusEnum : values()) {
            if (withdrawalStatusEnum.status == status) {
                return withdrawalStatusEnum.name;
            }
        }
        return null;
    }
}
