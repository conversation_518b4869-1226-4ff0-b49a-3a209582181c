package com.ly.travel.car.carowner.common.enums;


public enum EnvEnum {
    TEST("test", "测试环境"), QA("qa", "测试环境"), UAT("uat", "UAT环境"), STAGE("stage", "预发环境"), PRODUCT("product", "生产环境");

    private String value;
    private String desc;

    EnvEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public boolean test() {
        return EnvEnum.TEST.equals(this) || EnvEnum.QA.equals(this) || EnvEnum.UAT.equals(this);
    }

    public boolean product() {
        return EnvEnum.PRODUCT.equals(this);
    }




}
