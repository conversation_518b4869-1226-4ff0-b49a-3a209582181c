package com.ly.travel.car.carowner.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum FreezeTypeEnum {

    NORMAL_FREEZE(0, "正常冻结"),

    SETTLEMENT_INCORRECT_FREEZE(1, "结算验证不通过冻结"),

    CUSTOMER_SERVICE_FREEZE(2, "客服订单冻结"),

    RISK_FREEZE(3, "风控冻结");

    private int type;

    private String name;

    public static String getNameByType(int type) {
        for (FreezeTypeEnum billTypeEnum : values()) {
            if (billTypeEnum.type == type) {
                return billTypeEnum.name;
            }
        }
        return null;
    }

    public static FreezeTypeEnum convertEnum(int type) {
        for (FreezeTypeEnum billTypeEnum : values()) {
            if (billTypeEnum.type == type) {
                return billTypeEnum;
            }
        }
        return null;
    }

    public boolean containsFreezeType(int binFreezeType) {
        return (binFreezeType & type) == type;
    }

    public int addFreezeType(int binFreezeType) {
        if (binFreezeType == 0) {
            return type;
        }
        if (type == 0) {
            return binFreezeType;
        }
        return binFreezeType | type;
    }

    public int subFreezeType(int binFreezeType) {
        if (binFreezeType == 0) {
            return 0;
        }
        if (type == 0) {
            return binFreezeType;
        }
        return binFreezeType & (~type);
    }

    public static List<FreezeTypeEnum> getFreezeTypeEnumList(int binFreezeType) {
        return Arrays.stream(values()).filter(item -> item.type != 0 && item.containsFreezeType(binFreezeType)).collect(Collectors.toList());
    }
}
