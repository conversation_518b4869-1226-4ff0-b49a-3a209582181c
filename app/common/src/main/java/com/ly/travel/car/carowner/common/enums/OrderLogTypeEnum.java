package com.ly.travel.car.carowner.common.enums;

public enum OrderLogTypeEnum {
    CREATE_ORDER(1, "创单","创单成功"),
    CONFIRM_YOUR_PEERS(2, "确认同行","司机:%s,手机:%s,车辆:%s,确认同行"),
    ORDER_SUCCESSFULLY_RECEIVED(4, "接单成功","司机:%s,手机:%s,车辆:%s,接单成功"),
    CANCEL(5, "取消","%s发起取消订单,原因:%s"),
    REFUND(6, "退款","订单发生退款，金额:%s"),
    GO_PICK_UP_THE_PASSENGER_CAR(7, "去接乘客","去接乘客"),
    ARRIVE_AT_THE_PASSENGER_S_PLACE(8, "到达乘客上车点","到达乘客上车点"),
    THE_PASSENGER_HAS_BOARDED_THE_BUS(9, "乘客已上车","乘客已上车"),
    PASSENGERS_GET_OFF_THE_BUS(10, "乘客已下车","乘客已下车"),

    SERVICE_FREEZE(11, "冻结","人工冻结订单"),
    SERVICE_UNFREEZE(12, "解冻","人工解冻订单"),
    PAY_NOTIFY(13, "支付通知", "乘客支付：%s元"),
    PASSENGER_GET_ON(14, "乘客点击已上车", "乘客点击已上车"),
    PASSENGER_GET_OFF(15, "乘客点击已到达", "乘客点击已到达"),
    AUTO_COMPLETE(16, "自动完单", "司乘长期未操作、行程超时异常自动完单"),
    CARD_REFUND_ORDER(17, "退免抽佣金额", "退款后自动退司机免抽佣金额%s"),

    FREEZE_COUPON(18, "锁定券", "接单成功，锁定卡券"),
    UNFREEZE_COUPON(19, "解锁券", "取消订单，释放卡券"),
    USED_COUPON(20, "核销券", "订单完成，核销卡券，免佣金额：%s元"),
    ;

    private int type;
    private String name;
    private String remark;


    OrderLogTypeEnum(int type, String name,String remark) {
        this.type = type;
        this.name = name;
        this.remark = remark;
    }

    public int getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public String getRemark() {
        return remark;
    }



    public String formatRemark(Object... args){
        return String.format(remark,args);
    }

    public static OrderLogTypeEnum getByType(Integer type) {
        if (null == type) {
            return null;
        }
        OrderLogTypeEnum[] elements = values();
        for (OrderLogTypeEnum element : elements) {
            if (element.getType() == type.intValue()) {
                return element;
            }
        }
        return null;
    }


}
