package com.ly.travel.car.carowner.common.enums;

public enum OrderStatusEnum {

    DISPATCHED(1, "已派车"), COMPLETED(2, "已完成"),
    CANCELLED(3, "取消")
    ;

    private int status;

    private String desc;

    OrderStatusEnum(int status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public int getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    public static OrderStatusEnum getByValue(Integer status) {
        if (null == status) {
            return null;
        }
        OrderStatusEnum[] elements = values();
        for (OrderStatusEnum element : elements) {
            if (element.getStatus() == status.intValue()) {
                return element;
            }
        }
        return null;
    }


    public static boolean isCompleted(int orderStatus) {
        // 完成：6
        return orderStatus == COMPLETED.getStatus();
    }

}
