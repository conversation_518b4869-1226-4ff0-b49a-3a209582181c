package com.ly.travel.car.carowner.common.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

public enum OrderTagEnum {
    AUTO_COMPLETE("AUTO_COMPLETE", "自动完单"),
    FREE_COMMISSION_CARD("FREE_COMMISSION_CARD", "免佣卡订单"),
    WITHDRAWAL_RISK("WITHDRAWAL_RISK", "被提现风控订单"),
    ;

    OrderTagEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private String code;


    private String desc;

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public static String getDescByCode(String code) {
        OrderTagEnum categoryEnum = Arrays.stream(values()).filter(s -> s.code.equals(code)).findFirst().orElse(null);
        if (categoryEnum != null) {
            return categoryEnum.desc;
        }
        return StringUtils.EMPTY;
    }


}
