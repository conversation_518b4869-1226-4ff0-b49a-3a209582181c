package com.ly.travel.car.carowner.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @desc 订单行程状态
 */
@Getter
@AllArgsConstructor
public enum OrderTripStateEnum {

    SET_OUT(1, CallbackTypeEnum.DRIVER_DEPARTURE, "司机已出发，去接乘客"),
    ARRIVED(2, CallbackTypeEnum.ARRIVE_DEPARTURE, "司机到达起点"),
    SERVICE(3, CallbackTypeEnum.SERVICE_ON, "乘客已上车，服务开始"),
    DEST(4, CallbackTypeEnum.ARRIVE_DESTINATION, "到达目的地，乘客已下车"),
    GET_ON(5, CallbackTypeEnum.SERVICE_ON, "乘客点击已上车"),

    ;

    private Integer code;

    private CallbackTypeEnum callbackTypeEnum;

    private String desc;


    public static OrderTripStateEnum of(CallbackTypeEnum callbackTypeEnum) {
        for (OrderTripStateEnum value : OrderTripStateEnum.values()) {
            if (value.callbackTypeEnum == callbackTypeEnum) {
                return value;
            }
        }
        return null;
    }
}
