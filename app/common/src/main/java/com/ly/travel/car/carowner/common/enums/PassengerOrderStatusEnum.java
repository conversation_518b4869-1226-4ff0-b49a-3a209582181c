package com.ly.travel.car.carowner.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @desc PassengerOrderStatusEnum
 */
@Getter
@AllArgsConstructor
public enum PassengerOrderStatusEnum {

    CREATE_SUCCESS(0, "已下单"),

    SENT(1, "已派车"),
    FINISH(2, "已完成"),
    CANCELED(3, "取消"),

    ACCEPT_ORDER(4, "抢单中"),

    ;

    private Integer code;

    private String desc;
}
