package com.ly.travel.car.carowner.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum ResultEnum {

    SUCCESS("200", "成功"),

    INTERNAL_SERVER_ERROR("500", "服务器内部异常"),

    SUBMIT_ERROR("101", "提交异常，请重试"),

    WITHDRAWAL_AMOUNT_ERROR("102", "提现金额异常，请重试"),

    ACCOUNT_STATUS_ERROR("103", "账户状态异常，如有问题请联系客服"),

    CAR_BAND_ERROR("104", "请先绑定银行卡"),

    THE_DAY_WITHDRAWAL_COUNT_ERROR("105", "已达今日提现次数上限，请明日再来"),

    MAXIMUM_WITHDRAWAL_AMOUNT_PER_TRANSACTION_ERROR("106", "已达单次提现金额上限"),

    MAXIMUM_WITHDRAWAL_AMOUNT_ERROR("107", "已达提现金额上限，请改日再来"),

    WITHDRAWAL_ACCOUNT_ERROR("108", "提现账号异常"),

    WITHDRAWAL_PRICE_VERIFY_ERROR("109", "有订单存在争议，可提现金额已更新"),

    APPLY_SUBMIT_SUCCESS("100", "申请提交成功，提现金额%s（元）"),

    NETWORK_ERROR("110", "网络异常，请稍后重试"),
    ;

    private String code;

    private String desc;
}