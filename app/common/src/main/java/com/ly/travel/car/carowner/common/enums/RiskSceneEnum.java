package com.ly.travel.car.carowner.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum RiskSceneEnum {

    WITHDRAWAL_AUDIT(1, "提现审核", "21-1"),

    ORDER_COMPLETE(2, "订单完结", "8-2"),

    ORDER_WITHDRAW(3, "订单提现", "22-1"),

    ORDER_UNFREEZE(4, "订单解冻", "23-1"),

    DRIVER_WITHDRAW(5, "司机提现", "11-1");

    /**
     * code
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 风控接口的场景值
     */
    private String scene;
}