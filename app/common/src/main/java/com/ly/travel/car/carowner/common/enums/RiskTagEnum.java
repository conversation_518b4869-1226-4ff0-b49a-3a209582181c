package com.ly.travel.car.carowner.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum RiskTagEnum {

    ORDER_FREEZE_TIME_UPDATE_TAG("car_risk_real_car_owner_ht_tag", "订单冻结时间更新"),

    ;

    private String tag;

    private String desc;

    public static RiskTagEnum getByTag(String tag) {
        for (RiskTagEnum value : RiskTagEnum.values()) {
            if (value.tag.equals(tag)) {
                return value;
            }
        }
        return null;
    }
}
