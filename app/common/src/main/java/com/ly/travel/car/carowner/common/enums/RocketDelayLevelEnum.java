package com.ly.travel.car.carowner.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version Id: RocketDelayLevelEnum, v 0.1 2024/3/15 14:04 zss48204 Exp $
 */
@Getter
@AllArgsConstructor
public enum RocketDelayLevelEnum {
    /**
     * 1秒
     */
    SECOND_1(1),
    /**
     * 5秒
     */
    SECOND_5(2),
    /**
     * 10秒
     */
    SECOND_10(3),
    /**
     * 30秒
     */
    SECOND_30(4),
    /**
     * 1分钟
     */
    MINUTE_1(5),
    /**
     * 2分钟
     */
    MINUTE_2(6),
    /**
     * 3分钟
     */
    MINUTE_3(7),
    /**
     * 4分钟
     */
    MINUTE_4(8),
    /**
     * 5分钟
     */
    MINUTE_5(9),
    /**
     * 6分钟
     */
    MINUTE_6(10),
    /**
     * 7分钟
     */
    MINUTE_7(11),
    /**
     * 8分钟
     */
    MINUTE_8(12),
    /**
     * 9分钟
     */
    MINUTE_9(13),
    /**
     * 10分钟
     */
    MINUTE_10(14),
    /**
     * 20分钟
     */
    MINUTE_20(15),
    /**
     * 30分钟
     */
    MINUTE_30(16),
    /**
     * 1小时
     */
    HOUR_1(17),
    /**
     * 2小时
     */
    HOUR_2(18),
    ;

    /**
     * level
     */
    private final Integer level;
}