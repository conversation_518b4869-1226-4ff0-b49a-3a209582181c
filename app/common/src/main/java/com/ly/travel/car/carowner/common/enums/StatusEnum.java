package com.ly.travel.car.carowner.common.enums;


public enum StatusEnum {

    DELETED(0), ENABLED(1), DISABLED(2);

    private int value;

    StatusEnum(int num) {
        this.value = num;
    }

    public int getValue() {
        return value;
    }

    public boolean equalsValue(Integer value) {
        if (null == value) {
            return false;
        }
        return this.value == value.intValue();
    }

    public static StatusEnum getByValue(Integer value) {
        if (null == value) {
            return null;
        }
        StatusEnum[] elements = values();
        for (StatusEnum element : elements) {
            if (element.getValue() == value.intValue()) {
                return element;
            }
        }
        return null;
    }

}
