package com.ly.travel.car.carowner.common.enums;

public enum SysConfigEnum {

    /**
     * 邀请码配置：Is_NoCode 控制无码展示开关，FreeCode默认邀请码
     */
    WXMIN_ACCESSTOKEN("wxmin_access_token_", ""),
    WXMIN_JS_ACCESSTOKEN("wxmin_js_access_token_", ""),
    /**
     * 订单结算差值配置
     */
    ORDER_SETTLEMENT_DIFFERENCE_AMOUNT("order_settlement_difference_amount", "1"),

    /**
     * 司机账户余额小于0机器人配置
     */
    DRIVER_ACCOUNT_ERROR_NOTICE_KEY("driver_account_error_notice", ""),

    /**
     * 结算验证金额不一致异常通知
     */
    SETTLEMENT_VERIFICATION_ERROR_NOTICE_KEY("settlement_verification_error_notice", ""),


    /**
     * 乘客点击已上车强制校验
     */
    PASSENGER_GET_ON_FORCE_VERIFY_KEY("passenger_get_on_force_verify", "0"),

    /**
     * 易宝失败提醒
     */
    YEE_PAY_FAIL_NOTICE("yee_pay_fail_notice", ""),

    /**
     * 车主认证，系统审核不通过/系统审核成功，待人工审核 风控 企业微信机器人模板id
     */
    VERIFY_NOTIFY_TEMPLE_ID("verify_notify_temple_id", "beee67f7-e05c-4a69-87e3-015c7fa0d7e6"),

    /**
     * 风控通知员工工号
     */
    riskNotifyUser("riskNotifyUser", "1201550"),

    /**
     * 接单配置司机每日取消配置
     */
    DRIVER_DAY_ORDER_CANCEL("driver_day_order_cancel", "15"),

    /**
     * 司机活动卡券配置
     */
    DRIVER_ACTIVITY_COUPON_CONFIG("driver_activity_coupon_config", ""),

    /**
     * 提现风控通知机器人
     */
    WITHDRAWAL_RISK_NOTICE("withdrawal_risk_notice", ""),

    /**
     * 提现风控通知用户
     */
    WITHDRAWAL_RISK_USER("withdrawal_risk_user", ""),
    ;

    private String key;
    private String value;

    SysConfigEnum(String key, String value) {
        this.value = value;
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public String getKey() {
        return key;
    }
}
