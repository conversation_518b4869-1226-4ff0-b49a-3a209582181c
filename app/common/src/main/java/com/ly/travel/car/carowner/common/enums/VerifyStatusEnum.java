package com.ly.travel.car.carowner.common.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version VerifyStatusEnum, 2025/9/10 01:04
 */
@Getter
public enum VerifyStatusEnum {

                              /**
                               * 未认证
                               */
                              NO_VERIFY(1, "未认证"),
                              /**
                               * 待系统认证
                               */
                              VERIFY_SYSTEM_ING(2, "待系统认证"),
                              /**
                               * 待人工认证
                               */
                              VERIFY_PERSON_ING(3, "待人工认证"),
                              /**
                               * 认证通过
                               */
                              VERIFY_ADOPT(4, "认证通过"),
                              /**
                               * 认证不通过
                               */
                              VERIFY_REJECT(5, "认证不通过"),
                              /**
                               * 认证已过期
                               */
                              VERIFY_TIMEOUT(6, "认证已过期"),

    ;

    VerifyStatusEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private Integer code;
    private String  msg;

    public void setCode(Integer code) {
        this.code = code;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static VerifyStatusEnum get(Integer code) {
        for (VerifyStatusEnum orderStatusEnum : VerifyStatusEnum.values()) {
            if (orderStatusEnum.getCode().equals(code)) {
                return orderStatusEnum;
            }
        }
        return null;
    }

    public static String getByCode(Integer code) {
        for (VerifyStatusEnum verifyStatusEnum : VerifyStatusEnum.values()) {
            if (verifyStatusEnum.getCode().equals(code)) {
                return verifyStatusEnum.getMsg();
            }
        }
        return StringUtils.EMPTY;
    }

    private static String getSex(String idCardNo) {
        //18位身份证号取17位数字，15位身份证号取15位数字。单数为男性，双数为女性
        String sex = "";
        if (idCardNo.length() == 18) {
            sex = idCardNo.substring(idCardNo.length() - 2, idCardNo.length() - 1);

        } else if (idCardNo.length() == 15) {
            sex = idCardNo.substring(idCardNo.length() - 1);
        }
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(sex)) {
            Integer s = Integer.parseInt(sex);
            if (s % 2 == 0) {
                return "女";
            } else {
                return "男";
            }
        }
        return "";
    }
}
