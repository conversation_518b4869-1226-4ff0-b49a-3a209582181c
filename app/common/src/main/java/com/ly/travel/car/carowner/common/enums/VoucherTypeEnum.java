package com.ly.travel.car.carowner.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum VoucherTypeEnum {

    /**
     * 业务单据类型枚举
     */
    DRIVER_ORDER_SETTLEMENT(1, "订单"),

    DRIVER_WITHDRAWAL_APPLY(2, "提现申请"),

    DRIVER_APPEAL(3, "调账"),

    DRIVER_WITHDRAWAL_FAIL_BACK(4, "提现失败回冲"),

    DRIVER_ORDER_FREE_COMMISSION(5, "免抽佣奖励"),

    ;

    private int type;

    private String desc;
}
