package com.ly.travel.car.carowner.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@AllArgsConstructor
@Getter
public enum WithdrawLogTypeEnum {

    APPLY_WITHDRAW(1, "申请提现"),

    AUDIT(2, "审核"),

    PAY(3, "支付"),
    ;

    public int code;

    public String desc;

    public static String getDescByCode(int code) {
        WithdrawLogTypeEnum categoryEnum = Arrays.stream(values()).filter(s -> s.code == code).findFirst().orElse(null);
        if (categoryEnum != null) {
            return categoryEnum.desc;
        }
        return "";
    }
}
