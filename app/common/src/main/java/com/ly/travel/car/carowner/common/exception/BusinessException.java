package com.ly.travel.car.carowner.common.exception;


import com.ly.travel.car.carowner.common.enums.ResultEnum;

public class BusinessException extends BaseException {


    public BusinessException(String code, String message) {
        super(code, message);
    }

    public BusinessException(String code, String message, Throwable cause) {
        super(code, message, cause);
    }

    public BusinessException(String code, Throwable cause) {
        super(code, cause);
    }

    public BusinessException(String message) {
        super("500", message);
    }

    public BusinessException(ResultEnum resultEnum) {
        super(resultEnum.getCode(), resultEnum.getDesc());
    }
}
