package com.ly.travel.car.carowner.common.exception;



public class DriverAccountException extends BusinessException {

    public DriverAccountException(String message) {
        super("222", message);
    }

    public DriverAccountException(String code, String message) {
        super(code, message);
    }

    public DriverAccountException(String code, String message, Throwable cause) {
        super(code, message, cause);
    }

    public DriverAccountException(String code, Throwable cause) {
        super(code, cause);
    }
}
