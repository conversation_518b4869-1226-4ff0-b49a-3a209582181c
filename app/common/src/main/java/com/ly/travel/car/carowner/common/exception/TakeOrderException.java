package com.ly.travel.car.carowner.common.exception;



public class TakeOrderException extends BusinessException {

    public TakeOrderException( String message) {
        super("222", message);
    }

    public TakeOrderException(String code, String message) {
        super(code, message);
    }

    public TakeOrderException(String code, String message, Throwable cause) {
        super(code, message, cause);
    }

    public TakeOrderException(String code, Throwable cause) {
        super(code, cause);
    }
}
