package com.ly.travel.car.carowner.common.lock;

import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Function;


@Component
public class DistributedRedisLock {
    private static final Logger logger = LoggerFactory.getLogger(DistributedRedisLock.class);

    private RedissonClient redisson;
    private static final String LOCK_TITLE = "core:redisLock:";
    private static final String LOCK_VALUE_TITLE = "core:value:redisLock:";
    private static final String LOCK_SETNX_VALUE_TITLE = "core:value:redisLockNX:";


    @Autowired
    public DistributedRedisLock(RedissonManager redissonManager) {
        redisson = redissonManager.getRedisson();
    }



    /**
     * 带有超时时间的超时获取锁操作 默认等待5秒钟； 超过5秒还没有获取到锁， 则放弃加锁操作 锁获取成功后， 超时时间默认为30s，防止进程异常退出没有释放锁时造成死锁
     *
     * @param lockName
     *            要锁的资源名称
     * @return true: 获取锁成功； false: 没有获取到锁
     */
    public RLock tryAcquire(String lockName, String lockValue) {
        return tryAcquire(lockName, lockValue, 5, 30, TimeUnit.SECONDS);
    }

    /**
     * 带有超时时间的超时获取锁操作
     *
     * @param lockName
     *            要锁的资源名称
     * @param waitTime
     *            等待获取锁操作的时长
     * @param leaseTime
     *            锁获取成功后，锁保持的超时时间，防止进程异常退出没有释放锁时造成死锁
     * @param timeUnit
     *            单位（分钟，或秒等）
     * @return true: 获取锁成功； false: 没有获取到锁
     */
    public RLock tryAcquire(String lockName, String lockValue, long waitTime, long leaseTime, TimeUnit timeUnit) {
        String key = LOCK_TITLE + lockName;
        try {
            RLock myLock = redisson.getLock(key);
            boolean locked = myLock.tryLock(waitTime, leaseTime, timeUnit);

            if (locked) {
                logger.info("lockName:{}, thread-name:{} 获取锁成功!", key, Thread.currentThread().getName());
                return myLock;
            } else {
                logger.error("lockName:{}, thread-name:{} 获取锁等待超时，放弃! 等待时间:{}", key, Thread.currentThread().getName(),
                        waitTime);
            }
        } catch (InterruptedException e) {
            logger.error("lockName:{}, thread-name:{} 获取锁异常！", key, Thread.currentThread().getName(),
                    waitTime, e);
        }
        return null;
    }



    public boolean release(RLock myLock,String lockName, String lockValue) {
        String key = LOCK_TITLE + lockName;
        String keyValue = LOCK_VALUE_TITLE + lockName;
        String nxKey = LOCK_SETNX_VALUE_TITLE + lockName;
        boolean success = true;
        try {
            if(myLock!=null&&myLock.isLocked()) {
                myLock.forceUnlock();
            }
            logger.info("lockName:{}, thread-name:{} 释放锁成功! 锁定状态:{}", key, Thread.currentThread().getName(),
                myLock.isLocked());

            return true;
        } catch (Exception e) {
            logger.error("lockName:{}, thread-name:{} 释放锁失败! ", key, Thread.currentThread().getName(),
                e);
            return false;
        }
    }

    public void lockWithUnlock(String lockName, Consumer<Boolean> consumer) {
        RLock lock=null;
        try {
            lock = tryAcquire(lockName, null,10L, 30L,TimeUnit.SECONDS);
            consumer.accept(Objects.nonNull(lock));
        } finally {
            release(lock,lockName,null);
        }
    }

    public <T> T lockWithUnlockAndResult(String lockName, Function<Boolean, T> consumer) {
        RLock lock=null;
        try {
            lock = tryAcquire(lockName, null,10L, 30L,TimeUnit.SECONDS);
            return consumer.apply(Objects.nonNull(lock));
        } finally {
            release(lock,lockName,null);
        }
    }


}
