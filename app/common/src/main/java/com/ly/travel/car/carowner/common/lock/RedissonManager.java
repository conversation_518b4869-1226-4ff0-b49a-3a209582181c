package com.ly.travel.car.carowner.common.lock;

import com.ly.travel.car.carowner.common.redis.CacheConfig;
import com.ly.travel.car.carowner.common.redis.RedisConfig;
import com.ly.travel.car.carowner.common.redis.RedisConfigManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.redisson.config.ClusterServersConfig;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 使用Redisson实现集群模式下的分布式锁（测试）
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RedissonManager {
    private static Logger logger = LoggerFactory.getLogger(RedissonManager.class);



    private final String RAtomicName = "genId_";

    private Config config = new Config();

    private RedissonClient redisson = null;



    private void init(){
        try{
            CacheConfig cacheConfig = RedisConfigManager.getRedisConfigCollection();
            log.info("env:{}, clusterNodes=<{}>",  cacheConfig);

            String ipPortPair=null;

            String password = null;


                if (cacheConfig != null && !CollectionUtils.isEmpty(cacheConfig.getRedisConfig())) {
                    for (RedisConfig redisConfig : cacheConfig.getRedisConfig()) {
                        ipPortPair = "redis://" + redisConfig.getIp();
                        if (StringUtils.isBlank(password) && StringUtils.isNoneBlank(redisConfig.getPassword())) {
                            password = redisConfig.getPassword();
                        }
                    }
                }

            log.info("clusterNodeAarry:{}", ipPortPair);

            SingleServerConfig singleServerConfig = config.useSingleServer();
            singleServerConfig.setAddress(ipPortPair);
            if (StringUtils.isNotBlank(password)) {
                singleServerConfig.setPassword(password);
            }

            redisson = Redisson.create(config);

            RAtomicLong atomicLong = redisson.getAtomicLong(RAtomicName);
            atomicLong.set(1);//自增设置为从1开始
        }catch (Exception e){
            log.error("加载redission失败",e);
        }
    }

    public RedissonClient getRedisson(){
        if(redisson == null){
            init(); //初始化
        }
        return redisson;
    }

    /** 获取redis中的原子ID */
    public Long nextID(){
        RAtomicLong atomicLong = getRedisson().getAtomicLong(RAtomicName);
        atomicLong.incrementAndGet();
        return atomicLong.get();
    }
}
