package com.ly.travel.car.carowner.common.redis;

import java.util.ArrayList;
import java.util.List;

public class CacheConfig {
    List<RedisConfig> redisConfig = new ArrayList<>();
    String name;
    private String type; // S 代表redis为单机模式， M代表为主从模式，C代表集群模式
    private String masterName;

    public List<RedisConfig> getRedisConfig() {
        return redisConfig;
    }

    public void setRedisConfig(List<RedisConfig> redisConfig) {
        this.redisConfig = redisConfig;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getMasterName() {
        return masterName;
    }

    public void setMasterName(String masterName) {
        this.masterName = masterName;
    }
}
