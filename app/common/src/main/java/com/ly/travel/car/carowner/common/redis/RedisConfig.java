package com.ly.travel.car.carowner.common.redis;

public class RedisConfig {
    private String ip;
    private String password;
    private int timeOut = 5000;
    private int maxPool = 20;
    private int minPool = 3;
    private boolean sentinel = false;

    public boolean isSentinel() {
        return sentinel;
    }

    public void setSentinel(boolean sentinel) {
        this.sentinel = sentinel;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public int getTimeOut() {
        return timeOut;
    }

    public void setTimeOut(int timeOut) {
        this.timeOut = timeOut;
    }

    public int getMaxPool() {
        return maxPool;
    }

    public void setMaxPool(int maxPool) {
        this.maxPool = maxPool;
    }

    public int getMinPool() {
        return minPool;
    }

    public void setMinPool(int minPool) {
        this.minPool = minPool;
    }
}
