package com.ly.travel.car.carowner.common.redis;

import com.ly.tcbase.config.AppProfile;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.StringReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;

public class RedisConfigManager {
    private static Logger logger = LoggerFactory.getLogger(RedisConfigManager.class);
    private static String url =
            "http://tccomponent.17usoft.com/tcconfigcenter6/v6/getspecifickeyvalue/%s/%s/TCBase.Cache.v3";


    private static String suffix="redis.proxy";
    private static String defaultProjectName = AppProfile.getAppName();

    public static  CacheConfig getRedisCacheConfig(String env) {
        return getRedisConfig(defaultProjectName, env);
    }


    public static CacheConfig getRedisConfigCollection() {
        CacheConfig redisConfig = getRedisConfig(defaultProjectName, AppProfile.getEnvironment());

        return redisConfig;
    }

    public static  CacheConfig getRedisConfig(String projectName, String env) {
        String config = getConfigString(projectName, env);
        try {
            SAXReader reader = new SAXReader();
            reader.setEncoding("UTF-8");
            Document document = reader.read(new StringReader(config));
            Element element = document.getRootElement();
            if (!"tcbase.cache".equals(element.getName())) {
                return null;
            }
            Iterator<Element> iterator = element.elementIterator("cache");
            while (iterator.hasNext()) {
                Element cacheElement = iterator.next();
                CacheConfig curCacheConfig = getCacheConfig(cacheElement);
                if (curCacheConfig == null) {
                    continue;
                }
                if(curCacheConfig.getName().endsWith(suffix)){
                    return curCacheConfig;
                }
            }
        } catch (Exception e) {
            logger.error("获取redis服务节点信息失败！ ", e);
        }
        logger.error("获取redis服务节点信息失败，为空！ ");

        return null;
    }

    private static String getConfigString(String projectName, String env) {
        String urlStr = String.format(url, env, projectName);
        BufferedReader in = null;
        StringBuilder result = new StringBuilder();
        try {
            URL url = new URL(urlStr);
            HttpURLConnection conn = (HttpURLConnection)url.openConnection();
            String auth = projectName + ":" + projectName;
            byte[] authEncBytes = Base64.getEncoder().encode(auth.getBytes());
            String authStringEnc = new String(authEncBytes);
            conn.setRequestProperty("Authorization", "Basic " + authStringEnc);
            conn.setRequestMethod("GET");
            conn.connect();
            in = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
        } catch (IOException e) {
            logger.error("", e);
            throw new RuntimeException(e);
        } finally {
            try {
                if (in != null) {
                    in.close();
                }
            } catch (IOException ex) {
                logger.error("", ex);
            }
        }
        return result.toString();
    }

    private static CacheConfig getCacheConfig(Element cacheElement) {
        CacheConfig cacheConfig = new CacheConfig();
        String nameAttr = cacheElement.attributeValue("name");
        if (nameAttr == null || "".equals(nameAttr)) {
            return null;
        }
        cacheConfig.setName(nameAttr.trim());
        cacheConfig
                .setType(cacheElement.attributeValue("type") == null ? "S" : cacheElement.attributeValue("type").trim());
        Iterator<Element> redisIterator = cacheElement.elementIterator("redis");
        while (redisIterator.hasNext()) {
            Element redisElement = redisIterator.next();
            RedisConfig curRedisConfigNode = getRedisNodeConfig(redisElement);
            cacheConfig.getRedisConfig().add(curRedisConfigNode);
        }
        return cacheConfig;
    }

    private static RedisConfig getRedisNodeConfig(Element redisElement) {
        RedisConfig redisConfig = new RedisConfig();
        redisConfig.setIp(redisElement.attributeValue("ip") == null ? null : redisElement.attributeValue("ip").trim());
        redisConfig.setSentinel(redisElement.attributeValue("sentinel") != null
                && Boolean.parseBoolean(redisElement.attributeValue("sentinel").trim()));
        redisConfig.setTimeOut(redisElement.attributeValue("timeOut") == null ? 1000
                : Integer.parseInt(redisElement.attributeValue("timeOut").trim()));
        redisConfig.setPassword(
                redisElement.attributeValue("password") == null ? null : redisElement.attributeValue("password").trim());
        return redisConfig;
    }

    public static void main(String[] args) {
        // test, qa , uat
        CacheConfig redisConfig = getRedisConfig(AppProfile.getAppName(), AppProfile.getEnvironment());
        System.out.println(redisConfig);

    }
}
