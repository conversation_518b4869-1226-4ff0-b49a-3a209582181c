package com.ly.travel.car.carowner.common.thread;

import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicLong;

public class MTThreadFactory implements ThreadFactory {

    private ThreadFactory threadFactory = Executors.defaultThreadFactory();
    String nameFormat;
    Thread.UncaughtExceptionHandler uncaughtExceptionHandler;
    final AtomicLong count = new AtomicLong(0L);

    public MTThreadFactory(String nameFormat, Thread.UncaughtExceptionHandler uncaughtExceptionHandler) {
        this.nameFormat = nameFormat + "-";
        this.uncaughtExceptionHandler = uncaughtExceptionHandler;
    }

    public Thread newThread(Runnable runnable) {
        Thread thread = threadFactory.newThread(runnable);
        if (nameFormat != null) {
            thread.setName(nameFormat + count.getAndIncrement());
        }

        if (uncaughtExceptionHandler != null) {
            thread.setUncaughtExceptionHandler(uncaughtExceptionHandler);
        }

        return thread;
    }
}
