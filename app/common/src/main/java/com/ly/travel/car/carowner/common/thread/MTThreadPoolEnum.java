package com.ly.travel.car.carowner.common.thread;

import java.util.concurrent.*;

/**
 * <AUTHOR>
 * 线程池定义枚举
 */
public enum MTThreadPoolEnum {
    WXWORK_ALTER("WxWorkAlert-Thread",1,1,10, new ThreadPoolExecutor.DiscardPolicy(),new ArrayBlockingQueue<>(20)),
    SMS_NOTICE("SMS-Thread",1,3,30, new ThreadPoolExecutor.DiscardPolicy(),new ArrayBlockingQueue<>(1000)),
    CXPUSH_NOTICE("CxPush-Thread",5,10,60, new ThreadPoolExecutor.DiscardPolicy(),new ArrayBlockingQueue<>(5000)),
    SEND_SMS("SendSms-Thread",1,3,30, new ThreadPoolExecutor.DiscardPolicy(),new ArrayBlockingQueue<>(1000)),
    UPDATE_PAY_STATUS("UpdatePayStatus-Thread",1, new ThreadPoolExecutor.CallerRunsPolicy(),true),
    ;

    MTThreadPoolEnum(String threadName, Integer coreNum,RejectedExecutionHandler rejectedExecutionHandler,boolean scheduleThreadFlag){
        this.scheduleThreadFlag=true;
        this.threadName = threadName;
        this.maxNum=coreNum;
        this.minNum=coreNum;
        this.rejectedExecutionHandler = rejectedExecutionHandler;
    }

    MTThreadPoolEnum(String threadName, Integer minNum, Integer maxNum, Integer survivalSeconds, RejectedExecutionHandler rejectedExecutionHandler) {
        this(threadName, minNum, maxNum, survivalSeconds, rejectedExecutionHandler, new SynchronousQueue<>());
    }

    MTThreadPoolEnum(String threadName, Integer minNum, Integer maxNum, Integer survivalSeconds, RejectedExecutionHandler rejectedExecutionHandler, BlockingQueue<Runnable> blockingQueue) {
        this.threadName = threadName;
        this.minNum = minNum;
        this.maxNum = maxNum;
        this.rejectedExecutionHandler = rejectedExecutionHandler;
        this.survivalSeconds = survivalSeconds;
        this.blockingQueue = blockingQueue;
    }

    public boolean isScheduleThreadFlag() {
        return scheduleThreadFlag;
    }

    public void setScheduleThreadFlag(boolean scheduleThreadFlag) {
        this.scheduleThreadFlag = scheduleThreadFlag;
    }

    private boolean scheduleThreadFlag;
    private String threadName;
    private Integer minNum;
    private Integer maxNum;
    private RejectedExecutionHandler rejectedExecutionHandler;
    private Integer survivalSeconds;
    private BlockingQueue<Runnable> blockingQueue;

    public BlockingQueue<Runnable> getBlockingQueue() {
        return blockingQueue;
    }

    public void setBlockingQueue(BlockingQueue<Runnable> blockingQueue) {
        this.blockingQueue = blockingQueue;
    }

    public Integer getSurvivalSeconds() {
        return survivalSeconds;
    }

    public void setSurvivalSeconds(Integer survivalSeconds) {
        this.survivalSeconds = survivalSeconds;
    }

    public Integer getMaxNum() {
        return maxNum;
    }

    public void setMaxNum(Integer maxNum) {
        this.maxNum = maxNum;
    }

    public String getThreadName() {
        return threadName;
    }

    public void setThreadName(String threadName) {
        this.threadName = threadName;
    }

    public Integer getMinNum() {
        return minNum;
    }

    public void setMinNum(Integer minNum) {
        this.minNum = minNum;
    }

    public RejectedExecutionHandler getRejectedExecutionHandler() {
        return rejectedExecutionHandler;
    }

    public void setRejectedExecutionHandler(RejectedExecutionHandler rejectedExecutionHandler) {
        this.rejectedExecutionHandler = rejectedExecutionHandler;
    }
}
