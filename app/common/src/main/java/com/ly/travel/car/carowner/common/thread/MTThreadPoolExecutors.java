package com.ly.travel.car.carowner.common.thread;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.*;

/**
 * <AUTHOR>
 * 线程池操作类
 */
@Slf4j
public class MTThreadPoolExecutors {

    private ThreadPoolExecutor threadPoolExecutor;

    private MTThread<PERSON>oolEnum myThreadPool;

    private MTThreadPoolExecutors(ThreadPoolExecutor threadPoolExecutor,MTThreadPoolEnum myThreadPool) {
        this.threadPoolExecutor = threadPoolExecutor;
        this.myThreadPool=myThreadPool;
    }

    public static MTThreadPoolExecutors create(MTThreadPoolEnum myThreadPool) {
        return threadPoolExecutorHashMap.computeIfAbsent(myThreadPool, (threadParams) -> {
            ThreadPoolExecutor threadPoolExecutor = null;
            if (threadParams.isScheduleThreadFlag()) {
                threadPoolExecutor = new ScheduledThreadPoolExecutor(threadParams.getMinNum(), new MTThreadFactory(threadParams.getThreadName(), (Thread t, Throwable e) -> {
                    log.warn("线程执行时异常" + t.getName(), e);
                }
                ), threadParams.getRejectedExecutionHandler());
            } else {
                threadPoolExecutor = new ThreadPoolExecutor(threadParams.getMinNum(), threadParams.getMaxNum(),
                        threadParams.getSurvivalSeconds(),
                        TimeUnit.SECONDS,
                        threadParams.getBlockingQueue(),
                        new MTThreadFactory(threadParams.getThreadName(), (Thread t, Throwable e) -> {
                            log.warn("线程执行时异常" + t.getName(), e);
                        }
                        )
                        , threadParams.getRejectedExecutionHandler());
            }
            MTThreadPoolExecutors MTThreadPoolExecutors = new MTThreadPoolExecutors(threadPoolExecutor, threadParams);
            return MTThreadPoolExecutors;
        });
    }

    public boolean isExceedMaxPoolSize(){
        return threadPoolExecutor.getActiveCount()>=threadPoolExecutor.getMaximumPoolSize();
    }

    public void execute(Runnable runnable) {
        try {
            threadPoolExecutor.execute(runnable);
        } catch (RejectedExecutionException ex) {
            log.error(this.myThreadPool.getThreadName()+ "线程池execute添加新任务时被拒绝", ex);
        } catch (Exception ex) {
            log.error(this.myThreadPool.getThreadName()+"线程池execute添加新任务时异常", ex);
        }
    }

    public void scheduleRun(Runnable command,long delay, TimeUnit unit) {
        try {
            ((ScheduledThreadPoolExecutor)threadPoolExecutor).schedule(command,delay,unit);
        } catch (RejectedExecutionException ex) {
            log.error( this.myThreadPool.getThreadName()+ "线程池execute添加新任务时被拒绝", ex);
        } catch (Exception ex) {
            log.error( this.myThreadPool.getThreadName()+ "线程池execute添加新任务时异常", ex);
        }
    }

    public <T> Future<T> submit(Callable<T> task) {
        try {
            return threadPoolExecutor.submit(task);
        } catch (Exception ex) {
            log.error(this.myThreadPool.getThreadName()+"线程池submit添加新任务时异常", ex);
        }
        return null;
    }


    private static final ConcurrentHashMap<MTThreadPoolEnum, MTThreadPoolExecutors> threadPoolExecutorHashMap = new ConcurrentHashMap();


}
