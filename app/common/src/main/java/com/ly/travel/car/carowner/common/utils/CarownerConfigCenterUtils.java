package com.ly.travel.car.carowner.common.utils;

import com.alibaba.fastjson.JSONObject;
import com.ly.travel.car.carowner.common.configcenter.ConfigCenterUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
public class CarownerConfigCenterUtils {

    static {
        ConfigCenterUtils.addConfigCenterMoniter(CarownerConfigCenterUtils.class);
    }

    @ConfigCenterUtils.ConfigCenterField(configKey = "wxappId", defaultValue = "wx82d2c69bbefab98c", appUk = "groundtravel.shared.mobility.carowner.mobile")
    public static String WXAPP_ID;

    @ConfigCenterUtils.ConfigCenterField(configKey = "wxappsecret", defaultValue = "a69c278b11c6d56c88556c76e5f890a8", appUk = "groundtravel.shared.mobility.carowner.mobile")
    public static String WXAPPSECRET;

    @ConfigCenterUtils.ConfigCenterField(configKey = "wxgzhId", defaultValue = "wx6299f305686999da", appUk = "groundtravel.shared.mobility.carowner.mobile")
    public static String WXGZH_ID;

    @ConfigCenterUtils.ConfigCenterField(configKey = "wxgzhsecret", defaultValue = "9a37699529729d5a1beb4fd24affe9d1", appUk = "groundtravel.shared.mobility.carowner.mobile")
    public static String WXGZHSECRET;

    @ConfigCenterUtils.ConfigCenterField(configKey = "shorturl_domainType", defaultValue = "10", appUk = "groundtravel.dsf.shared.mobility.carowner.core")
    public static String shorturl_domainType;

    @ConfigCenterUtils.ConfigCenterField(configKey = "smsUrl", defaultValue = "http://tccommon.17usoft.com/smstemplateservice/send/", appUk = "groundtravel.dsf.shared.mobility.carowner.core")
    public static String SMS_URL;

    @ConfigCenterUtils.ConfigCenterField(configKey = "cx_push_dsf_version", defaultValue = "latest", appUk = "groundtravel.dsf.shared.mobility.carowner.core")
    public static String cx_push_dsf_version;

    @ConfigCenterUtils.ConfigCenterField(configKey = "update_driver_account_amount_switch", defaultValue = "0")
    public static String UPDATE_DRIVER_ACCOUNT_AMOUNT_SWITCH;

    @ConfigCenterUtils.ConfigCenterField(configKey = "yeePay_remitFailCodes", defaultValue = "UA00006,UA00008,UA00009,UA00010,UA00011,UA00014,UA00016,UA00021,UA30003,UA30004,UA30012,UA30013,UA30044,UA30045,UA30046,UA40001,UA5010,UA5011")
    public static String YEE_PAY_REMIT_FAIL_CODES;

    @ConfigCenterUtils.ConfigCenterField(configKey = "yeePay_appKey", defaultValue = "app_***********")
    public static String yeePayAppId;

    @ConfigCenterUtils.ConfigCenterField(configKey = "yeePay_secret", defaultValue = "MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQg5z6Lu9z/bUMRZz1keaFAYj5OmLuNL7pxXuKatgO0c4igCgYIKoEcz1UBgi2hRANCAASvMQyj81+GWhNEGS78oH3giknBqPm7h7Dk0VduuKBIEAySGSojYqMvUGkXnTAewZgrY4KuqEoRLiA1ghToDDYB")
    public static String yeePaySecret;

    @ConfigCenterUtils.ConfigCenterField(configKey = "yeePay_parentMerchantNo", defaultValue = "***********")
    public static String yeePayParentMerchantNo;

    @ConfigCenterUtils.ConfigCenterField(configKey = "yeePay_merchantNo", defaultValue = "10091416873")
    public static String yeePayMerchantNo;

    @ConfigCenterUtils.ConfigCenterField(configKey = "driver_withdrawal_manual_review_status", defaultValue = "1")
    public static int driverWithdrawalManualReviewStatus;

    @ConfigCenterUtils.ConfigCenterField(configKey = "settlement_freeze_hour", defaultValue = "24")
    public static int settlementFreezeHour;

    @ConfigCenterUtils.ConfigCenterField(configKey = "complete_risk_freezeHour", defaultValue = "24")
    public static int completeRiskFreezeHour;

    @ConfigCenterUtils.ConfigCenterField(configKey = "notice_user_config", defaultValue = "")
    public static String noticeUserConfig;

    /**
     * 小程序跳转链接
     */
    @ConfigCenterUtils.ConfigCenterField(configKey = "wxAppJumpUrl", defaultValue = "https://groundtravel.17u.cn/mdriver/web/transferMini?schemeUrl=",appUk = "groundtravel.shared.mobility.carowner.mobile")
    public static String wxAppJumpUrl;

    @ConfigCenterUtils.ConfigCenterField(configKey = "TestGrabOrder_AutoSuccess", defaultValue = "0")
    public static String TestGrabOrder_AutoSuccess;

    @ConfigCenterUtils.ConfigCenterField(configKey = "car.platform.call.back.url", defaultValue = "",appUk = "groundtravel.shared.mobility.carowner.openapi")
    public static String carPlatformCallBackUrl;

    @ConfigCenterUtils.ConfigCenterField(configKey = "driver_coupon_freeze_close_switch", defaultValue = "true")
    public static String driverCouponFreezeCloseSwitch;

    /**
     * ES模板版本号配置
     */
    public static Map<String, String> esQueryTemplateList;

    @ConfigCenterUtils.ConfigCenterField(configKey = "esQueryTemplateList", defaultValue = "")
    public static void setEsQueryTemplateList(String value) {
        try {
            esQueryTemplateList = JSONObject.parseObject(value, Map.class);
        }catch (Exception ex){
            log.error("获取司机发送im消息快捷语异常,{},{}",value,ex.getMessage(),ex);
        }
    }

    //es 大研发地址
    @ConfigCenterUtils.ConfigCenterField(configKey = "ES_ServerUrl", defaultValue = "http://es.dss.17usoft.com")
    public static String serverUrl;

    //es 大研发地址
    @ConfigCenterUtils.ConfigCenterField(configKey = "ES_Authentication", defaultValue = "ae016a5a-a575-47f5-a68b-e91a41dc0398")
    public static String authentication;

    //es 大研发地址
    @ConfigCenterUtils.ConfigCenterField(configKey = "ES_IndexPrefix", defaultValue = "traffic-rvoperatelog")
    public static String indexPrefix;

    public static final String table_rvoperationlog = "rvoperationlog";

    @ConfigCenterUtils.ConfigCenterField(configKey = "checkRealTelephone", defaultValue = "0")
    public static String checkRealTelephone;

    /**
     * 完单风控mq延迟n秒
     */
    @ConfigCenterUtils.ConfigCenterField(configKey = "orderCompleteDelayLevel", defaultValue = "300")
    public static Integer orderCompleteDelayLevel;
}
