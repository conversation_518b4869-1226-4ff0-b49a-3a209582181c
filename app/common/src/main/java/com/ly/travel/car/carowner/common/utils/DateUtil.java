package com.ly.travel.car.carowner.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

@Slf4j
public class DateUtil {

    /**
     * 服务器时区 上海
     **/
    public static final String TIME_ZONE_SHANGHAI = "Asia/Shanghai";

    /**
     * 服务器时区 北京
     **/
    public static final String TIME_ZONE_BEIJING = "Asia/Beijing";

    public static final String DATE_PATTERN_YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public static final String DATE_PATTERN_YEAR_MONTH_DAY = "yyyy年MM月dd日";
    public static final String DATE_PATTERN_YEAR_MONTH_DAY_NO_FORMAT = "yyyyMMdd";
    public static final String DATE_PATTERN_YYYY_MM_DD = "yyyy-MM-dd";
    public static final String DATE_PATTERN_MM_DD_HH_MM = "MM-dd HH:mm";
    public static final String BEGIN_TIME = " 00:00:00";
    public static final String END_TIME = " 23:59:59";
    public static final Date MIN_DATE = DateUtil.string2Date("1900-01-01 00:00:00");
    public static final String DATE_PATTERN_YYYY_MM_DD_HH_MM = "yyyy-MM-dd HH:mm";
    public static final String DATE_PATTERN_MONTH_DAY_HH_MM = "MM月dd日 HH:mm";

    /**
     * 存放不同的日期模板格式的sdf的Map
     */
    private static Map<String, ThreadLocal<SimpleDateFormat>> sdfMap = new HashMap<>();

    /**
     * 锁对象
     */
    private static final Object lockObj = new Object();

    public static LocalDateTime convertFromDate(Date date) {
        ZoneId zoneId = TimeZone.getTimeZone(DateUtil.TIME_ZONE_SHANGHAI).toZoneId();
        return date.toInstant().atZone(zoneId).toLocalDateTime();
    }

    public static Date convertToDate(LocalDateTime localDateTime) {
        ZoneId zoneId = TimeZone.getTimeZone(DateUtil.TIME_ZONE_SHANGHAI).toZoneId();
        return Date.from(localDateTime.atZone(zoneId).toInstant());
    }

    public static int dayForWeek(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int weekNo = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (weekNo < 0) {
            weekNo = 0;
        }
        return weekNo;
    }

    public static Date string2Date(String dateString) {
        SimpleDateFormat format = new SimpleDateFormat(DATE_PATTERN_YYYY_MM_DD_HH_MM_SS);
        ParsePosition pos = new ParsePosition(0);
        return format.parse(dateString, pos);
    }

    public static Date string2Date(String dateString, String pattern) {
        SimpleDateFormat format = new SimpleDateFormat(pattern);
        ParsePosition pos = new ParsePosition(0);
        return format.parse(dateString, pos);
    }

    public static Date addDay(Date date, int amount) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DAY_OF_MONTH, amount);
        return cal.getTime();
    }

    public static Date addHour(Date date, int amount) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.HOUR_OF_DAY, amount);
        return cal.getTime();
    }


    public static String date2String(Date date, String pattern) {
        SimpleDateFormat format = new SimpleDateFormat(pattern);
        return format.format(date);
    }

    public static String date2String(Date date) {
        SimpleDateFormat format = new SimpleDateFormat(DATE_PATTERN_YYYY_MM_DD_HH_MM_SS);
        return format.format(date);
    }

    public static double getDistanceSecondsOfTwoDate(Date before, Date after) {
        long beforeTime = before.getTime();
        long afterTime = after.getTime();
        return (afterTime - beforeTime) / (1000);
    }


    public static boolean isToday(Date date) {
        String dateStr = date2String(date, "yyyy-MM-dd");
        String nowDateStr = date2String(new Date(), "yyyy-MM-dd");
        return dateStr.equals(nowDateStr);
    }


    public static boolean isTomorrow(Date date) {
        String dateStr = date2String(date, "yyyy-MM-dd");
        String tomorrowDateStr = date2String(DateUtils.addDays(new Date(), 1), "yyyy-MM-dd");
        return dateStr.equals(tomorrowDateStr);
    }

    public static Date getMinDate() {
        return DateUtil.string2Date("1900-01-01 00:00:00");
    }

    /**
     * 计算当前日期与{@code endDate}的间隔天数
     *
     * @param endDate
     * @return 间隔天数
     */
    public static long untilDate(LocalDate endDate) {
        return LocalDate.now().until(endDate, ChronoUnit.DAYS);
    }

    /**
     * 计算日期{@code startDate}与{@code endDate}的间隔天数
     *
     * @param startDate
     * @param endDate
     * @return 间隔天数
     */
    public static long untilDate(LocalDate startDate, LocalDate endDate) {
        return startDate.until(endDate, ChronoUnit.DAYS);
    }

    public static void main(String[] args) {
        LocalDateTime startDateTime = LocalDateTime.of(2019, 7, 26, 15, 8);
        System.out.println(startDateTime.format(DateTimeFormatter.ISO_DATE_TIME));
        System.out.println(untilDateTime(startDateTime, LocalDateTime.now()));
    }

    public static long untilDateTime(LocalDateTime startDateTime, LocalDateTime endDateTime) {
        return startDateTime.until(endDateTime, ChronoUnit.DAYS);
    }

    /**
     * 获取一天的开始时间
     *
     * @param date 2019-09-19 12:10:00
     * @return 2019-09-19 00:00:00
     */
    public static Date beginOfDate(Date date) {
        if (date == null) {
            return date;
        }
        Date begin = null;
        try {
            begin = DateUtil.string2Date(DateUtil.date2String(date, DateUtil.DATE_PATTERN_YYYY_MM_DD) + BEGIN_TIME,
                    DateUtil.DATE_PATTERN_YYYY_MM_DD_HH_MM_SS);
        } catch (Exception e) {
            log.error("beginOfDay error, date:{}", date, e);
            begin = date;
        }
        return begin;
    }

    public static Date getLastTime(BigDecimal timeInDays) {
        LocalDateTime now = LocalDateTime.now();
        int totalMinutesToSubtract = (int) (timeInDays.doubleValue() * 24 * 60);
        LocalDateTime timeBefore = now.minusMinutes(totalMinutesToSubtract);
        return Date.from(timeBefore.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static String getTodayInYYYYMMDD() {
        LocalDate today = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_PATTERN_YEAR_MONTH_DAY_NO_FORMAT);
        return today.format(formatter);
    }

    /**
     * 获取距离当天午夜12点的秒数
     */
    public static int getSecondsUntilMidnight() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime midnight = now.toLocalDate().atStartOfDay().plusDays(1);
        return (int) Duration.between(now, midnight).getSeconds();
    }

    private static SimpleDateFormat getWebFormat() {
        return (SimpleDateFormat) ((Map) THREAD_LOCAL_FORMATTERS.get()).get("yyyy-MM-dd");
    }

    private static SimpleDateFormat getNewFormat() {
        return (SimpleDateFormat) ((Map) THREAD_LOCAL_FORMATTERS.get()).get("yyyy-MM-dd HH:mm:ss");
    }

    private static SimpleDateFormat getNoSecondFormat() {
        return (SimpleDateFormat) ((Map) THREAD_LOCAL_FORMATTERS.get()).get("yyyy-MM-dd HH:mm");
    }

    private static SimpleDateFormat getShortFormat() {
        return (SimpleDateFormat) ((Map) THREAD_LOCAL_FORMATTERS.get()).get("yyyyMMdd");
    }

    private static SimpleDateFormat getLongNoSTimeFormat() {
        return (SimpleDateFormat) ((Map) THREAD_LOCAL_FORMATTERS.get()).get("yyyyMMddHHmm");
    }

    private static SimpleDateFormat getLongFormat() {
        return (SimpleDateFormat) ((Map) THREAD_LOCAL_FORMATTERS.get()).get("yyyyMMddHHmmss");
    }

    private static final ThreadLocal<Map<String, SimpleDateFormat>> THREAD_LOCAL_FORMATTERS = ThreadLocal.withInitial(() -> {
        Map<String, SimpleDateFormat> map = new HashMap(9);
        map.put("yyyyMMdd", new SimpleDateFormat("yyyyMMdd"));
        map.put("yyyy-MM-dd", new SimpleDateFormat("yyyy-MM-dd"));
        map.put("yyyyMMddHHmm", new SimpleDateFormat("yyyyMMddHHmm"));
        map.put("yyyy-MM-dd HH:mm", new SimpleDateFormat("yyyy-MM-dd HH:mm"));
        map.put("yyyyMMddHHmmss", new SimpleDateFormat("yyyyMMddHHmmss"));
        map.put("yyyy-MM-dd HH:mm:ss", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
        map.put("yyyy", new SimpleDateFormat("yyyy"));
        map.put("MM-dd", new SimpleDateFormat("MM-dd"));
        map.put("HH:mm", new SimpleDateFormat("HH:mm"));
        map.put("HH:mm:ss", new SimpleDateFormat("HH:mm:ss"));
        map.put("HH", new SimpleDateFormat("HH"));
        return map;
    });

    public static Date parseAutomatic(String dateString) {
        if (isEmpty(dateString)) {
            return null;
        } else if ("yyyy-MM-dd HH:mm:ss".length() == dateString.length()) {
            return parseY4M2d2H2m2s2WebString(dateString);
        } else if ("yyyy-MM-dd HH:mm".length() == dateString.length()) {
            return parseY4M2d2H2m2WebString(dateString);
        } else if ("yyyy-MM-dd".length() == dateString.length()) {
            return parseY4M2d2WebString(dateString);
        } else if ("yyyyMMdd".length() == dateString.length()) {
            return parseY4M2d2String(dateString);
        } else if ("yyyyMMddHHmm".length() == dateString.length()) {
            return parseY4M2d2H2m2String(dateString);
        } else {
            return "yyyyMMddHHmmss".length() == dateString.length() ? parseY4M2d2H2m2s2String(dateString) : null;
        }
    }

    private static boolean isEmpty(String value) {
        return value == null || value.length() == 0;
    }

    public static String getY4M2d2WebString(Date date) {
        return null == date ? "" : getWebFormat().format(date);
    }

    public static Date parseY4M2d2H2m2s2WebString(String dateString) {
        if (isEmpty(dateString)) {
            return null;
        } else {
            Date date;
            try {
                date = getNewFormat().parse(dateString);
            } catch (ParseException var3) {
                date = null;
            }

            return date;
        }
    }

    public static Date parseY4M2d2H2m2WebString(String dateString) {
        if (isEmpty(dateString)) {
            return null;
        } else {
            Date date;
            try {
                date = getNoSecondFormat().parse(dateString);
            } catch (ParseException var3) {
                date = null;
            }

            return date;
        }
    }

    public static Date parseY4M2d2WebString(String dateString) {
        if (isEmpty(dateString)) {
            return null;
        } else {
            Date date;
            try {
                date = getWebFormat().parse(dateString);
            } catch (ParseException var3) {
                date = null;
            }

            return date;
        }
    }

    public static Date parseY4M2d2String(String dateString) {
        if (isEmpty(dateString)) {
            return null;
        } else {
            Date date;
            try {
                date = getShortFormat().parse(dateString);
            } catch (ParseException var3) {
                date = null;
            }

            return date;
        }
    }

    public static Date parseY4M2d2H2m2String(String dateString) {
        if (isEmpty(dateString)) {
            return null;
        } else {
            Date date;
            try {
                date = getLongNoSTimeFormat().parse(dateString);
            } catch (ParseException var3) {
                date = null;
            }

            return date;
        }
    }

    public static Date parseY4M2d2H2m2s2String(String dateString) {
        if (isEmpty(dateString)) {
            return null;
        } else {
            Date date;
            try {
                date = getLongFormat().parse(dateString);
            } catch (ParseException var3) {
                date = null;
            }

            return date;
        }
    }

    /**
     * 是用ThreadLocal<SimpleDateFormat>来获取SimpleDateFormat,这样每个线程只会有一个SimpleDateFormat
     *
     * @param date
     * @param pattern
     * @return
     */
    public static String format(Date date, String pattern) {
        return getSdf(pattern).format(date);
    }

    /**
     * 返回一个ThreadLocal的sdf,每个线程只会new一次sdf
     *
     * @param pattern
     * @return
     */
    private static SimpleDateFormat getSdf(final String pattern) {
        ThreadLocal<SimpleDateFormat> tl = sdfMap.get(pattern);
        // 此处的双重判断和同步是为了防止sdfMap这个单例被多次put重复的sdf
        if (tl == null) {
            synchronized (lockObj) {
                tl = sdfMap.get(pattern);
                if (tl == null) {
                    // 这里是关键,使用ThreadLocal<SimpleDateFormat>替代原来直接new SimpleDateFormat
                    tl = new ThreadLocal<SimpleDateFormat>() {
                        @Override
                        protected SimpleDateFormat initialValue() {
                            return new SimpleDateFormat(pattern);
                        }
                    };
                    sdfMap.put(pattern, tl);
                }
            }
        }
        return tl.get();
    }
}
