package com.ly.travel.car.carowner.common.utils;

import com.ly.travel.car.carowner.common.enums.EnvEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Arrays;
@Slf4j
@Component
public class EnvUtil {

    private static EnvEnum ENV;


    @Value("${sof-env}")
    public void setEnv(String env) throws Exception {
        EnvEnum envEnum=Arrays.stream(EnvEnum.values()).filter(item->item.getValue().equals(env)).findFirst().orElse(null);
        if(envEnum==null) {
            log.error("项目无法启用，没有环境变量");
            throw  new Exception("项目无法启用，没有环境变量"+env);
        }
        ENV=envEnum;
    }

    public static EnvEnum getEnv(){
        return ENV;
    }

}
