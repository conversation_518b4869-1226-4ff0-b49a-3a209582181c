package com.ly.travel.car.carowner.common.utils;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.ly.sof.utils.mapping.FastJsonUtils;
import lombok.*;
import okhttp3.*;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.ws.rs.NotSupportedException;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version HttpUtils, 2025/6/7 16:53
 */
public class HttpUtils {

    private static Logger logger                      = LoggerFactory.getLogger(HttpUtils.class);

    public final static String                                   GET                         = "GET";
    public final static String                                   POST                        = "POST";
    public final static String                                   PUT                         = "PUT";
    public final static String                                   DELETE                      = "DELETE";
    public final static String                                   PATCH                       = "PATCH";

    private final static String                                  UTF8                        = "UTF-8";
    private final static String                                  GBK                         = "GBK";

    private final static String                                  DEFAULT_CHARSET             = UTF8;
    private final static String                                  DEFAULT_METHOD              = GET;
    private final static String                                  DEFAULT_MEDIA_TYPE          = javax.ws.rs.core.MediaType.APPLICATION_JSON;
    private final static boolean                                 DEFAULT_LOG                 = true;

    /** 单位毫秒 */
    private final static int                                     DEFAULT_CONNECT_TIMEOUT     = 3000;
    /** 单位毫秒 */
    private final static int                                     DEFAULT_READ_TIMEOUT        = 5000;
    /** 单位毫秒 */
    private final static int                                     DEFAULT_WRITE_TIMEOUT       = 5000;

    private final static ThreadLocal<Map<String, TimeoutConfig>> METHOD_TIMEOUT_CONFIG_CACHE = new ThreadLocal<>();

    private final static OkHttpClient CLIENT                      = new OkHttpClient.Builder()                  //
            .addInterceptor(new DynamicInterceptor())                                                                                          //
            .connectionPool(new ConnectionPool(500, 50, TimeUnit.MINUTES))                                                                     //
            .readTimeout(DEFAULT_READ_TIMEOUT, TimeUnit.MILLISECONDS)                                                                               //
            .connectTimeout(DEFAULT_CONNECT_TIMEOUT, TimeUnit.MILLISECONDS)                                                                         //
            .writeTimeout(DEFAULT_WRITE_TIMEOUT, TimeUnit.MILLISECONDS)                                                                             //
            .build();

    //===========================================get start========================================================

    /**
     * GET请求
     *
     * @param url URL地址
     * @return string
     */
    public static String get(String url) throws IOException {
        return execute(OkHttp.builder().url(url).build());
    }

    /**
     * GET请求
     *
     * @param url     URL地址
     * @param charset the charset
     * @return string
     */
    public static String get(String url, String charset) throws IOException {
        return execute(OkHttp.builder().url(url).responseCharset(charset).build());
    }

    /**
     * 带查询参数的GET查询
     *
     * @param url       URL地址
     * @param charset   the charset
     * @param headerMap the header map
     * @return string string
     * @throws IOException the io exception
     */
    public static String get(String url, String charset, Map<String, String> headerMap) throws IOException {
        return execute(OkHttp.builder().url(url).responseCharset(charset).headerMap(headerMap).build());
    }

    /**
     * 带查询参数的GET查询
     *
     * @param url       URL地址
     * @param queryMap  查询参数
     * @param headerMap the header map
     * @return string string
     * @throws IOException the io exception
     */
    public static String get(String url, Map<String, String> queryMap, Map<String, String> headerMap) throws IOException {
        return execute(OkHttp.builder().url(url).queryMap(queryMap).headerMap(headerMap).build());
    }

    /**
     * Get string.
     *
     * @param url      the url
     * @param queryMap the query map
     * @param timeout  the timeout
     * @return the string
     */
    public static String get(String url, Map<String, String> queryMap, int timeout) throws IOException {
        setMethodTimeOutConfig(url, timeout, TimeUnit.SECONDS);
        return execute(OkHttp.builder().url(url).queryMap(queryMap).build());
    }

    /**
     * Get string.
     *
     * @param url      the url
     * @param queryMap the query map
     * @param timeout  the timeout
     * @param timeUnit the time unit
     * @return the string
     */
    public static String get(String url, Map<String, String> queryMap, int timeout, TimeUnit timeUnit) throws IOException {
        if (timeout <= 0 || timeUnit == null) {
            return get(url, DEFAULT_CHARSET, queryMap);
        }
        setMethodTimeOutConfig(url, timeout, timeUnit);
        return execute(OkHttp.builder().url(url).queryMap(queryMap).build());
    }

    /**
     * 带header和timeout的Get请求
     * @param url
     * @param queryMap
     * @param headerMap
     * @param timeout
     * @param timeUnit
     * @return
     * @throws IOException
     */
    public static String get(String url,
                             Map<String, String> queryMap,
                             Map<String, String> headerMap,
                             int timeout,
                             TimeUnit timeUnit) throws IOException {
        if (timeout <= 0 || timeUnit == null) {
            return get(url, DEFAULT_CHARSET, queryMap);
        }
        setMethodTimeOutConfig(url, timeout, timeUnit);
        return execute(OkHttp.builder().url(url).queryMap(queryMap).headerMap(headerMap).build());
    }

    /**
     * 带查询参数的GET查询
     *
     * @param url      URL地址
     * @param queryMap 查询参数
     * @param charset  the charset
     * @return string
     */
    public static String get(String url, Map<String, String> queryMap, String charset) throws IOException {
        return execute(OkHttp.builder().url(url).queryMap(queryMap).responseCharset(charset).build());
    }

    //==================================================get end=================================================

    /**
     * POST
     * application/json
     *
     * @param url the url
     * @param obj the obj
     * @return string
     */
    public static String postJson(String url, Object obj) throws IOException {
        return execute(OkHttp.builder().url(url).method(POST).data(JSON.toJSONString(obj)).mediaType(DEFAULT_MEDIA_TYPE).build());
    }

    /**
     * POST
     * application/json
     *
     * @param url     the url
     * @param obj     the obj
     * @param timeout the timeout
     * @return string
     */
    public static String postJson(String url, Object obj, int timeout) throws IOException {
        setMethodTimeOutConfig(url, timeout, TimeUnit.SECONDS);
        return execute(OkHttp.builder().url(url).method(POST).data(JSON.toJSONString(obj)).mediaType(DEFAULT_MEDIA_TYPE).build());
    }

    /**
     * Post json string.
     *
     * @param url      the url
     * @param obj      the obj
     * @param timeout  the timeout
     * @param timeUnit the time unit
     * @return the string
     */
    public static String postJson(String url, Object obj, int timeout, TimeUnit timeUnit) throws IOException {
        setMethodTimeOutConfig(url, timeout, timeUnit);
        String data;
        if (obj instanceof String) {
            data = (String) obj;
        } else {
            data = JSON.toJSONString(obj);
        }
        return execute(OkHttp.builder().url(url).method(POST).data(data).mediaType(DEFAULT_MEDIA_TYPE).build());
    }

    /**
     * Post json string.
     *
     * @param url      the url
     * @param obj      the obj
     * @param timeout  the timeout
     * @param timeUnit the time unit
     * @return the string
     */
    public static String postJson(String url, Object obj, Map<String, String> headerMap, int timeout, TimeUnit timeUnit) throws IOException {
        setMethodTimeOutConfig(url, timeout, timeUnit);
        return execute(OkHttp.builder().url(url).method(POST).data((obj instanceof  String)?(String)obj: FastJsonUtils.toJSONString(obj)).mediaType(DEFAULT_MEDIA_TYPE).headerMap(headerMap).build());
    }

    /**
     * POST
     * application/x-www-form-urlencoded
     *
     * @param url     the url
     * @param formMap the form map
     * @return string
     */
    public static String postForm(String url, Map<String, String> formMap) throws IOException {
        String data = "";
        if (MapUtils.isNotEmpty(formMap)) {
            data = formMap.entrySet().stream().map(entry -> String.format("%s=%s", entry.getKey(), entry.getValue())).collect(Collectors.joining("&"));
        }
        return execute(OkHttp.builder().url(url).method(POST).data(data).mediaType(javax.ws.rs.core.MediaType.APPLICATION_FORM_URLENCODED).build());
    }

    /**
     * Post string.
     *
     * @param url       the url
     * @param data      the data
     * @param mediaType the media type
     * @param charset   the charset
     * @return the string
     */
    private static String post(String url, String data, String mediaType, String charset) throws IOException {
        return execute(OkHttp.builder().url(url).method(POST).data(data).mediaType(mediaType).responseCharset(charset).build());
    }


    public static String postJson(String url, String body, Map<String, String> headerMap, Map<String, String> paramMap,int timeout, TimeUnit timeUnit) throws IOException {
        setMethodTimeOutConfig(url, timeout, timeUnit);
        return execute(OkHttp.builder().url(url).method(POST).data(body).mediaType(DEFAULT_MEDIA_TYPE).queryMap(paramMap).headerMap(headerMap).build());
    }

    /**
     * 通用执行方法
     */
    private static String execute(OkHttp okHttp) throws IOException {
        if (StringUtils.isBlank(okHttp.requestCharset)) {
            okHttp.requestCharset = DEFAULT_CHARSET;
        }
        if (StringUtils.isBlank(okHttp.responseCharset)) {
            okHttp.responseCharset = DEFAULT_CHARSET;
        }
        if (StringUtils.isBlank(okHttp.method)) {
            okHttp.method = DEFAULT_METHOD;
        }
        if (StringUtils.isBlank(okHttp.mediaType)) {
            okHttp.mediaType = DEFAULT_MEDIA_TYPE;
        }
        if (okHttp.requestLog) {
            //记录请求日志
            logger.info(okHttp.toString());
        }

        String url = okHttp.url;

        Request.Builder builder = new Request.Builder();

        if (MapUtils.isNotEmpty(okHttp.queryMap)) {
            String queryParams = okHttp.queryMap.entrySet().stream().map(entry -> String.format("%s=%s", entry.getKey(), entry.getValue())).collect(Collectors.joining("&"));
            url = String.format("%s%s%s", url, url.contains("?") ? "&" : "?", queryParams);
        }
        builder.url(url);

        if (MapUtils.isNotEmpty(okHttp.headerMap)) {
            okHttp.headerMap.forEach(builder::addHeader);
        }

        String method = okHttp.method.toUpperCase();
        String mediaType = String.format("%s;charset=%s", okHttp.mediaType, okHttp.requestCharset);

        if (StringUtils.equals(method, GET)) {
            builder.get();
        } else if (ArrayUtils.contains(new String[] { POST, PUT, DELETE, PATCH }, method)) {
            RequestBody requestBody = RequestBody.create(MediaType.parse(mediaType), okHttp.data);
            builder.method(method, requestBody);
        } else {
            throw new NotSupportedException(String.format("http method:%s not support!", method));
        }

        Response response = CLIENT.newCall(builder.build()).execute();
        assert response.body() != null;
        byte[] bytes = response.body().bytes();
        String result = new String(bytes, okHttp.responseCharset);
        if (okHttp.responseLog) {
            //记录返回日志
            logger.info(String.format("Got detail->%s ,response->%s", okHttp, result));
        }
        return result;
    }

    private static void setMethodTimeOutConfig(String url, int timeout, TimeUnit timeUnit) {
        if (StringUtils.isBlank(url) || timeout <= 0) {
            return;
        }

        Map<String, TimeoutConfig> config = METHOD_TIMEOUT_CONFIG_CACHE.get();
        if (config == null) {
            METHOD_TIMEOUT_CONFIG_CACHE.set(Maps.newConcurrentMap());
        }
        METHOD_TIMEOUT_CONFIG_CACHE.get().putIfAbsent(url, new TimeoutConfig(timeout, timeUnit));
    }

    @Builder
    @ToString(exclude = { "requestCharset", "responseCharset", "requestLog", "responseLog" })
    static class OkHttp {
        private String              url;
        @Builder.Default
        private String              method          = DEFAULT_METHOD;
        private String              data;
        @Builder.Default
        private String              mediaType       = DEFAULT_MEDIA_TYPE;
        private Map<String, String> queryMap;
        private Map<String, String> headerMap;
        @Builder.Default
        private String              requestCharset  = DEFAULT_CHARSET;
        @Builder.Default
        private boolean             requestLog      = DEFAULT_LOG;
        @Builder.Default
        private String              responseCharset = DEFAULT_CHARSET;
        @Builder.Default
        private boolean             responseLog     = DEFAULT_LOG;
    }

    /**
     * 动态超时时间设置
     */
    static class DynamicInterceptor implements Interceptor {

        @Override
        public Response intercept(Chain chain) throws IOException {
            Request request = chain.request();
            String url = request.url().toString();
            Map<String, TimeoutConfig> config = METHOD_TIMEOUT_CONFIG_CACHE.get();
            if (config != null && config.containsKey(url)) {
                TimeoutConfig timeoutConfig = config.get(url);
                return chain.withConnectTimeout(timeoutConfig.getConnectTimeout(), timeoutConfig.getTimeUnit())//
                        .withReadTimeout(timeoutConfig.getReadTimeout(), timeoutConfig.getTimeUnit())//
                        .withWriteTimeout(timeoutConfig.getWriteTimeout(), timeoutConfig.getTimeUnit())//
                        .proceed(request);
            }
            return chain.proceed(request);
        }
    }

    /**
     * 超时时间配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    static class TimeoutConfig {
        private int      connectTimeout;
        private int      readTimeout;
        private int      writeTimeout;
        private TimeUnit timeUnit = TimeUnit.MILLISECONDS;

        public TimeoutConfig(int readTimeout, TimeUnit timeUnit) {
            this.connectTimeout = DEFAULT_CONNECT_TIMEOUT;
            this.readTimeout = readTimeout;
            this.writeTimeout = DEFAULT_WRITE_TIMEOUT;
            this.timeUnit = timeUnit;
        }
    }
}
