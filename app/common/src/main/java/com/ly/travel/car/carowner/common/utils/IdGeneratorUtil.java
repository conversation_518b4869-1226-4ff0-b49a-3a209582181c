/**
 * Copyright &copy; 2012-2014 <a href="https://github.com/thinkgem/jeesite">JeeSite</a> All rights reserved.
 */
package com.ly.travel.car.carowner.common.utils;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.security.SecureRandom;

@Slf4j
public class IdGeneratorUtil {

    private static SecureRandom random = new SecureRandom();

    public static String create(String prefix, String dprefix) {
        String id;
        String GET_URL = "http://tccomponent.17usoft.com/idgenerator/generator";
        try {
            // 拼凑get请求的URL字串
            String getURL = GET_URL + "?prefix=" + URLEncoder.encode(prefix, "utf-8") + "&dprefix="
                    + URLEncoder.encode(dprefix, "utf-8");
            URL getUrl = new URL(getURL);
            // 根据拼凑的URL，打开连接
            HttpURLConnection connection = (HttpURLConnection) getUrl.openConnection();
            // 进行连接
            connection.connect();
            // 取得输入流，并使用Reader读取
            BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String lines;
            while ((lines = reader.readLine()) != null) {
                reader.close();
                // 断开连接
                connection.disconnect();
                // 把json字符串转换成json对象
                JSONObject jsonObject = null;
                jsonObject = JSONObject.parseObject(lines);
                id = jsonObject.getString("id");
                return id;
            }
        } catch (Exception e) {
            log.error("调用公共订单号接口异常>>> {}", e);
        }
        id = randomNum((prefix + dprefix).toUpperCase());
        log.info("本地生成ID：{}", id);
        return id;
    }

    public static String randomNum(String prefix) {
        return prefix + System.currentTimeMillis() + "" + String.valueOf(randomLong()).substring(0, 5);
    }

    public static long randomLong() {
        return Math.abs(random.nextLong());
    }
}
