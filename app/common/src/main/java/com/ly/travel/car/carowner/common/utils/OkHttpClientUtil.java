package com.ly.travel.car.carowner.common.utils;

import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.TimeUnit;

public class OkHttpClientUtil {
    static final Logger logger = LoggerFactory.getLogger(OkHttpClientUtil.class);

    public static OkHttpClientUtil instance = null;
    private OkHttpClient okHttpClient;

    OkHttpClientUtil() {
        okHttpClient = new OkHttpClient.Builder()
                .retryOnConnectionFailure(true)
                .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(10, TimeUnit.SECONDS)
                .writeTimeout(10, TimeUnit.SECONDS)
                .connectionPool(new ConnectionPool())
                .build();
    }

    public static OkHttpClientUtil getInstance() {
        if (instance == null) {
            synchronized (OkHttpClientUtil.class) {
                if (instance == null) {
                    instance = new OkHttpClientUtil();
                }
            }
        }
        return instance;
    }

    public String post(String url, String jsonData, Map<String, String> headerMap) {
        Response response;
        try {
            Request.Builder builder = new Request.Builder();
            if (headerMap != null && headerMap.size() > 0) {
                for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                    builder.header(entry.getKey(), entry.getValue());
                }
            }

            RequestBody requestBody = RequestBody.create(MediaType.parse("application/json;charset=UTF-8"),jsonData);
            Request request = builder
                    .url(url)
                    .post(requestBody)
                    .build();
            response = okHttpClient.newCall(request).execute();
            String result = null;
            try {
                result = response.body().string();
            } catch (Exception e) {
                result = "";
            }
            return result;
        } catch (Exception e) {
            logger.error("请求失败" + url + " body" + jsonData, e);
        }
        return null;
    }

    public String post(String url, String jsonData, Map<String, String> headerMap, Long timeOut, TimeUnit timeUnit) {
        Response response;
        try {
            Request.Builder builder = new Request.Builder();
            if (headerMap != null && headerMap.size() > 0) {
                for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                    builder.header(entry.getKey(), entry.getValue());
                }
            }

            RequestBody requestBody = RequestBody.create(MediaType.parse("application/json;charset=UTF-8"), jsonData);
            Request request = builder
                    .url(url)
                    .post(requestBody)
                    .build();
            response = okHttpClient
                    .newBuilder()
                    .connectTimeout(timeOut, timeUnit)
                    .readTimeout(timeOut, timeUnit)
                    .writeTimeout(timeOut, timeUnit)
                    .build()
                    .newCall(request)
                    .execute();
            String result = null;
            try {
                result = response.body().string();
            } catch (Exception e) {
                result = "";
            }
            return result;
        } catch (Exception e) {
            logger.error("post请求失败url=" + url + ",jsonData=" + jsonData, e);
            throw new RuntimeException("请求失败");
        } finally {
            response = null;
        }
    }

}
