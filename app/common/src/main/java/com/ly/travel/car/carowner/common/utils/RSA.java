package com.ly.travel.car.carowner.common.utils;

import javax.crypto.Cipher;
import java.io.ByteArrayOutputStream;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;

public class RSA {
    public static final String CHARSET = "utf-8";
    public static final String KEY_ALGORITHM = "RSA";
    public static final String SIGNATURE_ALGORITHM = "MD5withRSA";
    private static final String PUBLIC_KEY = "LocatorPublicKey";
    private static final String PRIVATE_KEY = "LocatorPrivateKey";
    private static final int MAX_ENCRYPT_BLOCK = 117;
    private static final int MAX_DECRYPT_BLOCK = 128;

    public RSA() {
    }

    public static Map<String, Object> genKeyPair() throws Exception {
        KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance("RSA");
        keyPairGen.initialize(512);
        KeyPair keyPair = keyPairGen.generateKeyPair();
        RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();
        RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();
        HashMap keyMap = new HashMap(2);
        keyMap.put("LocatorPublicKey", publicKey);
        keyMap.put("LocatorPrivateKey", privateKey);
        return keyMap;
    }

    public static String sign(byte[] data, String privateKey) throws Exception {
        byte[] keyBytes = Base64.decode(privateKey);
        PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey privateK = keyFactory.generatePrivate(pkcs8KeySpec);
        Signature signature = Signature.getInstance("MD5withRSA");
        signature.initSign(privateK);
        signature.update(data);
        return Base64.encode(signature.sign());
    }

    public static boolean verify(byte[] data, String publicKey, String sign) throws Exception {
        byte[] keyBytes = Base64.decode(publicKey);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PublicKey publicK = keyFactory.generatePublic(keySpec);
        Signature signature = Signature.getInstance("MD5withRSA");
        signature.initVerify(publicK);
        signature.update(data);
        return signature.verify(Base64.decode(sign));
    }

    public static byte[] decryptByPrivateKey(byte[] encryptedData, String privateKey) throws Exception {
        byte[] keyBytes = Base64.decode(privateKey);
        PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey privateK = keyFactory.generatePrivate(pkcs8KeySpec);
        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(2, privateK);
        int inputLen = encryptedData.length;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int offSet = 0;

        for (int i = 0; inputLen - offSet > 0; offSet = i * 128) {
            byte[] cache;
            if (inputLen - offSet > 128) {
                cache = cipher.doFinal(encryptedData, offSet, 128);
            } else {
                cache = cipher.doFinal(encryptedData, offSet, inputLen - offSet);
            }

            out.write(cache, 0, cache.length);
            ++i;
        }

        byte[] decryptedData = out.toByteArray();
        out.close();
        return decryptedData;
    }

    public static String decryptByPrivateKey(String encryptedStr, String privateKey) throws Exception {
        byte[] encryptedData = Base64.decode(encryptedStr);
        byte[] keyBytes = Base64.decode(privateKey);
        PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey privateK = keyFactory.generatePrivate(pkcs8KeySpec);
        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(2, privateK);
        int inputLen = encryptedData.length;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int offSet = 0;

        for (int i = 0; inputLen - offSet > 0; offSet = i * 128) {
            byte[] cache;
            if (inputLen - offSet > 128) {
                cache = cipher.doFinal(encryptedData, offSet, 128);
            } else {
                cache = cipher.doFinal(encryptedData, offSet, inputLen - offSet);
            }

            out.write(cache, 0, cache.length);
            ++i;
        }

        byte[] decryptedData = out.toByteArray();
        out.close();
        return new String(decryptedData, "utf-8");
    }

    public static byte[] decryptByPublicKey(byte[] encryptedData, String publicKey) throws Exception {
        byte[] keyBytes = Base64.decode(publicKey);
        X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PublicKey publicK = keyFactory.generatePublic(x509KeySpec);
        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(2, publicK);
        int inputLen = encryptedData.length;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int offSet = 0;

        for (int i = 0; inputLen - offSet > 0; offSet = i * 128) {
            byte[] cache;
            if (inputLen - offSet > 128) {
                cache = cipher.doFinal(encryptedData, offSet, 128);
            } else {
                cache = cipher.doFinal(encryptedData, offSet, inputLen - offSet);
            }

            out.write(cache, 0, cache.length);
            ++i;
        }

        byte[] decryptedData = out.toByteArray();
        out.close();
        return decryptedData;
    }

    public static byte[] encryptByPublicKey(byte[] data, String publicKey) throws Exception {
        byte[] keyBytes = Base64.decode(publicKey);
        X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PublicKey publicK = keyFactory.generatePublic(x509KeySpec);
        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(1, publicK);
        int inputLen = data.length;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int offSet = 0;

        for (int i = 0; inputLen - offSet > 0; offSet = i * 117) {
            byte[] cache;
            if (inputLen - offSet > 117) {
                cache = cipher.doFinal(data, offSet, 117);
            } else {
                cache = cipher.doFinal(data, offSet, inputLen - offSet);
            }

            out.write(cache, 0, cache.length);
            ++i;
        }

        byte[] encryptedData = out.toByteArray();
        out.close();
        return encryptedData;
    }

    public static String encryptByPublicKey(String source, String publicKey) throws Exception {
        byte[] data = source.getBytes("utf-8");
        byte[] keyBytes = Base64.decode(publicKey);
        X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PublicKey publicK = keyFactory.generatePublic(x509KeySpec);
        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(1, publicK);
        int inputLen = data.length;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int offSet = 0;

        for (int i = 0; inputLen - offSet > 0; offSet = i * 117) {
            byte[] cache;
            if (inputLen - offSet > 117) {
                cache = cipher.doFinal(data, offSet, 117);
            } else {
                cache = cipher.doFinal(data, offSet, inputLen - offSet);
            }

            out.write(cache, 0, cache.length);
            ++i;
        }

        byte[] encryptedData = out.toByteArray();
        out.close();
        return Base64.encode(encryptedData);
    }

    public static byte[] encryptByPrivateKey(byte[] data, String privateKey) throws Exception {
        byte[] keyBytes = Base64.decode(privateKey);
        PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey privateK = keyFactory.generatePrivate(pkcs8KeySpec);
        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(1, privateK);
        int inputLen = data.length;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int offSet = 0;

        for (int i = 0; inputLen - offSet > 0; offSet = i * 117) {
            byte[] cache;
            if (inputLen - offSet > 117) {
                cache = cipher.doFinal(data, offSet, 117);
            } else {
                cache = cipher.doFinal(data, offSet, inputLen - offSet);
            }

            out.write(cache, 0, cache.length);
            ++i;
        }

        byte[] encryptedData = out.toByteArray();
        out.close();
        return encryptedData;
    }

    public static String getPrivateKey(Map<String, Object> keyMap) throws Exception {
        Key key = (Key) keyMap.get("LocatorPrivateKey");
        String encode = Base64.encode(key.getEncoded());
        return encode;
    }

    public static String getPublicKey(Map<String, Object> keyMap) throws Exception {
        Key key = (Key) keyMap.get("LocatorPublicKey");
        return Base64.encode(key.getEncoded());
    }

    public static void main(String[] args) {
        try {
            Map<String, Object> keyPair = RSA.genKeyPair();
            System.out.println("公钥:[" + getPublicKey(keyPair) + "]");
            System.out.println("私钥:[" + getPrivateKey(keyPair) + "]");

            String publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCDBG7+5D"
                    + "+pVkMbifEQ6K1Q5jkXb24VZDJsh6VOTkiUxO3f3iRPokPam0hmdb2DvtlstPrZ295"
                    + "/DRifFxFo2mCX3lnE7jRP3yGe9cJvEFumIK9UZ9Da3rr9DorWQs2CmhXfmoPbreuwjeMnDmR64Jv8kiXVGMnzubeXGeqjzqPS7wIDAQAB";
            String privateKey =
                    "MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAIMEbv7kP6lWQxuJ8RDorVDmORdvbhVkMmyHpU5OSJTE7d"
                            + "/eJE+iQ9qbSGZ1vYO+2Wy0+tnb3n8NGJ8XEWjaYJfeWcTuNE/fIZ71wm8QW6Ygr1Rn0Nreuv0OitZCzYKaFd"
                            + "+ag9ut67CN4ycOZHrgm"
                            + "/ySJdUYyfO5t5cZ6qPOo9LvAgMBAAECgYAG1cVgoNG9aNWpayHbavgXv1r9IQASN5710IHMOwNIV5m9Ux571RBLKEy3o67WX6+CH1Ly6KiL4kgFjLOIz9O4JWzjw5QIf3+Uv2e0gOkDCX67D94f6X6Jrjw2HfEIC1copLyXM2qSEdUfv4kXyIRorTScYB1etw5zYgFrRHEmKQJBAMA0ILVNILeUPH7Gok0UemantvK0WeuAFIGyqxRLEAzUpn9+ZkYyb6rC2ghRIoSofA5mpZfeJzHcgQR0Ar9wEesCQQCugTNGUi0Ep39xVYH9pHhJW/VRFQhamYnGSDsxkX+gjrh6GI587jqkcNLaIqu5mzWEn7G7pIewIGNlcgqR1D4NAkEAov2Rny3AyO0UQA4CJItxIQyowDjg6Cy9Nx9kpNjiy36pkwSFhpHO0GhcrSDgvM6EJVI2qwrhAg+T+UrH6cAH8QJBAILUzQuI6sWVit/+utHesGxXrInY2gWLHZdyaibUrjpDJp4rsRCLT54gFIWE/vjOHAbN7+P+C9kJv1u8b5474TUCQQCFt4hy/3VLrH8pvWdjVeRRhYyaCIKISxtTxcsCCKqzrldCE/5ZjsK0ZLpe6ICOTT4XtNid4BKeu2w9nD4ofR2r";

            String dataStr = "77b3e6926e7295494dd3be91c6934899";

            byte[] bytes = RSA.encryptByPublicKey(dataStr.getBytes("utf-8"), publicKey);
            System.out.println("加密后的数据：" + Base64.encode(bytes));

            byte[] bytes1 = RSA.decryptByPrivateKey(bytes, privateKey);
            String aaa = new String(bytes1);
            System.out.println("解密后的数据：" + aaa);

            // 公钥加密的数据：
            String miwen = "Iz0PaRnq11cYCT7fF2MW66SsAQGPbKDeU2tL+Sai04DbTBBVhbuRM"
                    + "//9mTn7NSROaFAGY1qnIb9CqvfHjtFVNm2PzEJr82TvVuhgsGqQmhlhgmJksn9IGUj4JGoMWqUbga6qNo7sSPb"
                    + "+KhQb7FmLcny30w0qT9fBHCG3WBeaa+g=";
            byte[] decode = Base64.decode(miwen);
            // 私钥解密：
            byte[] bytes2 = RSA.decryptByPrivateKey(decode, privateKey);
            aaa = new String(bytes2);
            System.out.println("解密后的数据：" + aaa);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
