package com.ly.travel.car.carowner.common.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

/**
 * 短链接转换工具类
 *
 * <AUTHOR>
 * @doc http://wiki.17usoft.com/display/YFZX/User+Guide
 * @since 2019/9/5 10:46
 */
@Slf4j
@Component
public class ShortUrlUtil {
    /**
     * 生成接口地址
     */

    private static String CREATE_URL;

    /**
     * 解析接口地址
     */

    private static String SEACH_URL;

    @Value("${short.create.url}")
    public void setCreateUrl(String createUrl) {
        CREATE_URL = createUrl;
    }

    @Value("${short.search.url}")
    public void setSearchUrl(String searchUrl) {
        SEACH_URL = searchUrl;
    }

    /**
     * 短链接过期时间 单位秒
     */
    private final static Long TTL = 2592000L;

    /**
     * 创建短链接
     *
     * @param longUrls
     *            必须ly.com，17u.cn，elong.com，tcent.cn，17usoft.com，17u.com，40017.cn，chebada.com
     * @return
     * @throws UnsupportedEncodingException
     */
    public static String create(String longUrls) {
        if (StringUtils.isEmpty(longUrls)) {
            return "";
        }
        if (!longUrls.contains("://")) {
            return "";
        }
        String shortUrl = null;
        String resp = null;
        String jsonString = null;
        try {
            jsonString = "?ttl=" + TTL + "&domainType="+CarownerConfigCenterUtils.shorturl_domainType+"&longUrls=" + URLEncoder.encode(longUrls, "utf-8");
            resp = HttpClientUtil.getInstance().post(CREATE_URL, jsonString,null);
            JSONObject jsonObject = JSON.parseObject(resp);
            JSONArray datas = jsonObject.getJSONArray("datas");
            shortUrl = datas.getJSONObject(0).getString("shortUrl");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            resp = e.getMessage();
        } finally {
            log.info("创建短链接服务,入参={},jsonString={},出参={}", longUrls, jsonString, resp);
        }
        return shortUrl;
    }

    /**
     * 解析短链接
     *
     * @param shortUrl
     * @return
     */
    public static String seach(String shortUrl) {
        if (StringUtils.isEmpty(shortUrl)) {
            return "";
        }
        if (!shortUrl.contains("://")) {
            return "";
        }
        String resp = null;
        String jsonString = null;
        String longUrl = null;
        try {
            jsonString = "shortUrl=" + URLEncoder.encode(shortUrl, "utf-8");
            resp = HttpClientUtil.getInstance().post(SEACH_URL, jsonString,null);

            longUrl = JSON.parseObject(resp).getString("longUrl");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            resp = e.getMessage();
        } finally {
            log.info("解析短链接服务,入参={},jsonString={},出参={}", shortUrl, jsonString, resp);
        }
        return longUrl;
    }

    public static void main(String[] args) {
        String url = "http://www.ly.com?a=a&b=b";
        String s = create(url);
        String seach = seach(s);
    }
}
