package com.ly.travel.car.carowner.dal;


import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Configuration;

@Configuration
@MapperScan(basePackages = "com.ly.travel.car.carowner.dal.mapper", sqlSessionFactoryRef = "sqlSessionFactory")
public class MpConfig {
//    @Bean
//    public GlobalConfig globalConfiguration() {
//        GlobalConfig conf = new GlobalConfig();
//        conf.setDbConfig(new GlobalConfig.DbConfig());
//        return conf;
//    }

//    @Bean
//    public MybatisSqlSessionFactoryBean sqlSessionFactory(DataSource dataSource){
//        MybatisSqlSessionFactoryBean sqlSessionFactory=new MybatisSqlSessionFactoryBean();
//        sqlSessionFactory.setDataSource(dataSource);
//
//        return sqlSessionFactory;
//    }
}
