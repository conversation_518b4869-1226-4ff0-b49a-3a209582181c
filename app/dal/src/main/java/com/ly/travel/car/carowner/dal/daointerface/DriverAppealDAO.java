/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */package com.ly.travel.car.carowner.dal.daointerface;
import org.springframework.dao.DataAccessException;
import com.ly.travel.car.carowner.dal.operation.driverappeal.*;
import com.ly.travel.car.carowner.dal.dataobject.*;


import java.io.*;
import java.net.*;
import java.util.*;

import java.math.BigInteger;

import com.ly.sof.utils.page.PageQuery;
import com.ly.sof.utils.page.PageList;
import com.ly.sof.utils.page.PageQueryUtils;
/**
* DriverAppealDAO
 * database table: driver_appeal
 * database table comments: DriverAppeal
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to 
 * directory <tt>(project-home)/dalgen</tt>
 * 
 * <AUTHOR>
 **/
public interface DriverAppealDAO {


    /**
    * 
    * sql:
    *
    <pre>INSERT      INTO         driver_appeal         (                                                                             env ,                                                                appeal_no ,                                                                driver_id ,                                                                arbitration_time ,                                                                appeal_amount ,                                                                appeal_content ,                                                                remark ,                                                                create_user ,                                                                create_time ,                                                                order_no                                         )      VALUES         (?,?,?,?,?,?,?,?,?,?)</pre>
    */
    public long insert(DriverAppealDO driverAppeal) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>UPDATE         driver_appeal      SET         appeal_no = ?                                                                                     , driver_id = ?                                                                                     , arbitration_time = ?                                                                                     , appeal_amount = ?                                                                                     , appeal_content = ?                                                                                     , remark = ?                                                                                     , create_user = ?                                                                                     , create_time = ?                                                                                     , order_no = ?                                                                  WHERE         id = ?</pre>
    */
    public int update(DriverAppealDO driverAppeal) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `env` ,                    `appeal_no` ,                    `driver_id` ,                    `arbitration_time` ,                    `appeal_amount` ,                    `appeal_content` ,                    `remark` ,                    `create_user` ,                    `create_time` ,                    `order_no`                                           FROM         driver_appeal                              WHERE         appeal_no = ?</pre>
    */
    public List<DriverAppealDO> queryByAppealNo(String appealNo) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `env` ,                    `appeal_no` ,                    `driver_id` ,                    `arbitration_time` ,                    `appeal_amount` ,                    `appeal_content` ,                    `remark` ,                    `create_user` ,                    `create_time` ,                    `order_no`                                           FROM         driver_appeal                              WHERE         appeal_content = ?</pre>
    */
    public List<DriverAppealDO> queryByAppealContent(String appealContent) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `env` ,                    `appeal_no` ,                    `driver_id` ,                    `arbitration_time` ,                    `appeal_amount` ,                    `appeal_content` ,                    `remark` ,                    `create_user` ,                    `create_time` ,                    `order_no`                                           FROM         driver_appeal                              WHERE         remark = ?</pre>
    */
    public List<DriverAppealDO> queryByRemark(String remark) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `env` ,                    `appeal_no` ,                    `driver_id` ,                    `arbitration_time` ,                    `appeal_amount` ,                    `appeal_content` ,                    `remark` ,                    `create_user` ,                    `create_time` ,                    `order_no`                                           FROM         driver_appeal                              WHERE         create_user = ?</pre>
    */
    public List<DriverAppealDO> queryByCreateUser(String createUser) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `env` ,                    `appeal_no` ,                    `driver_id` ,                    `arbitration_time` ,                    `appeal_amount` ,                    `appeal_content` ,                    `remark` ,                    `create_user` ,                    `create_time` ,                    `order_no`                                           FROM         driver_appeal                              WHERE         order_no = ?</pre>
    */
    public List<DriverAppealDO> queryByOrderNo(String orderNo) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `env` ,                    `appeal_no` ,                    `driver_id` ,                    `arbitration_time` ,                    `appeal_amount` ,                    `appeal_content` ,                    `remark` ,                    `create_user` ,                    `create_time` ,                    `order_no`                               FROM         driver_appeal                  WHERE         id = ?</pre>
    */
    public DriverAppealDO queryById(Long id) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `env` ,                    `appeal_no` ,                    `driver_id` ,                    `arbitration_time` ,                    `appeal_amount` ,                    `appeal_content` ,                    `remark` ,                    `create_user` ,                    `create_time` ,                    `order_no`                               FROM         driver_appeal                  WHERE         1=1                                                                                   AND                              id=?                                                                                                                                                AND                              env like concat('%', ?,'%' )                                                                                                                                                AND                              appeal_no like concat('%', ?,'%' )                                                                                                                                                AND                              driver_id=?                                                                                                                                                AND                              arbitration_time >= ?                                                            AND                              arbitration_time <= ?                                                                                                                                                AND                              appeal_amount=?                                                                                                                                                AND                              appeal_content like concat('%', ?,'%' )                                                                                                                                                AND                              remark like concat('%', ?,'%' )                                                                                                                                                AND                              create_user like concat('%', ?,'%' )                                                                                                                                                AND                              create_time >= ?                                                            AND                              create_time <= ?                                                                                                                                                AND                              order_no like concat('%', ?,'%' )                                                                                           ORDER BY         id DESC</pre>
    */
    public PageList<DriverAppealDO> findPage(FindPageQuery param) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `env` ,                    `appeal_no` ,                    `driver_id` ,                    `arbitration_time` ,                    `appeal_amount` ,                    `appeal_content` ,                    `remark` ,                    `create_user` ,                    `create_time` ,                    `order_no`                               FROM         driver_appeal                  WHERE         1=1                                                                                   AND                              id=?                                                                                                                                                AND                              env like concat('%', ?,'%' )                                                                                                                                                AND                              appeal_no like concat('%', ?,'%' )                                                                                                                                                AND                              driver_id=?                                                                                                                                                AND                              arbitration_time >= ?                                                            AND                              arbitration_time <= ?                                                                                                                                                AND                              appeal_amount=?                                                                                                                                                AND                              appeal_content like concat('%', ?,'%' )                                                                                                                                                AND                              remark like concat('%', ?,'%' )                                                                                                                                                AND                              create_user like concat('%', ?,'%' )                                                                                                                                                AND                              create_time >= ?                                                            AND                              create_time <= ?                                                                                                                                                AND                              order_no like concat('%', ?,'%' )                                                                                           ORDER BY         id DESC</pre>
    */
    public List<DriverAppealDO> queryAll(QueryAllQuery param) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>DELETE      FROM         driver_appeal                  WHERE         id = ?</pre>
    */
    public int delete(Long id) throws DataAccessException;

}



