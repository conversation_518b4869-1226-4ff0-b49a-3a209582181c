/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */package com.ly.travel.car.carowner.dal.daointerface;
import org.springframework.dao.DataAccessException;
import com.ly.travel.car.carowner.dal.operation.driverbillfreeze.*;
import com.ly.travel.car.carowner.dal.dataobject.*;


import java.io.*;
import java.net.*;
import java.util.*;

import java.math.BigInteger;

import com.ly.sof.utils.page.PageQuery;
import com.ly.sof.utils.page.PageList;
import com.ly.sof.utils.page.PageQueryUtils;
/**
* DriverBillFreezeDAO
 * database table: driver_bill_freeze
 * database table comments: DriverBillFreeze
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to 
 * directory <tt>(project-home)/dalgen</tt>
 * 
 * <AUTHOR>
 **/
public interface DriverBillFreezeDAO {


    /**
    * 
    * sql:
    *
    <pre>INSERT      INTO         driver_bill_freeze         (                                                                             bill_no ,                                                                env ,                                                                driver_id ,                                                                unfreeze_flag ,                                                                freeze_remark ,                                                                create_time ,                                                                create_user ,                                                                update_time ,                                                                update_user ,                                                                unfreeze_time ,                                                                freeze_type                                         )      VALUES         (?,?,?,?,?,?,?,?,?,?,?)</pre>
    */
    public long insert(DriverBillFreezeDO driverBillFreeze) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>UPDATE         driver_bill_freeze      SET         bill_no = ?                                                                                                                 , driver_id = ?                                                                                     , unfreeze_flag = ?                                                                                     , freeze_remark = ?                                                                                     , create_time = ?                                                                                     , create_user = ?                                                                                     , update_time = ?                                                                                     , update_user = ?                                                                                     , unfreeze_time = ?                                                                                     , freeze_type = ?                                                                  WHERE         id = ?</pre>
    */
    public int update(DriverBillFreezeDO driverBillFreeze) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `bill_no` ,                    `env` ,                    `driver_id` ,                    `unfreeze_flag` ,                    `freeze_remark` ,                    `create_time` ,                    `create_user` ,                    `update_time` ,                    `update_user` ,                    `unfreeze_time` ,                    `freeze_type`                                           FROM         driver_bill_freeze                              WHERE         bill_no = ?</pre>
    */
    public List<DriverBillFreezeDO> queryByBillNo(String billNo) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `bill_no` ,                    `env` ,                    `driver_id` ,                    `unfreeze_flag` ,                    `freeze_remark` ,                    `create_time` ,                    `create_user` ,                    `update_time` ,                    `update_user` ,                    `unfreeze_time` ,                    `freeze_type`                                           FROM         driver_bill_freeze                              WHERE         freeze_remark = ?</pre>
    */
    public List<DriverBillFreezeDO> queryByFreezeRemark(String freezeRemark) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `bill_no` ,                    `env` ,                    `driver_id` ,                    `unfreeze_flag` ,                    `freeze_remark` ,                    `create_time` ,                    `create_user` ,                    `update_time` ,                    `update_user` ,                    `unfreeze_time` ,                    `freeze_type`                                           FROM         driver_bill_freeze                              WHERE         create_user = ?</pre>
    */
    public List<DriverBillFreezeDO> queryByCreateUser(String createUser) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `bill_no` ,                    `env` ,                    `driver_id` ,                    `unfreeze_flag` ,                    `freeze_remark` ,                    `create_time` ,                    `create_user` ,                    `update_time` ,                    `update_user` ,                    `unfreeze_time` ,                    `freeze_type`                                           FROM         driver_bill_freeze                              WHERE         update_user = ?</pre>
    */
    public List<DriverBillFreezeDO> queryByUpdateUser(String updateUser) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `bill_no` ,                    `env` ,                    `driver_id` ,                    `unfreeze_flag` ,                    `freeze_remark` ,                    `create_time` ,                    `create_user` ,                    `update_time` ,                    `update_user` ,                    `unfreeze_time` ,                    `freeze_type`                               FROM         driver_bill_freeze                  WHERE         id = ?</pre>
    */
    public DriverBillFreezeDO queryById(Long id) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `bill_no` ,                    `env` ,                    `driver_id` ,                    `unfreeze_flag` ,                    `freeze_remark` ,                    `create_time` ,                    `create_user` ,                    `update_time` ,                    `update_user` ,                    `unfreeze_time` ,                    `freeze_type`                               FROM         driver_bill_freeze                  WHERE         1=1                                                                                   AND                              id=?                                                                                                                                                AND                              bill_no like concat('%', ?,'%' )                                                                                                                                                AND                              env like concat('%', ?,'%' )                                                                                                                                                AND                              driver_id=?                                                                                                                                                AND                              unfreeze_flag=?                                                                                                                                                AND                              freeze_remark like concat('%', ?,'%' )                                                                                                                                                AND                              create_time >= ?                                                            AND                              create_time <= ?                                                                                                                                                AND                              create_user like concat('%', ?,'%' )                                                                                                                                                AND                              update_time >= ?                                                            AND                              update_time <= ?                                                                                                                                                AND                              update_user like concat('%', ?,'%' )                                                                                                                                                AND                              unfreeze_time >= ?                                                            AND                              unfreeze_time <= ?                                                                                                                                                AND                              freeze_type=?                                                                                           ORDER BY         id DESC</pre>
    */
    public PageList<DriverBillFreezeDO> findPage(FindPageQuery param) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `bill_no` ,                    `env` ,                    `driver_id` ,                    `unfreeze_flag` ,                    `freeze_remark` ,                    `create_time` ,                    `create_user` ,                    `update_time` ,                    `update_user` ,                    `unfreeze_time` ,                    `freeze_type`                               FROM         driver_bill_freeze                  WHERE         1=1                                                                                   AND                              id=?                                                                                                                                                AND                              bill_no like concat('%', ?,'%' )                                                                                                                                                AND                              env like concat('%', ?,'%' )                                                                                                                                                AND                              driver_id=?                                                                                                                                                AND                              unfreeze_flag=?                                                                                                                                                AND                              freeze_remark like concat('%', ?,'%' )                                                                                                                                                AND                              create_time >= ?                                                            AND                              create_time <= ?                                                                                                                                                AND                              create_user like concat('%', ?,'%' )                                                                                                                                                AND                              update_time >= ?                                                            AND                              update_time <= ?                                                                                                                                                AND                              update_user like concat('%', ?,'%' )                                                                                                                                                AND                              unfreeze_time >= ?                                                            AND                              unfreeze_time <= ?                                                                                                                                                AND                              freeze_type=?                                                                                           ORDER BY         id DESC</pre>
    */
    public List<DriverBillFreezeDO> queryAll(QueryAllQuery param) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>DELETE      FROM         driver_bill_freeze                  WHERE         id = ?</pre>
    */
    public int delete(Long id) throws DataAccessException;

}



