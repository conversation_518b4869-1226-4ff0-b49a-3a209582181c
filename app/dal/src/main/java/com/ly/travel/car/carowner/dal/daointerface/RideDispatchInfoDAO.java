/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */package com.ly.travel.car.carowner.dal.daointerface;
import org.springframework.dao.DataAccessException;
import com.ly.travel.car.carowner.dal.operation.ridedispatchinfo.*;
import com.ly.travel.car.carowner.dal.dataobject.*;


import java.io.*;
import java.net.*;
import java.util.*;

import java.math.BigInteger;

import com.ly.sof.utils.page.PageQuery;
import com.ly.sof.utils.page.PageList;
import com.ly.sof.utils.page.PageQueryUtils;
/**
* RideDispatchInfoDAO
 * database table: ride_dispatch_info
 * database table comments: RideDispatchInfo
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to 
 * directory <tt>(project-home)/dalgen</tt>
 * 
 * <AUTHOR>
 **/
public interface RideDispatchInfoDAO {


    /**
    * 
    * sql:
    *
    <pre>INSERT      INTO         ride_dispatch_info         (                                                                             order_no ,                                                                status ,                                                                accept_time ,                                                                create_user ,                                                                create_time ,                                                                update_user ,                                                                update_time ,                                                                vehicle_no ,                                                                driver_name ,                                                                driver_mobile ,                                                                driver_trip_no ,                                                                driver_id ,                                                                hitch_percent                                         )      VALUES         (?,?,?,?,?,?,?,?,?,?,?,?,?)</pre>
    */
    public long insert(RideDispatchInfoDO rideDispatchInfo) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>UPDATE         ride_dispatch_info      SET         order_no = ?                                                                                     , status = ?                                                                                     , accept_time = ?                                                                                     , create_user = ?                                                                                     , create_time = ?                                                                                     , update_user = ?                                                                                     , update_time = ?                                                                                     , vehicle_no = ?                                                                                     , driver_name = ?                                                                                     , driver_mobile = ?                                                                                     , driver_trip_no = ?                                                                                     , driver_id = ?                                                                                     , hitch_percent = ?                                                                  WHERE         id = ?</pre>
    */
    public int update(RideDispatchInfoDO rideDispatchInfo) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `order_no` ,                    `status` ,                    `accept_time` ,                    `create_user` ,                    `create_time` ,                    `update_user` ,                    `update_time` ,                    `vehicle_no` ,                    `driver_name` ,                    `driver_mobile` ,                    `driver_trip_no` ,                    `driver_id` ,                    `hitch_percent`                                           FROM         ride_dispatch_info                              WHERE         order_no = ?</pre>
    */
    public List<RideDispatchInfoDO> queryByOrderNo(String orderNo) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `order_no` ,                    `status` ,                    `accept_time` ,                    `create_user` ,                    `create_time` ,                    `update_user` ,                    `update_time` ,                    `vehicle_no` ,                    `driver_name` ,                    `driver_mobile` ,                    `driver_trip_no` ,                    `driver_id` ,                    `hitch_percent`                                           FROM         ride_dispatch_info                              WHERE         create_user = ?</pre>
    */
    public List<RideDispatchInfoDO> queryByCreateUser(String createUser) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `order_no` ,                    `status` ,                    `accept_time` ,                    `create_user` ,                    `create_time` ,                    `update_user` ,                    `update_time` ,                    `vehicle_no` ,                    `driver_name` ,                    `driver_mobile` ,                    `driver_trip_no` ,                    `driver_id` ,                    `hitch_percent`                                           FROM         ride_dispatch_info                              WHERE         update_user = ?</pre>
    */
    public List<RideDispatchInfoDO> queryByUpdateUser(String updateUser) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `order_no` ,                    `status` ,                    `accept_time` ,                    `create_user` ,                    `create_time` ,                    `update_user` ,                    `update_time` ,                    `vehicle_no` ,                    `driver_name` ,                    `driver_mobile` ,                    `driver_trip_no` ,                    `driver_id` ,                    `hitch_percent`                                           FROM         ride_dispatch_info                              WHERE         vehicle_no = ?</pre>
    */
    public List<RideDispatchInfoDO> queryByVehicleNo(String vehicleNo) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `order_no` ,                    `status` ,                    `accept_time` ,                    `create_user` ,                    `create_time` ,                    `update_user` ,                    `update_time` ,                    `vehicle_no` ,                    `driver_name` ,                    `driver_mobile` ,                    `driver_trip_no` ,                    `driver_id` ,                    `hitch_percent`                                           FROM         ride_dispatch_info                              WHERE         driver_name = ?</pre>
    */
    public List<RideDispatchInfoDO> queryByDriverName(String driverName) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `order_no` ,                    `status` ,                    `accept_time` ,                    `create_user` ,                    `create_time` ,                    `update_user` ,                    `update_time` ,                    `vehicle_no` ,                    `driver_name` ,                    `driver_mobile` ,                    `driver_trip_no` ,                    `driver_id` ,                    `hitch_percent`                                           FROM         ride_dispatch_info                              WHERE         driver_mobile = ?</pre>
    */
    public List<RideDispatchInfoDO> queryByDriverMobile(String driverMobile) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `order_no` ,                    `status` ,                    `accept_time` ,                    `create_user` ,                    `create_time` ,                    `update_user` ,                    `update_time` ,                    `vehicle_no` ,                    `driver_name` ,                    `driver_mobile` ,                    `driver_trip_no` ,                    `driver_id` ,                    `hitch_percent`                                           FROM         ride_dispatch_info                              WHERE         driver_trip_no = ?</pre>
    */
    public List<RideDispatchInfoDO> queryByDriverTripNo(String driverTripNo) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `order_no` ,                    `status` ,                    `accept_time` ,                    `create_user` ,                    `create_time` ,                    `update_user` ,                    `update_time` ,                    `vehicle_no` ,                    `driver_name` ,                    `driver_mobile` ,                    `driver_trip_no` ,                    `driver_id` ,                    `hitch_percent`                               FROM         ride_dispatch_info                  WHERE         id = ?</pre>
    */
    public RideDispatchInfoDO queryById(Long id) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `order_no` ,                    `status` ,                    `accept_time` ,                    `create_user` ,                    `create_time` ,                    `update_user` ,                    `update_time` ,                    `vehicle_no` ,                    `driver_name` ,                    `driver_mobile` ,                    `driver_trip_no` ,                    `driver_id` ,                    `hitch_percent`                               FROM         ride_dispatch_info                  WHERE         1=1                                                                                   AND                              id=?                                                                                                                                                AND                              order_no like concat('%', ?,'%' )                                                                                                                                                AND                              status=?                                                                                                                                                AND                              accept_time >= ?                                                            AND                              accept_time <= ?                                                                                                                                                AND                              create_user like concat('%', ?,'%' )                                                                                                                                                AND                              create_time >= ?                                                            AND                              create_time <= ?                                                                                                                                                AND                              update_user like concat('%', ?,'%' )                                                                                                                                                AND                              update_time >= ?                                                            AND                              update_time <= ?                                                                                                                                                AND                              vehicle_no like concat('%', ?,'%' )                                                                                                                                                AND                              driver_name like concat('%', ?,'%' )                                                                                                                                                AND                              driver_mobile like concat('%', ?,'%' )                                                                                                                                                AND                              driver_trip_no like concat('%', ?,'%' )                                                                                                                                                AND                              driver_id=?                                                                                                                                                AND                              hitch_percent=?                                                                                           ORDER BY         id DESC</pre>
    */
    public PageList<RideDispatchInfoDO> findPage(FindPageQuery param) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `order_no` ,                    `status` ,                    `accept_time` ,                    `create_user` ,                    `create_time` ,                    `update_user` ,                    `update_time` ,                    `vehicle_no` ,                    `driver_name` ,                    `driver_mobile` ,                    `driver_trip_no` ,                    `driver_id` ,                    `hitch_percent`                               FROM         ride_dispatch_info                  WHERE         1=1                                                                                   AND                              id=?                                                                                                                                                AND                              order_no like concat('%', ?,'%' )                                                                                                                                                AND                              status=?                                                                                                                                                AND                              accept_time >= ?                                                            AND                              accept_time <= ?                                                                                                                                                AND                              create_user like concat('%', ?,'%' )                                                                                                                                                AND                              create_time >= ?                                                            AND                              create_time <= ?                                                                                                                                                AND                              update_user like concat('%', ?,'%' )                                                                                                                                                AND                              update_time >= ?                                                            AND                              update_time <= ?                                                                                                                                                AND                              vehicle_no like concat('%', ?,'%' )                                                                                                                                                AND                              driver_name like concat('%', ?,'%' )                                                                                                                                                AND                              driver_mobile like concat('%', ?,'%' )                                                                                                                                                AND                              driver_trip_no like concat('%', ?,'%' )                                                                                                                                                AND                              driver_id=?                                                                                                                                                AND                              hitch_percent=?                                                                                           ORDER BY         id DESC</pre>
    */
    public List<RideDispatchInfoDO> queryAll(QueryAllQuery param) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>DELETE      FROM         ride_dispatch_info                  WHERE         id = ?</pre>
    */
    public int delete(Long id) throws DataAccessException;

}



