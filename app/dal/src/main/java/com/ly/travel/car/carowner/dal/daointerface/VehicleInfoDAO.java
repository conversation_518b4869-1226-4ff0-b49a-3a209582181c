/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */package com.ly.travel.car.carowner.dal.daointerface;
import org.springframework.dao.DataAccessException;
import com.ly.travel.car.carowner.dal.operation.vehicleinfo.*;
import com.ly.travel.car.carowner.dal.dataobject.*;


import java.io.*;
import java.net.*;
import java.util.*;

import java.math.BigInteger;

import com.ly.sof.utils.page.PageQuery;
import com.ly.sof.utils.page.PageList;
import com.ly.sof.utils.page.PageQueryUtils;
/**
* VehicleInfoDAO
 * database table: vehicle_info
 * database table comments: VehicleInfo
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to 
 * directory <tt>(project-home)/dalgen</tt>
 * 
 * <AUTHOR>
 **/
public interface VehicleInfoDAO {


    /**
    * 
    * sql:
    *
    <pre>INSERT      INTO         vehicle_info         (                                                                             env ,                                                                vehicle_no ,                                                                driver_id ,                                                                vehicle_no_city ,                                                                vehicle_type ,                                                                vehicle_owner ,                                                                use_character ,                                                                car_brand ,                                                                car_model ,                                                                vin ,                                                                color ,                                                                register_date ,                                                                engine_no ,                                                                issue_date ,                                                                auth_seat_num ,                                                                pass_seat_num ,                                                                driving_license_picture ,                                                                fuel_type ,                                                                brand_model ,                                                                vehicle_status ,                                                                inspect_end_date ,                                                                check_date ,                                                                status ,                                                                verify_remark ,                                                                unaudit_reason ,                                                                unaudit_remark ,                                                                vehicle_picture_start_date ,                                                                vehicle_valid_period ,                                                                vehicle_picture ,                                                                create_user ,                                                                create_time ,                                                                update_user ,                                                                update_time                                         )      VALUES         (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)</pre>
    */
    public long insert(VehicleInfoDO vehicleInfo) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>UPDATE         vehicle_info      SET         vehicle_no = ?                                                                                     , driver_id = ?                                                                                     , vehicle_no_city = ?                                                                                     , vehicle_type = ?                                                                                     , vehicle_owner = ?                                                                                     , use_character = ?                                                                                     , car_brand = ?                                                                                     , car_model = ?                                                                                     , vin = ?                                                                                     , color = ?                                                                                     , register_date = ?                                                                                     , engine_no = ?                                                                                     , issue_date = ?                                                                                     , auth_seat_num = ?                                                                                     , pass_seat_num = ?                                                                                     , driving_license_picture = ?                                                                                     , fuel_type = ?                                                                                     , brand_model = ?                                                                                     , vehicle_status = ?                                                                                     , inspect_end_date = ?                                                                                     , check_date = ?                                                                                     , status = ?                                                                                     , verify_remark = ?                                                                                     , unaudit_reason = ?                                                                                     , unaudit_remark = ?                                                                                     , vehicle_picture_start_date = ?                                                                                     , vehicle_valid_period = ?                                                                                     , vehicle_picture = ?                                                                                     , create_user = ?                                                                                     , create_time = ?                                                                                     , update_user = ?                                                                                     , update_time = ?                                                                  WHERE         id = ?</pre>
    */
    public int update(VehicleInfoDO vehicleInfo) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `env` ,                    `vehicle_no` ,                    `driver_id` ,                    `vehicle_no_city` ,                    `vehicle_type` ,                    `vehicle_owner` ,                    `use_character` ,                    `car_brand` ,                    `car_model` ,                    `vin` ,                    `color` ,                    `register_date` ,                    `engine_no` ,                    `issue_date` ,                    `auth_seat_num` ,                    `pass_seat_num` ,                    `driving_license_picture` ,                    `fuel_type` ,                    `brand_model` ,                    `vehicle_status` ,                    `inspect_end_date` ,                    `check_date` ,                    `status` ,                    `verify_remark` ,                    `unaudit_reason` ,                    `unaudit_remark` ,                    `vehicle_picture_start_date` ,                    `vehicle_valid_period` ,                    `vehicle_picture` ,                    `create_user` ,                    `create_time` ,                    `update_user` ,                    `update_time`                                           FROM         vehicle_info                              WHERE         vehicle_no = ?</pre>
    */
    public List<VehicleInfoDO> queryByVehicleNo(String vehicleNo) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `env` ,                    `vehicle_no` ,                    `driver_id` ,                    `vehicle_no_city` ,                    `vehicle_type` ,                    `vehicle_owner` ,                    `use_character` ,                    `car_brand` ,                    `car_model` ,                    `vin` ,                    `color` ,                    `register_date` ,                    `engine_no` ,                    `issue_date` ,                    `auth_seat_num` ,                    `pass_seat_num` ,                    `driving_license_picture` ,                    `fuel_type` ,                    `brand_model` ,                    `vehicle_status` ,                    `inspect_end_date` ,                    `check_date` ,                    `status` ,                    `verify_remark` ,                    `unaudit_reason` ,                    `unaudit_remark` ,                    `vehicle_picture_start_date` ,                    `vehicle_valid_period` ,                    `vehicle_picture` ,                    `create_user` ,                    `create_time` ,                    `update_user` ,                    `update_time`                                           FROM         vehicle_info                              WHERE         vehicle_no_city = ?</pre>
    */
    public List<VehicleInfoDO> queryByVehicleNoCity(String vehicleNoCity) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `env` ,                    `vehicle_no` ,                    `driver_id` ,                    `vehicle_no_city` ,                    `vehicle_type` ,                    `vehicle_owner` ,                    `use_character` ,                    `car_brand` ,                    `car_model` ,                    `vin` ,                    `color` ,                    `register_date` ,                    `engine_no` ,                    `issue_date` ,                    `auth_seat_num` ,                    `pass_seat_num` ,                    `driving_license_picture` ,                    `fuel_type` ,                    `brand_model` ,                    `vehicle_status` ,                    `inspect_end_date` ,                    `check_date` ,                    `status` ,                    `verify_remark` ,                    `unaudit_reason` ,                    `unaudit_remark` ,                    `vehicle_picture_start_date` ,                    `vehicle_valid_period` ,                    `vehicle_picture` ,                    `create_user` ,                    `create_time` ,                    `update_user` ,                    `update_time`                                           FROM         vehicle_info                              WHERE         vehicle_type = ?</pre>
    */
    public List<VehicleInfoDO> queryByVehicleType(String vehicleType) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `env` ,                    `vehicle_no` ,                    `driver_id` ,                    `vehicle_no_city` ,                    `vehicle_type` ,                    `vehicle_owner` ,                    `use_character` ,                    `car_brand` ,                    `car_model` ,                    `vin` ,                    `color` ,                    `register_date` ,                    `engine_no` ,                    `issue_date` ,                    `auth_seat_num` ,                    `pass_seat_num` ,                    `driving_license_picture` ,                    `fuel_type` ,                    `brand_model` ,                    `vehicle_status` ,                    `inspect_end_date` ,                    `check_date` ,                    `status` ,                    `verify_remark` ,                    `unaudit_reason` ,                    `unaudit_remark` ,                    `vehicle_picture_start_date` ,                    `vehicle_valid_period` ,                    `vehicle_picture` ,                    `create_user` ,                    `create_time` ,                    `update_user` ,                    `update_time`                                           FROM         vehicle_info                              WHERE         vehicle_owner = ?</pre>
    */
    public List<VehicleInfoDO> queryByVehicleOwner(String vehicleOwner) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `env` ,                    `vehicle_no` ,                    `driver_id` ,                    `vehicle_no_city` ,                    `vehicle_type` ,                    `vehicle_owner` ,                    `use_character` ,                    `car_brand` ,                    `car_model` ,                    `vin` ,                    `color` ,                    `register_date` ,                    `engine_no` ,                    `issue_date` ,                    `auth_seat_num` ,                    `pass_seat_num` ,                    `driving_license_picture` ,                    `fuel_type` ,                    `brand_model` ,                    `vehicle_status` ,                    `inspect_end_date` ,                    `check_date` ,                    `status` ,                    `verify_remark` ,                    `unaudit_reason` ,                    `unaudit_remark` ,                    `vehicle_picture_start_date` ,                    `vehicle_valid_period` ,                    `vehicle_picture` ,                    `create_user` ,                    `create_time` ,                    `update_user` ,                    `update_time`                                           FROM         vehicle_info                              WHERE         use_character = ?</pre>
    */
    public List<VehicleInfoDO> queryByUseCharacter(String useCharacter) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `env` ,                    `vehicle_no` ,                    `driver_id` ,                    `vehicle_no_city` ,                    `vehicle_type` ,                    `vehicle_owner` ,                    `use_character` ,                    `car_brand` ,                    `car_model` ,                    `vin` ,                    `color` ,                    `register_date` ,                    `engine_no` ,                    `issue_date` ,                    `auth_seat_num` ,                    `pass_seat_num` ,                    `driving_license_picture` ,                    `fuel_type` ,                    `brand_model` ,                    `vehicle_status` ,                    `inspect_end_date` ,                    `check_date` ,                    `status` ,                    `verify_remark` ,                    `unaudit_reason` ,                    `unaudit_remark` ,                    `vehicle_picture_start_date` ,                    `vehicle_valid_period` ,                    `vehicle_picture` ,                    `create_user` ,                    `create_time` ,                    `update_user` ,                    `update_time`                                           FROM         vehicle_info                              WHERE         car_brand = ?</pre>
    */
    public List<VehicleInfoDO> queryByCarBrand(String carBrand) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `env` ,                    `vehicle_no` ,                    `driver_id` ,                    `vehicle_no_city` ,                    `vehicle_type` ,                    `vehicle_owner` ,                    `use_character` ,                    `car_brand` ,                    `car_model` ,                    `vin` ,                    `color` ,                    `register_date` ,                    `engine_no` ,                    `issue_date` ,                    `auth_seat_num` ,                    `pass_seat_num` ,                    `driving_license_picture` ,                    `fuel_type` ,                    `brand_model` ,                    `vehicle_status` ,                    `inspect_end_date` ,                    `check_date` ,                    `status` ,                    `verify_remark` ,                    `unaudit_reason` ,                    `unaudit_remark` ,                    `vehicle_picture_start_date` ,                    `vehicle_valid_period` ,                    `vehicle_picture` ,                    `create_user` ,                    `create_time` ,                    `update_user` ,                    `update_time`                                           FROM         vehicle_info                              WHERE         car_model = ?</pre>
    */
    public List<VehicleInfoDO> queryByCarModel(String carModel) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `env` ,                    `vehicle_no` ,                    `driver_id` ,                    `vehicle_no_city` ,                    `vehicle_type` ,                    `vehicle_owner` ,                    `use_character` ,                    `car_brand` ,                    `car_model` ,                    `vin` ,                    `color` ,                    `register_date` ,                    `engine_no` ,                    `issue_date` ,                    `auth_seat_num` ,                    `pass_seat_num` ,                    `driving_license_picture` ,                    `fuel_type` ,                    `brand_model` ,                    `vehicle_status` ,                    `inspect_end_date` ,                    `check_date` ,                    `status` ,                    `verify_remark` ,                    `unaudit_reason` ,                    `unaudit_remark` ,                    `vehicle_picture_start_date` ,                    `vehicle_valid_period` ,                    `vehicle_picture` ,                    `create_user` ,                    `create_time` ,                    `update_user` ,                    `update_time`                                           FROM         vehicle_info                              WHERE         vin = ?</pre>
    */
    public List<VehicleInfoDO> queryByVin(String vin) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `env` ,                    `vehicle_no` ,                    `driver_id` ,                    `vehicle_no_city` ,                    `vehicle_type` ,                    `vehicle_owner` ,                    `use_character` ,                    `car_brand` ,                    `car_model` ,                    `vin` ,                    `color` ,                    `register_date` ,                    `engine_no` ,                    `issue_date` ,                    `auth_seat_num` ,                    `pass_seat_num` ,                    `driving_license_picture` ,                    `fuel_type` ,                    `brand_model` ,                    `vehicle_status` ,                    `inspect_end_date` ,                    `check_date` ,                    `status` ,                    `verify_remark` ,                    `unaudit_reason` ,                    `unaudit_remark` ,                    `vehicle_picture_start_date` ,                    `vehicle_valid_period` ,                    `vehicle_picture` ,                    `create_user` ,                    `create_time` ,                    `update_user` ,                    `update_time`                                           FROM         vehicle_info                              WHERE         color = ?</pre>
    */
    public List<VehicleInfoDO> queryByColor(String color) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `env` ,                    `vehicle_no` ,                    `driver_id` ,                    `vehicle_no_city` ,                    `vehicle_type` ,                    `vehicle_owner` ,                    `use_character` ,                    `car_brand` ,                    `car_model` ,                    `vin` ,                    `color` ,                    `register_date` ,                    `engine_no` ,                    `issue_date` ,                    `auth_seat_num` ,                    `pass_seat_num` ,                    `driving_license_picture` ,                    `fuel_type` ,                    `brand_model` ,                    `vehicle_status` ,                    `inspect_end_date` ,                    `check_date` ,                    `status` ,                    `verify_remark` ,                    `unaudit_reason` ,                    `unaudit_remark` ,                    `vehicle_picture_start_date` ,                    `vehicle_valid_period` ,                    `vehicle_picture` ,                    `create_user` ,                    `create_time` ,                    `update_user` ,                    `update_time`                                           FROM         vehicle_info                              WHERE         register_date = ?</pre>
    */
    public List<VehicleInfoDO> queryByRegisterDate(String registerDate) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `env` ,                    `vehicle_no` ,                    `driver_id` ,                    `vehicle_no_city` ,                    `vehicle_type` ,                    `vehicle_owner` ,                    `use_character` ,                    `car_brand` ,                    `car_model` ,                    `vin` ,                    `color` ,                    `register_date` ,                    `engine_no` ,                    `issue_date` ,                    `auth_seat_num` ,                    `pass_seat_num` ,                    `driving_license_picture` ,                    `fuel_type` ,                    `brand_model` ,                    `vehicle_status` ,                    `inspect_end_date` ,                    `check_date` ,                    `status` ,                    `verify_remark` ,                    `unaudit_reason` ,                    `unaudit_remark` ,                    `vehicle_picture_start_date` ,                    `vehicle_valid_period` ,                    `vehicle_picture` ,                    `create_user` ,                    `create_time` ,                    `update_user` ,                    `update_time`                                           FROM         vehicle_info                              WHERE         engine_no = ?</pre>
    */
    public List<VehicleInfoDO> queryByEngineNo(String engineNo) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `env` ,                    `vehicle_no` ,                    `driver_id` ,                    `vehicle_no_city` ,                    `vehicle_type` ,                    `vehicle_owner` ,                    `use_character` ,                    `car_brand` ,                    `car_model` ,                    `vin` ,                    `color` ,                    `register_date` ,                    `engine_no` ,                    `issue_date` ,                    `auth_seat_num` ,                    `pass_seat_num` ,                    `driving_license_picture` ,                    `fuel_type` ,                    `brand_model` ,                    `vehicle_status` ,                    `inspect_end_date` ,                    `check_date` ,                    `status` ,                    `verify_remark` ,                    `unaudit_reason` ,                    `unaudit_remark` ,                    `vehicle_picture_start_date` ,                    `vehicle_valid_period` ,                    `vehicle_picture` ,                    `create_user` ,                    `create_time` ,                    `update_user` ,                    `update_time`                                           FROM         vehicle_info                              WHERE         issue_date = ?</pre>
    */
    public List<VehicleInfoDO> queryByIssueDate(String issueDate) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `env` ,                    `vehicle_no` ,                    `driver_id` ,                    `vehicle_no_city` ,                    `vehicle_type` ,                    `vehicle_owner` ,                    `use_character` ,                    `car_brand` ,                    `car_model` ,                    `vin` ,                    `color` ,                    `register_date` ,                    `engine_no` ,                    `issue_date` ,                    `auth_seat_num` ,                    `pass_seat_num` ,                    `driving_license_picture` ,                    `fuel_type` ,                    `brand_model` ,                    `vehicle_status` ,                    `inspect_end_date` ,                    `check_date` ,                    `status` ,                    `verify_remark` ,                    `unaudit_reason` ,                    `unaudit_remark` ,                    `vehicle_picture_start_date` ,                    `vehicle_valid_period` ,                    `vehicle_picture` ,                    `create_user` ,                    `create_time` ,                    `update_user` ,                    `update_time`                                           FROM         vehicle_info                              WHERE         driving_license_picture = ?</pre>
    */
    public List<VehicleInfoDO> queryByDrivingLicensePicture(String drivingLicensePicture) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `env` ,                    `vehicle_no` ,                    `driver_id` ,                    `vehicle_no_city` ,                    `vehicle_type` ,                    `vehicle_owner` ,                    `use_character` ,                    `car_brand` ,                    `car_model` ,                    `vin` ,                    `color` ,                    `register_date` ,                    `engine_no` ,                    `issue_date` ,                    `auth_seat_num` ,                    `pass_seat_num` ,                    `driving_license_picture` ,                    `fuel_type` ,                    `brand_model` ,                    `vehicle_status` ,                    `inspect_end_date` ,                    `check_date` ,                    `status` ,                    `verify_remark` ,                    `unaudit_reason` ,                    `unaudit_remark` ,                    `vehicle_picture_start_date` ,                    `vehicle_valid_period` ,                    `vehicle_picture` ,                    `create_user` ,                    `create_time` ,                    `update_user` ,                    `update_time`                                           FROM         vehicle_info                              WHERE         fuel_type = ?</pre>
    */
    public List<VehicleInfoDO> queryByFuelType(String fuelType) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `env` ,                    `vehicle_no` ,                    `driver_id` ,                    `vehicle_no_city` ,                    `vehicle_type` ,                    `vehicle_owner` ,                    `use_character` ,                    `car_brand` ,                    `car_model` ,                    `vin` ,                    `color` ,                    `register_date` ,                    `engine_no` ,                    `issue_date` ,                    `auth_seat_num` ,                    `pass_seat_num` ,                    `driving_license_picture` ,                    `fuel_type` ,                    `brand_model` ,                    `vehicle_status` ,                    `inspect_end_date` ,                    `check_date` ,                    `status` ,                    `verify_remark` ,                    `unaudit_reason` ,                    `unaudit_remark` ,                    `vehicle_picture_start_date` ,                    `vehicle_valid_period` ,                    `vehicle_picture` ,                    `create_user` ,                    `create_time` ,                    `update_user` ,                    `update_time`                                           FROM         vehicle_info                              WHERE         brand_model = ?</pre>
    */
    public List<VehicleInfoDO> queryByBrandModel(String brandModel) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `env` ,                    `vehicle_no` ,                    `driver_id` ,                    `vehicle_no_city` ,                    `vehicle_type` ,                    `vehicle_owner` ,                    `use_character` ,                    `car_brand` ,                    `car_model` ,                    `vin` ,                    `color` ,                    `register_date` ,                    `engine_no` ,                    `issue_date` ,                    `auth_seat_num` ,                    `pass_seat_num` ,                    `driving_license_picture` ,                    `fuel_type` ,                    `brand_model` ,                    `vehicle_status` ,                    `inspect_end_date` ,                    `check_date` ,                    `status` ,                    `verify_remark` ,                    `unaudit_reason` ,                    `unaudit_remark` ,                    `vehicle_picture_start_date` ,                    `vehicle_valid_period` ,                    `vehicle_picture` ,                    `create_user` ,                    `create_time` ,                    `update_user` ,                    `update_time`                                           FROM         vehicle_info                              WHERE         vehicle_status = ?</pre>
    */
    public List<VehicleInfoDO> queryByVehicleStatus(String vehicleStatus) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `env` ,                    `vehicle_no` ,                    `driver_id` ,                    `vehicle_no_city` ,                    `vehicle_type` ,                    `vehicle_owner` ,                    `use_character` ,                    `car_brand` ,                    `car_model` ,                    `vin` ,                    `color` ,                    `register_date` ,                    `engine_no` ,                    `issue_date` ,                    `auth_seat_num` ,                    `pass_seat_num` ,                    `driving_license_picture` ,                    `fuel_type` ,                    `brand_model` ,                    `vehicle_status` ,                    `inspect_end_date` ,                    `check_date` ,                    `status` ,                    `verify_remark` ,                    `unaudit_reason` ,                    `unaudit_remark` ,                    `vehicle_picture_start_date` ,                    `vehicle_valid_period` ,                    `vehicle_picture` ,                    `create_user` ,                    `create_time` ,                    `update_user` ,                    `update_time`                                           FROM         vehicle_info                              WHERE         inspect_end_date = ?</pre>
    */
    public List<VehicleInfoDO> queryByInspectEndDate(String inspectEndDate) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `env` ,                    `vehicle_no` ,                    `driver_id` ,                    `vehicle_no_city` ,                    `vehicle_type` ,                    `vehicle_owner` ,                    `use_character` ,                    `car_brand` ,                    `car_model` ,                    `vin` ,                    `color` ,                    `register_date` ,                    `engine_no` ,                    `issue_date` ,                    `auth_seat_num` ,                    `pass_seat_num` ,                    `driving_license_picture` ,                    `fuel_type` ,                    `brand_model` ,                    `vehicle_status` ,                    `inspect_end_date` ,                    `check_date` ,                    `status` ,                    `verify_remark` ,                    `unaudit_reason` ,                    `unaudit_remark` ,                    `vehicle_picture_start_date` ,                    `vehicle_valid_period` ,                    `vehicle_picture` ,                    `create_user` ,                    `create_time` ,                    `update_user` ,                    `update_time`                                           FROM         vehicle_info                              WHERE         check_date = ?</pre>
    */
    public List<VehicleInfoDO> queryByCheckDate(String checkDate) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `env` ,                    `vehicle_no` ,                    `driver_id` ,                    `vehicle_no_city` ,                    `vehicle_type` ,                    `vehicle_owner` ,                    `use_character` ,                    `car_brand` ,                    `car_model` ,                    `vin` ,                    `color` ,                    `register_date` ,                    `engine_no` ,                    `issue_date` ,                    `auth_seat_num` ,                    `pass_seat_num` ,                    `driving_license_picture` ,                    `fuel_type` ,                    `brand_model` ,                    `vehicle_status` ,                    `inspect_end_date` ,                    `check_date` ,                    `status` ,                    `verify_remark` ,                    `unaudit_reason` ,                    `unaudit_remark` ,                    `vehicle_picture_start_date` ,                    `vehicle_valid_period` ,                    `vehicle_picture` ,                    `create_user` ,                    `create_time` ,                    `update_user` ,                    `update_time`                                           FROM         vehicle_info                              WHERE         verify_remark = ?</pre>
    */
    public List<VehicleInfoDO> queryByVerifyRemark(String verifyRemark) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `env` ,                    `vehicle_no` ,                    `driver_id` ,                    `vehicle_no_city` ,                    `vehicle_type` ,                    `vehicle_owner` ,                    `use_character` ,                    `car_brand` ,                    `car_model` ,                    `vin` ,                    `color` ,                    `register_date` ,                    `engine_no` ,                    `issue_date` ,                    `auth_seat_num` ,                    `pass_seat_num` ,                    `driving_license_picture` ,                    `fuel_type` ,                    `brand_model` ,                    `vehicle_status` ,                    `inspect_end_date` ,                    `check_date` ,                    `status` ,                    `verify_remark` ,                    `unaudit_reason` ,                    `unaudit_remark` ,                    `vehicle_picture_start_date` ,                    `vehicle_valid_period` ,                    `vehicle_picture` ,                    `create_user` ,                    `create_time` ,                    `update_user` ,                    `update_time`                                           FROM         vehicle_info                              WHERE         unaudit_remark = ?</pre>
    */
    public List<VehicleInfoDO> queryByUnauditRemark(String unauditRemark) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `env` ,                    `vehicle_no` ,                    `driver_id` ,                    `vehicle_no_city` ,                    `vehicle_type` ,                    `vehicle_owner` ,                    `use_character` ,                    `car_brand` ,                    `car_model` ,                    `vin` ,                    `color` ,                    `register_date` ,                    `engine_no` ,                    `issue_date` ,                    `auth_seat_num` ,                    `pass_seat_num` ,                    `driving_license_picture` ,                    `fuel_type` ,                    `brand_model` ,                    `vehicle_status` ,                    `inspect_end_date` ,                    `check_date` ,                    `status` ,                    `verify_remark` ,                    `unaudit_reason` ,                    `unaudit_remark` ,                    `vehicle_picture_start_date` ,                    `vehicle_valid_period` ,                    `vehicle_picture` ,                    `create_user` ,                    `create_time` ,                    `update_user` ,                    `update_time`                                           FROM         vehicle_info                              WHERE         vehicle_picture = ?</pre>
    */
    public List<VehicleInfoDO> queryByVehiclePicture(String vehiclePicture) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `env` ,                    `vehicle_no` ,                    `driver_id` ,                    `vehicle_no_city` ,                    `vehicle_type` ,                    `vehicle_owner` ,                    `use_character` ,                    `car_brand` ,                    `car_model` ,                    `vin` ,                    `color` ,                    `register_date` ,                    `engine_no` ,                    `issue_date` ,                    `auth_seat_num` ,                    `pass_seat_num` ,                    `driving_license_picture` ,                    `fuel_type` ,                    `brand_model` ,                    `vehicle_status` ,                    `inspect_end_date` ,                    `check_date` ,                    `status` ,                    `verify_remark` ,                    `unaudit_reason` ,                    `unaudit_remark` ,                    `vehicle_picture_start_date` ,                    `vehicle_valid_period` ,                    `vehicle_picture` ,                    `create_user` ,                    `create_time` ,                    `update_user` ,                    `update_time`                                           FROM         vehicle_info                              WHERE         create_user = ?</pre>
    */
    public List<VehicleInfoDO> queryByCreateUser(String createUser) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `env` ,                    `vehicle_no` ,                    `driver_id` ,                    `vehicle_no_city` ,                    `vehicle_type` ,                    `vehicle_owner` ,                    `use_character` ,                    `car_brand` ,                    `car_model` ,                    `vin` ,                    `color` ,                    `register_date` ,                    `engine_no` ,                    `issue_date` ,                    `auth_seat_num` ,                    `pass_seat_num` ,                    `driving_license_picture` ,                    `fuel_type` ,                    `brand_model` ,                    `vehicle_status` ,                    `inspect_end_date` ,                    `check_date` ,                    `status` ,                    `verify_remark` ,                    `unaudit_reason` ,                    `unaudit_remark` ,                    `vehicle_picture_start_date` ,                    `vehicle_valid_period` ,                    `vehicle_picture` ,                    `create_user` ,                    `create_time` ,                    `update_user` ,                    `update_time`                                           FROM         vehicle_info                              WHERE         update_user = ?</pre>
    */
    public List<VehicleInfoDO> queryByUpdateUser(String updateUser) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `env` ,                    `vehicle_no` ,                    `driver_id` ,                    `vehicle_no_city` ,                    `vehicle_type` ,                    `vehicle_owner` ,                    `use_character` ,                    `car_brand` ,                    `car_model` ,                    `vin` ,                    `color` ,                    `register_date` ,                    `engine_no` ,                    `issue_date` ,                    `auth_seat_num` ,                    `pass_seat_num` ,                    `driving_license_picture` ,                    `fuel_type` ,                    `brand_model` ,                    `vehicle_status` ,                    `inspect_end_date` ,                    `check_date` ,                    `status` ,                    `verify_remark` ,                    `unaudit_reason` ,                    `unaudit_remark` ,                    `vehicle_picture_start_date` ,                    `vehicle_valid_period` ,                    `vehicle_picture` ,                    `create_user` ,                    `create_time` ,                    `update_user` ,                    `update_time`                               FROM         vehicle_info                  WHERE         id = ?</pre>
    */
    public VehicleInfoDO queryById(Long id) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `env` ,                    `vehicle_no` ,                    `driver_id` ,                    `vehicle_no_city` ,                    `vehicle_type` ,                    `vehicle_owner` ,                    `use_character` ,                    `car_brand` ,                    `car_model` ,                    `vin` ,                    `color` ,                    `register_date` ,                    `engine_no` ,                    `issue_date` ,                    `auth_seat_num` ,                    `pass_seat_num` ,                    `driving_license_picture` ,                    `fuel_type` ,                    `brand_model` ,                    `vehicle_status` ,                    `inspect_end_date` ,                    `check_date` ,                    `status` ,                    `verify_remark` ,                    `unaudit_reason` ,                    `unaudit_remark` ,                    `vehicle_picture_start_date` ,                    `vehicle_valid_period` ,                    `vehicle_picture` ,                    `create_user` ,                    `create_time` ,                    `update_user` ,                    `update_time`                               FROM         vehicle_info                  WHERE         1=1                                                                                   AND                              id=?                                                                                                                                                AND                              env like concat('%', ?,'%' )                                                                                                                                                AND                              vehicle_no like concat('%', ?,'%' )                                                                                                                                                AND                              driver_id=?                                                                                                                                                AND                              vehicle_no_city like concat('%', ?,'%' )                                                                                                                                                AND                              vehicle_type like concat('%', ?,'%' )                                                                                                                                                AND                              vehicle_owner like concat('%', ?,'%' )                                                                                                                                                AND                              use_character like concat('%', ?,'%' )                                                                                                                                                AND                              car_brand like concat('%', ?,'%' )                                                                                                                                                AND                              car_model like concat('%', ?,'%' )                                                                                                                                                AND                              vin like concat('%', ?,'%' )                                                                                                                                                AND                              color like concat('%', ?,'%' )                                                                                                                                                AND                              register_date like concat('%', ?,'%' )                                                                                                                                                AND                              engine_no like concat('%', ?,'%' )                                                                                                                                                AND                              issue_date like concat('%', ?,'%' )                                                                                                                                                AND                              auth_seat_num=?                                                                                                                                                AND                              pass_seat_num=?                                                                                                                                                AND                              driving_license_picture like concat('%', ?,'%' )                                                                                                                                                AND                              fuel_type like concat('%', ?,'%' )                                                                                                                                                AND                              brand_model like concat('%', ?,'%' )                                                                                                                                                AND                              vehicle_status like concat('%', ?,'%' )                                                                                                                                                AND                              inspect_end_date like concat('%', ?,'%' )                                                                                                                                                AND                              check_date like concat('%', ?,'%' )                                                                                                                                                AND                              status=?                                                                                                                                                AND                              verify_remark like concat('%', ?,'%' )                                                                                                                                                AND                              unaudit_reason=?                                                                                                                                                AND                              unaudit_remark like concat('%', ?,'%' )                                                                                                                                                AND                              vehicle_picture_start_date >= ?                                                            AND                              vehicle_picture_start_date <= ?                                                                                                                                                AND                              vehicle_valid_period=?                                                                                                                                                AND                              vehicle_picture like concat('%', ?,'%' )                                                                                                                                                AND                              create_user like concat('%', ?,'%' )                                                                                                                                                AND                              create_time >= ?                                                            AND                              create_time <= ?                                                                                                                                                AND                              update_user like concat('%', ?,'%' )                                                                                                                                                AND                              update_time >= ?                                                            AND                              update_time <= ?                                                                                           ORDER BY         id DESC</pre>
    */
    public PageList<VehicleInfoDO> findPage(FindPageQuery param) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>SELECT         `id` ,                    `env` ,                    `vehicle_no` ,                    `driver_id` ,                    `vehicle_no_city` ,                    `vehicle_type` ,                    `vehicle_owner` ,                    `use_character` ,                    `car_brand` ,                    `car_model` ,                    `vin` ,                    `color` ,                    `register_date` ,                    `engine_no` ,                    `issue_date` ,                    `auth_seat_num` ,                    `pass_seat_num` ,                    `driving_license_picture` ,                    `fuel_type` ,                    `brand_model` ,                    `vehicle_status` ,                    `inspect_end_date` ,                    `check_date` ,                    `status` ,                    `verify_remark` ,                    `unaudit_reason` ,                    `unaudit_remark` ,                    `vehicle_picture_start_date` ,                    `vehicle_valid_period` ,                    `vehicle_picture` ,                    `create_user` ,                    `create_time` ,                    `update_user` ,                    `update_time`                               FROM         vehicle_info                  WHERE         1=1                                                                                   AND                              id=?                                                                                                                                                AND                              env like concat('%', ?,'%' )                                                                                                                                                AND                              vehicle_no like concat('%', ?,'%' )                                                                                                                                                AND                              driver_id=?                                                                                                                                                AND                              vehicle_no_city like concat('%', ?,'%' )                                                                                                                                                AND                              vehicle_type like concat('%', ?,'%' )                                                                                                                                                AND                              vehicle_owner like concat('%', ?,'%' )                                                                                                                                                AND                              use_character like concat('%', ?,'%' )                                                                                                                                                AND                              car_brand like concat('%', ?,'%' )                                                                                                                                                AND                              car_model like concat('%', ?,'%' )                                                                                                                                                AND                              vin like concat('%', ?,'%' )                                                                                                                                                AND                              color like concat('%', ?,'%' )                                                                                                                                                AND                              register_date like concat('%', ?,'%' )                                                                                                                                                AND                              engine_no like concat('%', ?,'%' )                                                                                                                                                AND                              issue_date like concat('%', ?,'%' )                                                                                                                                                AND                              auth_seat_num=?                                                                                                                                                AND                              pass_seat_num=?                                                                                                                                                AND                              driving_license_picture like concat('%', ?,'%' )                                                                                                                                                AND                              fuel_type like concat('%', ?,'%' )                                                                                                                                                AND                              brand_model like concat('%', ?,'%' )                                                                                                                                                AND                              vehicle_status like concat('%', ?,'%' )                                                                                                                                                AND                              inspect_end_date like concat('%', ?,'%' )                                                                                                                                                AND                              check_date like concat('%', ?,'%' )                                                                                                                                                AND                              status=?                                                                                                                                                AND                              verify_remark like concat('%', ?,'%' )                                                                                                                                                AND                              unaudit_reason=?                                                                                                                                                AND                              unaudit_remark like concat('%', ?,'%' )                                                                                                                                                AND                              vehicle_picture_start_date >= ?                                                            AND                              vehicle_picture_start_date <= ?                                                                                                                                                AND                              vehicle_valid_period=?                                                                                                                                                AND                              vehicle_picture like concat('%', ?,'%' )                                                                                                                                                AND                              create_user like concat('%', ?,'%' )                                                                                                                                                AND                              create_time >= ?                                                            AND                              create_time <= ?                                                                                                                                                AND                              update_user like concat('%', ?,'%' )                                                                                                                                                AND                              update_time >= ?                                                            AND                              update_time <= ?                                                                                           ORDER BY         id DESC</pre>
    */
    public List<VehicleInfoDO> queryAll(QueryAllQuery param) throws DataAccessException;

    /**
    * 
    * sql:
    *
    <pre>DELETE      FROM         vehicle_info                  WHERE         id = ?</pre>
    */
    public int delete(Long id) throws DataAccessException;

}



