/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.ly.travel.car.carowner.dal.dataobject;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;


import java.io.*;
import java.math.BigDecimal;
import java.net.*;
import java.util.*;


import com.ly.sof.utils.page.PageQuery;
import com.ly.sof.utils.page.PageList;
import com.ly.sof.utils.page.PageQueryUtils;
/**
* CommissionSettingDO
 * database table: commission_setting
 * database table comments: CommissionSetting
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 *
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to
 * directory <tt>(project-home)/dalgen</tt>
 *
 * <AUTHOR> Wang(Wang Junxiang)
 **/
@TableName("commission_setting")
public class CommissionSettingDO implements java.io.Serializable {
private static final long serialVersionUID = -5216457518046898601L;

    /**
    * 自增id        db_column: id
    */

        @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
    * 环境        db_column: env
    */

        @TableField(value = "env")
    private String env;
    /**
    * 佣金计算类型 1 比例 2 固定金额        db_column: type
    */

        @TableField(value = "type")
    private Integer type;
    /**
    * 佣金计算比例 包车        db_column: comm_rate_all
    */

        @TableField(value = "comm_rate_all")
    private BigDecimal commRateAll;
    /**
    * 佣金计算比例 拼车        db_column: comm_rate_join
    */

        @TableField(value = "comm_rate_join")
    private BigDecimal commRateJoin;
    /**
    * 佣金上限 包车        db_column: comm_limit_all
    */

        @TableField(value = "comm_limit_all")
    private BigDecimal commLimitAll;
    /**
    * 佣金上限 拼车        db_column: comm_limit_join
    */

        @TableField(value = "comm_limit_join")
    private BigDecimal commLimitJoin;
    /**
    * 创建人        db_column: create_user
    */

        @TableField(value = "create_user")
    private String createUser;
    /**
    * 创建时间        db_column: create_time
    */

        @TableField(value = "create_time")
    private Date createTime;
    /**
    * 修改人        db_column: update_user
    */

        @TableField(value = "update_user")
    private String updateUser;
    /**
    * 修改时间        db_column: update_time
    */

        @TableField(value = "update_time")
    private Date updateTime;


        public void setId(long id) {
        this.id = id;
        }

        public long getId() {
        return this.id;
        }

        public void setEnv(String env) {
        this.env = env;
        }

        public String getEnv() {
        return this.env;
        }

        public void setType(Integer type) {
        this.type = type;
        }

        public Integer getType() {
        return this.type;
        }

        public void setCommRateAll(BigDecimal commRateAll) {
        this.commRateAll = commRateAll;
        }

        public BigDecimal getCommRateAll() {
        return this.commRateAll;
        }

        public void setCommRateJoin(BigDecimal commRateJoin) {
        this.commRateJoin = commRateJoin;
        }

        public BigDecimal getCommRateJoin() {
        return this.commRateJoin;
        }

        public void setCommLimitAll(BigDecimal commLimitAll) {
        this.commLimitAll = commLimitAll;
        }

        public BigDecimal getCommLimitAll() {
        return this.commLimitAll;
        }

        public void setCommLimitJoin(BigDecimal commLimitJoin) {
        this.commLimitJoin = commLimitJoin;
        }

        public BigDecimal getCommLimitJoin() {
        return this.commLimitJoin;
        }

        public void setCreateUser(String createUser) {
        this.createUser = createUser;
        }

        public String getCreateUser() {
        return this.createUser;
        }

        public void setCreateTime(Date createTime) {
        this.createTime = createTime;
        }

        public Date getCreateTime() {
        return this.createTime;
        }

        public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        }

        public String getUpdateUser() {
        return this.updateUser;
        }

        public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        }

        public Date getUpdateTime() {
        return this.updateTime;
        }

public String toString() {
return new ToStringBuilder(this)
    .append("Id",getId())
    .append("Env",getEnv())
    .append("Type",getType())
    .append("CommRateAll",getCommRateAll())
    .append("CommRateJoin",getCommRateJoin())
    .append("CommLimitAll",getCommLimitAll())
    .append("CommLimitJoin",getCommLimitJoin())
    .append("CreateUser",getCreateUser())
    .append("CreateTime",getCreateTime())
    .append("UpdateUser",getUpdateUser())
    .append("UpdateTime",getUpdateTime())
.toString();
}

public int hashCode() {
return new HashCodeBuilder()
    .append(getId())
.toHashCode();
}

public boolean equals(Object obj) {
if(obj == null) return false;
if(this == obj) return true;
if(obj instanceof CommissionSettingDO == false) return false;
CommissionSettingDO other = (CommissionSettingDO)obj;
return new EqualsBuilder()
    .append(getId(),other.getId())
.isEquals();
}
}

