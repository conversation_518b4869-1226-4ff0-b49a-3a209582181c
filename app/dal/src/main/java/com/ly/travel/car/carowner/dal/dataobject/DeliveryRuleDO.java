/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.ly.travel.car.carowner.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * DeliveryRuleDO
 * database table: delivery_rules
 * database table comments: DeliveryRule
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * <p>
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to
 * directory <tt>(project-home)/dalgen</tt>
 *
 * <AUTHOR>
 **/
@TableName("delivery_rules")
public class DeliveryRuleDO implements Serializable {
    private static final long serialVersionUID = -5216457518046898601L;

    /**
     * 规则ID        db_column: id
     */

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 跨城订单不超过        db_column: cross_city_limit
     */

    @TableField(value = "cross_city_limit")
    private Integer crossCityLimit;
    /**
     * 市内订单不超过        db_column: city_limit
     */

    @TableField(value = "city_limit")
    private Integer cityLimit;
    /**
     * 总订单不超过        db_column: total_limit
     */

    @TableField(value = "total_limit")
    private Integer totalLimit;
    /**
     * 每日取消不超过        db_column: erverday_cancel_shold
     */

    @TableField(value = "erverday_cancel_shold")
    private Integer erverdayCancelShold;
    /**
     * 规则状态(0停用/1启用)        db_column: status
     */

    @TableField(value = "status")
    private Integer status;
    /**
     * 每日不可接单时间        db_column: everday_down
     */

    @TableField(value = "everday_down")
    private String everdayDown;
    /**
     * 每日不可接单时间        db_column: everday_up
     */

    @TableField(value = "everday_up")
    private String everdayUp;
    /**
     * 创建人        db_column: create_user
     */

    @TableField(value = "create_user")
    private String createUser;
    /**
     * 创建时间        db_column: create_time
     */

    @TableField(value = "create_time")
    private Date createTime;
    /**
     * 修改人        db_column: update_user
     */

    @TableField(value = "update_user")
    private String updateUser;
    /**
     * 修改时间        db_column: update_time
     */

    @TableField(value = "update_time")
    private Date updateTime;
    /**
     * 环境标识        db_column: env
     */

    @TableField(value = "env")
    private String env;


    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return this.id;
    }

    public void setCrossCityLimit(Integer crossCityLimit) {
        this.crossCityLimit = crossCityLimit;
    }

    public Integer getCrossCityLimit() {
        return this.crossCityLimit;
    }

    public void setCityLimit(Integer cityLimit) {
        this.cityLimit = cityLimit;
    }

    public Integer getCityLimit() {
        return this.cityLimit;
    }

    public void setTotalLimit(Integer totalLimit) {
        this.totalLimit = totalLimit;
    }

    public Integer getTotalLimit() {
        return this.totalLimit;
    }

    public void setErverdayCancelShold(Integer erverdayCancelShold) {
        this.erverdayCancelShold = erverdayCancelShold;
    }

    public Integer getErverdayCancelShold() {
        return this.erverdayCancelShold;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getStatus() {
        return this.status;
    }

    public void setEverdayDown(String everdayDown) {
        this.everdayDown = everdayDown;
    }

    public String getEverdayDown() {
        return this.everdayDown;
    }

    public void setEverdayUp(String everdayUp) {
        this.everdayUp = everdayUp;
    }

    public String getEverdayUp() {
        return this.everdayUp;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getCreateUser() {
        return this.createUser;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return this.createTime;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public String getUpdateUser() {
        return this.updateUser;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getUpdateTime() {
        return this.updateTime;
    }

    public void setEnv(String env) {
        this.env = env;
    }

    public String getEnv() {
        return this.env;
    }

    public String toString() {
        return new ToStringBuilder(this)
                .append("Id", getId())
                .append("CrossCityLimit", getCrossCityLimit())
                .append("CityLimit", getCityLimit())
                .append("TotalLimit", getTotalLimit())
                .append("ErverdayCancelShold", getErverdayCancelShold())
                .append("Status", getStatus())
                .append("EverdayDown", getEverdayDown())
                .append("EverdayUp", getEverdayUp())
                .append("CreateUser", getCreateUser())
                .append("CreateTime", getCreateTime())
                .append("UpdateUser", getUpdateUser())
                .append("UpdateTime", getUpdateTime())
                .append("Env", getEnv())
                .toString();
    }

    public int hashCode() {
        return new HashCodeBuilder()
                .append(getId())
                .toHashCode();
    }

    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (this == obj) return true;
        if (obj instanceof DeliveryRuleDO == false) return false;
        DeliveryRuleDO other = (DeliveryRuleDO) obj;
        return new EqualsBuilder()
                .append(getId(), other.getId())
                .isEquals();
    }
}

