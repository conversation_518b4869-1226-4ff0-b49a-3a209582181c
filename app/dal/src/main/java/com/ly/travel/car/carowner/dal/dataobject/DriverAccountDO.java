/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.ly.travel.car.carowner.dal.dataobject;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.util.*;

/**
 * DriverAccountDO
 * database table: driver_account
 * database table comments: DriverAccount
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * <p>
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to
 * directory <tt>(project-home)/dalgen</tt>
 *
 * <AUTHOR>
 **/
@TableName("driver_account")
public class DriverAccountDO implements java.io.Serializable {
    private static final long serialVersionUID = -5216457518046898601L;

    /**
     * 主键ID        db_column: id
     */

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 司机 ID        db_column: driver_id
     */

    @TableField(value = "driver_id")
    private Long driverId;

    /**
     * 总金额        db_column: total_amount
     */

    @TableField(value = "total_amount")
    private BigDecimal totalAmount;
    /**
     * 冻结金额        db_column: freeze_amount
     */

    @TableField(value = "freeze_amount")
    private BigDecimal freezeAmount;
    /**
     * 提现中金额        db_column: withdrawing_amount
     */

    @TableField(value = "withdrawing_amount")
    private BigDecimal withdrawingAmount;
    /**
     * 已提现金额        db_column: withdrawn_amount
     */

    @TableField(value = "withdrawn_amount")
    private BigDecimal withdrawnAmount;
    /**
     * 可用金额        db_column: available_amount
     */

    @TableField(value = "available_amount")
    private BigDecimal availableAmount;

    @TableField(value = "reward_amount")
    private BigDecimal rewardAmount;
    /**
     * 是否冻结 0-否 1-是        db_column: is_freeze
     */

    @TableField(value = "is_freeze")
    private Integer isFreeze;
    /**
     * 冻结时间        db_column: freeze_time
     */

    @TableField(value = "freeze_time")
    private Date freezeTime;
    /**
     * 解冻时间        db_column: unfreeze_time
     */

    @TableField(value = "unfreeze_time")
    private Date unfreezeTime;
    /**
     * 提现密码        db_column: withdrawal_password
     */

    @TableField(value = "withdrawal_password")
    private String withdrawalPassword;
    /**
     * 创建人        db_column: create_user
     */

    @TableField(value = "create_user")
    private String createUser;
    /**
     * 创建时间        db_column: create_time
     */

    @TableField(value = "create_time")
    private Date createTime;
    /**
     * 修改人        db_column: update_user
     */

    @TableField(value = "update_user")
    private String updateUser;
    /**
     * 修改时间        db_column: update_time
     */

    @TableField(value = "update_time")
    private Date updateTime;


    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return this.id;
    }

    public void setDriverId(Long driverId) {
        this.driverId = driverId;
    }

    public Long getDriverId() {
        return this.driverId;
    }
    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getTotalAmount() {
        return this.totalAmount;
    }

    public void setFreezeAmount(BigDecimal freezeAmount) {
        this.freezeAmount = freezeAmount;
    }

    public BigDecimal getFreezeAmount() {
        return this.freezeAmount;
    }

    public void setWithdrawingAmount(BigDecimal withdrawingAmount) {
        this.withdrawingAmount = withdrawingAmount;
    }

    public BigDecimal getWithdrawingAmount() {
        return this.withdrawingAmount;
    }

    public void setWithdrawnAmount(BigDecimal withdrawnAmount) {
        this.withdrawnAmount = withdrawnAmount;
    }

    public BigDecimal getWithdrawnAmount() {
        return this.withdrawnAmount;
    }

    public void setAvailableAmount(BigDecimal availableAmount) {
        this.availableAmount = availableAmount;
    }

    public BigDecimal getAvailableAmount() {
        return this.availableAmount;
    }

    public void setIsFreeze(Integer isFreeze) {
        this.isFreeze = isFreeze;
    }

    public Integer getIsFreeze() {
        return this.isFreeze;
    }

    public void setFreezeTime(Date freezeTime) {
        this.freezeTime = freezeTime;
    }

    public Date getFreezeTime() {
        return this.freezeTime;
    }

    public void setUnfreezeTime(Date unfreezeTime) {
        this.unfreezeTime = unfreezeTime;
    }

    public Date getUnfreezeTime() {
        return this.unfreezeTime;
    }

    public void setWithdrawalPassword(String withdrawalPassword) {
        this.withdrawalPassword = withdrawalPassword;
    }

    public String getWithdrawalPassword() {
        return this.withdrawalPassword;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getCreateUser() {
        return this.createUser;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return this.createTime;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public String getUpdateUser() {
        return this.updateUser;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getUpdateTime() {
        return this.updateTime;
    }

    public BigDecimal getRewardAmount() {
        return rewardAmount;
    }

    public void setRewardAmount(BigDecimal rewardAmount) {
        this.rewardAmount = rewardAmount;
    }

    public String toString() {
        return new ToStringBuilder(this)
                .append("Id", getId())
                .append("DriverId", getDriverId())
                .append("TotalAmount", getTotalAmount())
                .append("FreezeAmount", getFreezeAmount())
                .append("WithdrawingAmount", getWithdrawingAmount())
                .append("WithdrawnAmount", getWithdrawnAmount())
                .append("AvailableAmount", getAvailableAmount())
                .append("IsFreeze", getIsFreeze())
                .append("FreezeTime", getFreezeTime())
                .append("UnfreezeTime", getUnfreezeTime())
                .append("WithdrawalPassword", getWithdrawalPassword())
                .append("CreateUser", getCreateUser())
                .append("CreateTime", getCreateTime())
                .append("UpdateUser", getUpdateUser())
                .append("UpdateTime", getUpdateTime())
                .toString();
    }

    public int hashCode() {
        return new HashCodeBuilder()
                .append(getId())
                .toHashCode();
    }

    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (this == obj) return true;
        if (obj instanceof DriverAccountDO == false) return false;
        DriverAccountDO other = (DriverAccountDO) obj;
        return new EqualsBuilder()
                .append(getId(), other.getId())
                .isEquals();
    }
}

