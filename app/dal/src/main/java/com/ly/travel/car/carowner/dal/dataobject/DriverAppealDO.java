/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.ly.travel.car.carowner.dal.dataobject;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.util.*;

/**
 * DriverAppealDO
 * database table: driver_appeal
 * database table comments: DriverAppeal
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * <p>
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to
 * directory <tt>(project-home)/dalgen</tt>
 *
 * <AUTHOR>
 **/
@TableName("driver_appeal")
public class DriverAppealDO implements java.io.Serializable {
    private static final long serialVersionUID = -5216457518046898601L;

    /**
     * 主键ID        db_column: id
     */

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 环境标识        db_column: env
     */

    @TableField(value = "env")
    private String env;
    /**
     * 申诉编号        db_column: appeal_no
     */

    @TableField(value = "appeal_no")
    private String appealNo;
    /**
     * 司机ID        db_column: driver_id
     */

    @TableField(value = "driver_id")
    private Long driverId;
    /**
     * 判定时间        db_column: arbitration_time
     */

    @TableField(value = "arbitration_time")
    private Date arbitrationTime;
    /**
     * 金额        db_column: appeal_amount
     */

    @TableField(value = "appeal_amount")
    private BigDecimal appealAmount;
    /**
     * 申诉内容        db_column: appeal_content
     */

    @TableField(value = "appeal_content")
    private String appealContent;
    /**
     * 备注        db_column: remark
     */

    @TableField(value = "remark")
    private String remark;
    /**
     * 创建人        db_column: create_user
     */

    @TableField(value = "create_user")
    private String createUser;
    /**
     * 创建时间        db_column: create_time
     */

    @TableField(value = "create_time")
    private Date createTime;
    /**
     * 订单号        db_column: order_no
     */

    @TableField(value = "order_no")
    private String orderNo;


    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return this.id;
    }

    public void setEnv(String env) {
        this.env = env;
    }

    public String getEnv() {
        return this.env;
    }

    public void setAppealNo(String appealNo) {
        this.appealNo = appealNo;
    }

    public String getAppealNo() {
        return this.appealNo;
    }

    public void setDriverId(Long driverId) {
        this.driverId = driverId;
    }

    public Long getDriverId() {
        return this.driverId;
    }

    public void setArbitrationTime(Date arbitrationTime) {
        this.arbitrationTime = arbitrationTime;
    }

    public Date getArbitrationTime() {
        return this.arbitrationTime;
    }

    public void setAppealAmount(BigDecimal appealAmount) {
        this.appealAmount = appealAmount;
    }

    public BigDecimal getAppealAmount() {
        return this.appealAmount;
    }

    public void setAppealContent(String appealContent) {
        this.appealContent = appealContent;
    }

    public String getAppealContent() {
        return this.appealContent;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getRemark() {
        return this.remark;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getCreateUser() {
        return this.createUser;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return this.createTime;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOrderNo() {
        return this.orderNo;
    }

    public String toString() {
        return new ToStringBuilder(this)
                .append("Id", getId())
                .append("Env", getEnv())
                .append("AppealNo", getAppealNo())
                .append("DriverId", getDriverId())
                .append("ArbitrationTime", getArbitrationTime())
                .append("AppealAmount", getAppealAmount())
                .append("AppealContent", getAppealContent())
                .append("Remark", getRemark())
                .append("CreateUser", getCreateUser())
                .append("CreateTime", getCreateTime())
                .append("OrderNo", getOrderNo())
                .toString();
    }

    public int hashCode() {
        return new HashCodeBuilder()
                .append(getId())
                .toHashCode();
    }

    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (this == obj) return true;
        if (obj instanceof DriverAppealDO == false) return false;
        DriverAppealDO other = (DriverAppealDO) obj;
        return new EqualsBuilder()
                .append(getId(), other.getId())
                .isEquals();
    }
}

