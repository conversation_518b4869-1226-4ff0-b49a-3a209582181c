/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.ly.travel.car.carowner.dal.dataobject;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.util.*;

/**
 * DriverBankCardDO
 * database table: driver_bank_card
 * database table comments: DriverBankCard
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * <p>
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to
 * directory <tt>(project-home)/dalgen</tt>
 *
 * <AUTHOR>
 **/
@TableName("driver_bank_card")
public class DriverBankCardDO implements java.io.Serializable {
    private static final long serialVersionUID = -5216457518046898601L;

    /**
     * 主键        db_column: id
     */

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 环境标识        db_column: env
     */

    @TableField(value = "env")
    private String env;
    /**
     * 司机 ID        db_column: driver_id
     */

    @TableField(value = "driver_id")
    private Long driverId;
    /**
     * 持卡人姓名        db_column: owner_name
     */

    @TableField(value = "owner_name")
    private String ownerName;
    /**
     * 持卡人身份证号        db_column: owner_id_card
     */

    @TableField(value = "owner_id_card")
    private String ownerIdCard;
    /**
     * 持卡人银行卡号        db_column: bank_card_no
     */

    @TableField(value = "bank_card_no")
    private String bankCardNo;
    /**
     * 卡类型：DEBIT:借记卡 CREDIT:贷记卡 CORPORATE:对公银行账户        db_column: bank_card_type
     */

    @TableField(value = "bank_card_type")
    private String bankCardType;
    /**
     * 银行卡code        db_column: bank_card_code
     */

    @TableField(value = "bank_card_code")
    private String bankCardCode;
    /**
     * 储蓄行ID        db_column: deposit_bank_id
     */

    @TableField(value = "deposit_bank_id")
    private Long depositBankId;
    /**
     * 持卡人预留手机号        db_column: owner_mobile
     */

    @TableField(value = "owner_mobile")
    private String ownerMobile;
    /**
     * 状态 0-无效 1-有效        db_column: status
     */

    @TableField(value = "status")
    private Integer status;
    /**
     * 创建人        db_column: create_user
     */

    @TableField(value = "create_user")
    private String createUser;
    /**
     * 创建时间        db_column: create_time
     */

    @TableField(value = "create_time")
    private Date createTime;
    /**
     * 修改人        db_column: update_user
     */

    @TableField(value = "update_user")
    private String updateUser;
    /**
     * 修改时间        db_column: update_time
     */

    @TableField(value = "update_time")
    private Date updateTime;


    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return this.id;
    }

    public void setEnv(String env) {
        this.env = env;
    }

    public String getEnv() {
        return this.env;
    }

    public void setDriverId(Long driverId) {
        this.driverId = driverId;
    }

    public Long getDriverId() {
        return this.driverId;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getOwnerName() {
        return this.ownerName;
    }

    public void setOwnerIdCard(String ownerIdCard) {
        this.ownerIdCard = ownerIdCard;
    }

    public String getOwnerIdCard() {
        return this.ownerIdCard;
    }

    public void setBankCardNo(String bankCardNo) {
        this.bankCardNo = bankCardNo;
    }

    public String getBankCardNo() {
        return this.bankCardNo;
    }

    public void setBankCardType(String bankCardType) {
        this.bankCardType = bankCardType;
    }

    public String getBankCardType() {
        return this.bankCardType;
    }

    public void setBankCardCode(String bankCardCode) {
        this.bankCardCode = bankCardCode;
    }

    public String getBankCardCode() {
        return this.bankCardCode;
    }

    public void setDepositBankId(Long depositBankId) {
        this.depositBankId = depositBankId;
    }

    public Long getDepositBankId() {
        return this.depositBankId;
    }

    public void setOwnerMobile(String ownerMobile) {
        this.ownerMobile = ownerMobile;
    }

    public String getOwnerMobile() {
        return this.ownerMobile;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getStatus() {
        return this.status;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getCreateUser() {
        return this.createUser;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return this.createTime;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public String getUpdateUser() {
        return this.updateUser;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getUpdateTime() {
        return this.updateTime;
    }

    public String toString() {
        return new ToStringBuilder(this)
                .append("Id", getId())
                .append("Env", getEnv())
                .append("DriverId", getDriverId())
                .append("OwnerName", getOwnerName())
                .append("OwnerIdCard", getOwnerIdCard())
                .append("BankCardNo", getBankCardNo())
                .append("BankCardType", getBankCardType())
                .append("BankCardCode", getBankCardCode())
                .append("DepositBankId", getDepositBankId())
                .append("OwnerMobile", getOwnerMobile())
                .append("Status", getStatus())
                .append("CreateUser", getCreateUser())
                .append("CreateTime", getCreateTime())
                .append("UpdateUser", getUpdateUser())
                .append("UpdateTime", getUpdateTime())
                .toString();
    }

    public int hashCode() {
        return new HashCodeBuilder()
                .append(getId())
                .toHashCode();
    }

    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (this == obj) return true;
        if (obj instanceof DriverBankCardDO == false) return false;
        DriverBankCardDO other = (DriverBankCardDO) obj;
        return new EqualsBuilder()
                .append(getId(), other.getId())
                .isEquals();
    }
}

