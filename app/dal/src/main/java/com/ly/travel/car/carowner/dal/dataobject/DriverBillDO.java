/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.ly.travel.car.carowner.dal.dataobject;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.util.*;

/**
 * DriverBillDO
 * database table: driver_bill
 * database table comments: DriverBill
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * <p>
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to
 * directory <tt>(project-home)/dalgen</tt>
 *
 * <AUTHOR>
 **/
@TableName("driver_bill")
public class DriverBillDO implements java.io.Serializable {
    private static final long serialVersionUID = -5216457518046898601L;

    /**
     * 主键ID        db_column: id
     */

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 流水号        db_column: bill_no
     */

    @TableField(value = "bill_no")
    private String billNo;
    /**
     * 环境标识        db_column: env
     */

    @TableField(value = "env")
    private String env;
    /**
     * 司机账户ID        db_column: driver_id
     */

    @TableField(value = "driver_id")
    private Long driverId;
    /**
     * 业务单据号        db_column: voucher_no
     */

    @TableField(value = "voucher_no")
    private String voucherNo;
    /**
     * 业务凭证单据类型枚举 1-订单 2-提现申请 3-调账 4-提现失败回冲        db_column: voucher_type
     */

    @TableField(value = "voucher_type")
    private Integer voucherType;
    /**
     * 明细大类 1-收入 2-支出        db_column: bill_type
     */

    @TableField(value = "bill_type")
    private Integer billType;
    /**
     * 金额        db_column: amount
     */

    @TableField(value = "amount")
    private BigDecimal amount;
    /**
     * 是否冻结 0-否 1-是        db_column: is_freeze
     */

    @TableField(value = "is_freeze")
    private Integer isFreeze;
    /**
     * 备注        db_column: remark
     */

    @TableField(value = "remark")
    private String remark;
    /**
     * 创建时间        db_column: create_time
     */

    @TableField(value = "create_time")
    private Date createTime;
    /**
     * 创建人        db_column: create_user
     */

    @TableField(value = "create_user")
    private String createUser;
    /**
     * 修改时间        db_column: update_time
     */

    @TableField(value = "update_time")
    private Date updateTime;
    /**
     * 修改人        db_column: update_user
     */

    @TableField(value = "update_user")
    private String updateUser;
    /**
     * 提现单号        db_column: withdraw_no
     */

    @TableField(value = "withdraw_no")
    private String withdrawNo;
    /**
     * 解冻时间        db_column: unfreeze_time
     */

    @TableField(value = "unfreeze_time")
    private Date unfreezeTime;
    /**
     * 冻结类型        db_column: freeze_type
     */

    @TableField(value = "freeze_type")
    private Integer freezeType;


    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return this.id;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    public String getBillNo() {
        return this.billNo;
    }

    public void setEnv(String env) {
        this.env = env;
    }

    public String getEnv() {
        return this.env;
    }

    public void setDriverId(Long driverId) {
        this.driverId = driverId;
    }

    public Long getDriverId() {
        return this.driverId;
    }

    public void setVoucherNo(String voucherNo) {
        this.voucherNo = voucherNo;
    }

    public String getVoucherNo() {
        return this.voucherNo;
    }

    public void setVoucherType(Integer voucherType) {
        this.voucherType = voucherType;
    }

    public Integer getVoucherType() {
        return this.voucherType;
    }

    public void setBillType(Integer billType) {
        this.billType = billType;
    }

    public Integer getBillType() {
        return this.billType;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getAmount() {
        return this.amount;
    }

    public void setIsFreeze(Integer isFreeze) {
        this.isFreeze = isFreeze;
    }

    public Integer getIsFreeze() {
        return this.isFreeze;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getRemark() {
        return this.remark;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return this.createTime;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getCreateUser() {
        return this.createUser;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getUpdateTime() {
        return this.updateTime;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public String getUpdateUser() {
        return this.updateUser;
    }

    public void setWithdrawNo(String withdrawNo) {
        this.withdrawNo = withdrawNo;
    }

    public String getWithdrawNo() {
        return this.withdrawNo;
    }

    public void setUnfreezeTime(Date unfreezeTime) {
        this.unfreezeTime = unfreezeTime;
    }

    public Date getUnfreezeTime() {
        return this.unfreezeTime;
    }

    public void setFreezeType(Integer freezeType) {
        this.freezeType = freezeType;
    }

    public Integer getFreezeType() {
        return this.freezeType;
    }

    public String toString() {
        return new ToStringBuilder(this)
                .append("Id", getId())
                .append("BillNo", getBillNo())
                .append("Env", getEnv())
                .append("DriverId", getDriverId())
                .append("VoucherNo", getVoucherNo())
                .append("VoucherType", getVoucherType())
                .append("BillType", getBillType())
                .append("Amount", getAmount())
                .append("IsFreeze", getIsFreeze())
                .append("Remark", getRemark())
                .append("CreateTime", getCreateTime())
                .append("CreateUser", getCreateUser())
                .append("UpdateTime", getUpdateTime())
                .append("UpdateUser", getUpdateUser())
                .append("WithdrawNo", getWithdrawNo())
                .append("UnfreezeTime", getUnfreezeTime())
                .append("FreezeType", getFreezeType())
                .toString();
    }

    public int hashCode() {
        return new HashCodeBuilder()
                .append(getId())
                .toHashCode();
    }

    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (this == obj) return true;
        if (obj instanceof DriverBillDO == false) return false;
        DriverBillDO other = (DriverBillDO) obj;
        return new EqualsBuilder()
                .append(getId(), other.getId())
                .isEquals();
    }
}

