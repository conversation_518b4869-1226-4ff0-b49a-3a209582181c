/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.ly.travel.car.carowner.dal.dataobject;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.util.*;

/**
 * DriverBillFreezeDO
 * database table: driver_bill_freeze
 * database table comments: DriverBillFreeze
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * <p>
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to
 * directory <tt>(project-home)/dalgen</tt>
 *
 * <AUTHOR>
 **/
@TableName("driver_bill_freeze")
public class DriverBillFreezeDO implements java.io.Serializable {
    private static final long serialVersionUID = -5216457518046898601L;

    /**
     * 主键ID        db_column: id
     */

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 流水号        db_column: bill_no
     */

    @TableField(value = "bill_no")
    private String billNo;
    /**
     * 环境标识        db_column: env
     */

    @TableField(value = "env")
    private String env;
    /**
     * 司机账户ID        db_column: driver_id
     */

    @TableField(value = "driver_id")
    private Long driverId;
    /**
     * 是否已解冻 0未解冻 1已解冻        db_column: unfreeze_flag
     */

    @TableField(value = "unfreeze_flag")
    private Integer unfreezeFlag;
    /**
     * 备注        db_column: freeze_remark
     */

    @TableField(value = "freeze_remark")
    private String freezeRemark;
    /**
     * 创建时间        db_column: create_time
     */

    @TableField(value = "create_time")
    private Date createTime;
    /**
     * 创建人        db_column: create_user
     */

    @TableField(value = "create_user")
    private String createUser;
    /**
     * 修改时间        db_column: update_time
     */

    @TableField(value = "update_time")
    private Date updateTime;
    /**
     * 修改人        db_column: update_user
     */

    @TableField(value = "update_user")
    private String updateUser;
    /**
     * 解冻时间        db_column: unfreeze_time
     */

    @TableField(value = "unfreeze_time")
    private Date unfreezeTime;
    /**
     * 冻结类型        db_column: freeze_type
     */

    @TableField(value = "freeze_type")
    private Integer freezeType;

    /**
     * 冻结时间        db_column: freeze_time
     */

    @TableField(value = "freeze_time")
    private Date freezeTime;


    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return this.id;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    public String getBillNo() {
        return this.billNo;
    }

    public void setEnv(String env) {
        this.env = env;
    }

    public String getEnv() {
        return this.env;
    }

    public void setDriverId(Long driverId) {
        this.driverId = driverId;
    }

    public Long getDriverId() {
        return this.driverId;
    }

    public void setUnfreezeFlag(Integer unfreezeFlag) {
        this.unfreezeFlag = unfreezeFlag;
    }

    public Integer getUnfreezeFlag() {
        return this.unfreezeFlag;
    }

    public void setFreezeRemark(String freezeRemark) {
        this.freezeRemark = freezeRemark;
    }

    public String getFreezeRemark() {
        return this.freezeRemark;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return this.createTime;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getCreateUser() {
        return this.createUser;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getUpdateTime() {
        return this.updateTime;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public String getUpdateUser() {
        return this.updateUser;
    }

    public void setUnfreezeTime(Date unfreezeTime) {
        this.unfreezeTime = unfreezeTime;
    }

    public Date getUnfreezeTime() {
        return this.unfreezeTime;
    }

    public void setFreezeType(Integer freezeType) {
        this.freezeType = freezeType;
    }

    public Integer getFreezeType() {
        return this.freezeType;
    }

    public Date getFreezeTime() {
        return freezeTime;
    }

    public void setFreezeTime(Date freezeTime) {
        this.freezeTime = freezeTime;
    }

    public String toString() {
        return new ToStringBuilder(this)
                .append("Id", getId())
                .append("BillNo", getBillNo())
                .append("Env", getEnv())
                .append("DriverId", getDriverId())
                .append("UnfreezeFlag", getUnfreezeFlag())
                .append("FreezeRemark", getFreezeRemark())
                .append("CreateTime", getCreateTime())
                .append("CreateUser", getCreateUser())
                .append("UpdateTime", getUpdateTime())
                .append("UpdateUser", getUpdateUser())
                .append("UnfreezeTime", getUnfreezeTime())
                .append("FreezeType", getFreezeType())
                .append("FreezeTime", getFreezeTime())
                .toString();
    }

    public int hashCode() {
        return new HashCodeBuilder()
                .append(getId())
                .toHashCode();
    }

    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (this == obj) return true;
        if (obj instanceof DriverBillFreezeDO == false) return false;
        DriverBillFreezeDO other = (DriverBillFreezeDO) obj;
        return new EqualsBuilder()
                .append(getId(), other.getId())
                .isEquals();
    }
}

