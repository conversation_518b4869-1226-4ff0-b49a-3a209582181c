/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.ly.travel.car.carowner.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.Date;

/**
* DriverInfoDO
 * database table: driver_info
 * database table comments: DriverInfo
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 *
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to
 * directory <tt>(project-home)/dalgen</tt>
 *
 * <AUTHOR>
 **/
@TableName("driver_info")
public class DriverInfoDO implements Serializable {
private static final long serialVersionUID = -5216457518046898601L;

    /**
    * 自增id        db_column: id
    */

        @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
    * 环境        db_column: env
    */

        @TableField(value = "env")
    private String env;
    /**
    * 联系电话        db_column: mobile
    */

        @TableField(value = "mobile")
    private String mobile;
    /**
     * 手机号对应openId        db_column: open_id
     */

    @TableField(value = "open_id")
    private String openId;
    /**
    * 姓名        db_column: name
    */

        @TableField(value = "name")
    private String name;
    /**
    * 性别 男 女        db_column: sex
    */

        @TableField(value = "sex")
    private String sex;
    /**
    * 民族        db_column: nation
    */

        @TableField(value = "nation")
    private String nation;
    /**
    * 出生日期        db_column: birth_day
    */

        @TableField(value = "birth_day")
    private String birthDay;
    /**
    * 身份证号        db_column: id_card
    */

        @TableField(value = "id_card")
    private String idCard;
    /**
    * 身份证有效期起        db_column: id_card_start_date
    */

        @TableField(value = "id_card_start_date")
    private String idCardStartDate;
    /**
    * 身份证有效期止        db_column: id_card_end_date
    */

        @TableField(value = "id_card_end_date")
    private String idCardEndDate;
    /**
    * 身份证签发机关        db_column: id_card_sign_organ
    */

        @TableField(value = "id_card_sign_organ")
    private String idCardSignOrgan;
    /**
    * 身份证地址        db_column: id_card_address
    */

        @TableField(value = "id_card_address")
    private String idCardAddress;
    /**
    * 身份证正面图片        db_column: id_card_front_picture
    */

        @TableField(value = "id_card_front_picture")
    private String idCardFrontPicture;
    /**
    * 身份证背面图片        db_column: id_card_back_picture
    */

        @TableField(value = "id_card_back_picture")
    private String idCardBackPicture;
    /**
    * 驾驶证初次领证日期        db_column: drive_first_issue_date
    */

        @TableField(value = "drive_first_issue_date")
    private String driveFirstIssueDate;
    /**
    * 驾驶证号        db_column: drive_id_card
    */

        @TableField(value = "drive_id_card")
    private String driveIdCard;
    /**
    * 驾驶证姓名        db_column: drive_name
    */

        @TableField(value = "drive_name")
    private String driveName;
    /**
    * 准驾车型 C1 C2 C3 C4 B1 B2 A1 A2 A3        db_column: drive_model
    */

        @TableField(value = "drive_model")
    private String driveModel;
    /**
    * 驾驶证有效期起        db_column: drive_start_date
    */

        @TableField(value = "drive_start_date")
    private String driveStartDate;
    /**
    * 驾驶证有效期止        db_column: drive_end_date
    */

        @TableField(value = "drive_end_date")
    private String driveEndDate;
    /**
    * 驾驶证图片        db_column: drive_license_picture
    */

        @TableField(value = "drive_license_picture")
    private String driveLicensePicture;
    /**
    * 最近完单时间        db_column: latest_complete_date
    */

        @TableField(value = "latest_complete_date")
    private Date latestCompleteDate;
    /**
     * 车牌号        db_column: vehicle_no
     */

    @TableField(value = "vehicle_no")
    private String vehicleNo;
    /**
    * 状态 1.未认证 2.待系统认证 3.待人工认证 4.认证通过 5.认证不通过        db_column: status
    */

        @TableField(value = "status")
    private Integer status;
    /**
     * 风控ocr不通过原因        db_column: verify_remark
     */

    @TableField(value = "verify_remark")
    private String verifyRemark;
    /**
    * 审核不通过原因 1.证件照片不清晰 2.证件照片作假 3.驾驶证已过期 4.准驾车型不符 5.驾龄不满足要求 6.驾驶证状态异常 7.其他        db_column: unaudit_reason
    */

        @TableField(value = "unaudit_reason")
    private Integer unauditReason;
    /**
    * 审核不通过其他原因        db_column: unaudit_remark
    */

        @TableField(value = "unaudit_remark")
    private String unauditRemark;
    /**
    * 封禁状态 0.未封禁 1.已封禁 与status合并使用        db_column: ban_status
    */

        @TableField(value = "ban_status")
    private Integer banStatus;
    /**
    * 封禁原因        db_column: ban_reason
    */

        @TableField(value = "ban_reason")
    private String banReason;
    /**
    * 人脸识别时间        db_column: face_date
    */

        @TableField(value = "face_date")
    private Date faceDate;
    /**
    * 人脸识别有效时长        db_column: face_valid_period
    */

        @TableField(value = "face_valid_period")
    private Integer faceValidPeriod;
    /**
    * 创建人        db_column: create_user
    */

        @TableField(value = "create_user")
    private String createUser;
    /**
    * 创建时间        db_column: create_time
    */

        @TableField(value = "create_time")
    private Date createTime;
    /**
    * 修改人        db_column: update_user
    */

        @TableField(value = "update_user")
    private String updateUser;
    /**
    * 修改时间        db_column: update_time
    */

        @TableField(value = "update_time")
    private Date updateTime;

    @TableField(value = "public_open_id")
        private String publicOpenId;


        public void setId(Long id) {
        this.id = id;
        }

        public Long getId() {
        return this.id;
        }

        public void setEnv(String env) {
        this.env = env;
        }

        public String getEnv() {
        return this.env;
        }

        public void setMobile(String mobile) {
        this.mobile = mobile;
        }

        public String getMobile() {
        return this.mobile;
        }

        public String getOpenId() {
            return openId;
        }

        public void setOpenId(String openId) {
            this.openId = openId;
        }

    public void setName(String name) {
        this.name = name;
        }

        public String getName() {
        return this.name;
        }

        public void setSex(String sex) {
        this.sex = sex;
        }

        public String getSex() {
        return this.sex;
        }

        public void setNation(String nation) {
        this.nation = nation;
        }

        public String getNation() {
        return this.nation;
        }

        public void setBirthDay(String birthDay) {
        this.birthDay = birthDay;
        }

        public String getBirthDay() {
        return this.birthDay;
        }

        public void setIdCard(String idCard) {
        this.idCard = idCard;
        }

        public String getIdCard() {
        return this.idCard;
        }

        public void setIdCardStartDate(String idCardStartDate) {
        this.idCardStartDate = idCardStartDate;
        }

        public String getIdCardStartDate() {
        return this.idCardStartDate;
        }

        public void setIdCardEndDate(String idCardEndDate) {
        this.idCardEndDate = idCardEndDate;
        }

        public String getIdCardEndDate() {
        return this.idCardEndDate;
        }

        public void setIdCardSignOrgan(String idCardSignOrgan) {
        this.idCardSignOrgan = idCardSignOrgan;
        }

        public String getIdCardSignOrgan() {
        return this.idCardSignOrgan;
        }

        public void setIdCardAddress(String idCardAddress) {
        this.idCardAddress = idCardAddress;
        }

        public String getIdCardAddress() {
        return this.idCardAddress;
        }

        public void setIdCardFrontPicture(String idCardFrontPicture) {
        this.idCardFrontPicture = idCardFrontPicture;
        }

        public String getIdCardFrontPicture() {
        return this.idCardFrontPicture;
        }

        public void setIdCardBackPicture(String idCardBackPicture) {
        this.idCardBackPicture = idCardBackPicture;
        }

        public String getIdCardBackPicture() {
        return this.idCardBackPicture;
        }

        public void setDriveFirstIssueDate(String driveFirstIssueDate) {
        this.driveFirstIssueDate = driveFirstIssueDate;
        }

        public String getDriveFirstIssueDate() {
        return this.driveFirstIssueDate;
        }

        public void setDriveIdCard(String driveIdCard) {
        this.driveIdCard = driveIdCard;
        }

        public String getDriveIdCard() {
        return this.driveIdCard;
        }

        public void setDriveName(String driveName) {
        this.driveName = driveName;
        }

        public String getDriveName() {
        return this.driveName;
        }

        public void setDriveModel(String driveModel) {
        this.driveModel = driveModel;
        }

        public String getDriveModel() {
        return this.driveModel;
        }

        public void setDriveStartDate(String driveStartDate) {
        this.driveStartDate = driveStartDate;
        }

        public String getDriveStartDate() {
        return this.driveStartDate;
        }

        public void setDriveEndDate(String driveEndDate) {
        this.driveEndDate = driveEndDate;
        }

        public String getDriveEndDate() {
        return this.driveEndDate;
        }

        public void setDriveLicensePicture(String driveLicensePicture) {
        this.driveLicensePicture = driveLicensePicture;
        }

        public String getDriveLicensePicture() {
        return this.driveLicensePicture;
        }

        public void setLatestCompleteDate(Date latestCompleteDate) {
        this.latestCompleteDate = latestCompleteDate;
        }

        public Date getLatestCompleteDate() {
        return this.latestCompleteDate;
        }

        public String getVehicleNo() {
            return vehicleNo;
        }

        public void setVehicleNo(String vehicleNo) {
            this.vehicleNo = vehicleNo;
        }

    public void setStatus(Integer status) {
        this.status = status;
        }

        public Integer getStatus() {
        return this.status;
        }

    public String getVerifyRemark() {
        return verifyRemark;
    }

    public void setVerifyRemark(String verifyRemark) {
        this.verifyRemark = verifyRemark;
    }

    public void setUnauditReason(Integer unauditReason) {
        this.unauditReason = unauditReason;
        }

        public Integer getUnauditReason() {
        return this.unauditReason;
        }

        public void setUnauditRemark(String unauditRemark) {
        this.unauditRemark = unauditRemark;
        }

        public String getUnauditRemark() {
        return this.unauditRemark;
        }

        public void setBanStatus(Integer banStatus) {
        this.banStatus = banStatus;
        }

        public Integer getBanStatus() {
        return this.banStatus;
        }

        public void setBanReason(String banReason) {
        this.banReason = banReason;
        }

        public String getBanReason() {
        return this.banReason;
        }

        public void setFaceDate(Date faceDate) {
        this.faceDate = faceDate;
        }

        public Date getFaceDate() {
        return this.faceDate;
        }

        public void setFaceValidPeriod(Integer faceValidPeriod) {
        this.faceValidPeriod = faceValidPeriod;
        }

        public Integer getFaceValidPeriod() {
        return this.faceValidPeriod;
        }

        public void setCreateUser(String createUser) {
        this.createUser = createUser;
        }

        public String getCreateUser() {
        return this.createUser;
        }

        public void setCreateTime(Date createTime) {
        this.createTime = createTime;
        }

        public Date getCreateTime() {
        return this.createTime;
        }

        public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        }

        public String getUpdateUser() {
        return this.updateUser;
        }

        public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        }

        public Date getUpdateTime() {
        return this.updateTime;
        }

    public String getPublicOpenId() {
        return publicOpenId;
    }

    public void setPublicOpenId(String publicOpenId) {
        this.publicOpenId = publicOpenId;
    }

    public String toString() {
return new ToStringBuilder(this)
    .append("Id",getId())
    .append("Env",getEnv())
    .append("Mobile",getMobile())
    .append("Name",getName())
    .append("Sex",getSex())
    .append("Nation",getNation())
    .append("BirthDay",getBirthDay())
    .append("IdCard",getIdCard())
    .append("IdCardStartDate",getIdCardStartDate())
    .append("IdCardEndDate",getIdCardEndDate())
    .append("IdCardSignOrgan",getIdCardSignOrgan())
    .append("IdCardAddress",getIdCardAddress())
    .append("IdCardFrontPicture",getIdCardFrontPicture())
    .append("IdCardBackPicture",getIdCardBackPicture())
    .append("DriveFirstIssueDate",getDriveFirstIssueDate())
    .append("DriveIdCard",getDriveIdCard())
    .append("DriveName",getDriveName())
    .append("DriveModel",getDriveModel())
    .append("DriveStartDate",getDriveStartDate())
    .append("DriveEndDate",getDriveEndDate())
    .append("DriveLicensePicture",getDriveLicensePicture())
    .append("LatestCompleteDate",getLatestCompleteDate())
    .append("Status",getStatus())
        .append("VerifyRemark",getVerifyRemark())
    .append("UnauditReason",getUnauditReason())
    .append("UnauditRemark",getUnauditRemark())
    .append("BanStatus",getBanStatus())
    .append("BanReason",getBanReason())
    .append("FaceDate",getFaceDate())
    .append("FaceValidPeriod",getFaceValidPeriod())
    .append("CreateUser",getCreateUser())
    .append("CreateTime",getCreateTime())
    .append("UpdateUser",getUpdateUser())
    .append("UpdateTime",getUpdateTime())
    .append("PublicOpenId",getPublicOpenId())
.toString();
}

public int hashCode() {
return new HashCodeBuilder()
    .append(getId())
.toHashCode();
}

public boolean equals(Object obj) {
if(obj == null) return false;
if(this == obj) return true;
if(obj instanceof DriverInfoDO == false) return false;
DriverInfoDO other = (DriverInfoDO)obj;
return new EqualsBuilder()
    .append(getId(),other.getId())
.isEquals();
}
}

