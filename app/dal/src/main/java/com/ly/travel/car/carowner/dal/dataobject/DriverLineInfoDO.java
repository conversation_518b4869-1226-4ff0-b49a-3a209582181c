package com.ly.travel.car.carowner.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 司机行程订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2025/07/30 13:54
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("driver_line_info")
public class DriverLineInfoDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 环境
     */
    @TableField(value = "env", fill = FieldFill.INSERT)
    private String env;

    /**
     * 行程线路no
     */
    @TableField("driver_line_no")
    private String driverLineNo;

    /**
     * 线路名称
     */
    @TableField("driver_line_name")
    private String driverLineName;

    /**
     * 司机id
     */
    @TableField("driver_id")
    private Long driverId;

    /**
     * 出发城市id
     */
    @TableField("start_city_id")
    private Long startCityId;

    /**
     * 出发城市名称
     */
    @TableField("start_city_name")
    private String startCityName;

    /**
     * 出发地址
     */
    @TableField("start_address")
    private String startAddress;

    /**
     * 出发点经度
     */
    @TableField("starting_lon")
    private BigDecimal startingLon;

    /**
     * 出发点纬度
     */
    @TableField("starting_lat")
    private BigDecimal startingLat;

    /**
     * 目的点经度
     */
    @TableField("ending_lon")
    private BigDecimal endingLon;

    /**
     * 目的点纬度
     */
    @TableField("ending_lat")
    private BigDecimal endingLat;

    /**
     * 到达城市id
     */
    @TableField("end_city_id")
    private Long endCityId;

    /**
     * 到达城市名称
     */
    @TableField("end_city_name")
    private String endCityName;

    /**
     * 到达地址
     */
    @TableField("end_address")
    private String endAddress;

    /**
     * 可提供座位数
     */
    @TableField("can_seat_num")
    private Integer canSeatNum;

    /**
     * 预计行驶时间
     */
    @TableField("duration")
    private Integer duration;

    /**
     * 预计出发时间，空为全天
     */
    @TableField("plan_start_time")
    private String planStartTime;

    /**
     * 接单的配置json
     */
    @TableField("accept_config")
    private String acceptConfig;

    /**
     * 可接单星期几
     */
    @TableField("accept_week")
    private String acceptWeek;

    /**
     * 状态：0-创建启用、-1已删除
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 最新修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 自动播单
     */
    @TableField("auto_broadcast_flag")
    private Integer autoBroadcastFlag;

    /**
     * 距离千米
     */
    @TableField("distance")
    private BigDecimal distance;
    /**
     * 出发城市行政区code        db_column: start_district_code
     */

    @TableField(value = "start_district_code")
    private String startDistrictCode;
    /**
     * 出发城市行政区        db_column: start_district
     */

    @TableField(value = "start_district")
    private String startDistrict;
    /**
     * 目的城市行政区code        db_column: end_district_code
     */

    @TableField(value = "end_district_code")
    private String endDistrictCode;
    /**
     * 目的城市行政区        db_column: end_district
     */

    @TableField(value = "end_district")
    private String endDistrict;


}
