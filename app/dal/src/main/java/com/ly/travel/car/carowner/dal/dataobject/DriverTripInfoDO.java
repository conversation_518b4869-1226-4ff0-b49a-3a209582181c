/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.ly.travel.car.carowner.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
* DriverTripInfoDO
 * database table: driver_trip_info
 * database table comments: DriverTripInfo
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 *
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to
 * directory <tt>(project-home)/dalgen</tt>
 *
 * <AUTHOR> <PERSON>)
 **/
@TableName("driver_trip_info")
public class DriverTripInfoDO implements Serializable {
    private static final long serialVersionUID = -5216457518046898601L;

    /**
    * 主键        db_column: id
    */

    @TableId(value = "id", type = IdType.AUTO)
    private Long              id;
    /**
    * 环境        db_column: env
    */

    @TableField(value = "env")
    private String            env;
    /**
    * 行程订单no        db_column: driver_trip_no
    */

    @TableField(value = "driver_trip_no")
    private String            driverTripNo;
    /**
    * 司机id        db_column: driver_id
    */

    @TableField(value = "driver_id")
    private Long              driverId;
    /**
    * 出发城市id        db_column: start_city_id
    */

    @TableField(value = "start_city_id")
    private Long              startCityId;
    /**
    * 出发城市名称        db_column: start_city_name
    */

    @TableField(value = "start_city_name")
    private String            startCityName;
    /**
    * 出发地址        db_column: start_address
    */

    @TableField(value = "start_address")
    private String            startAddress;
    /**
    * 出发点经度        db_column: starting_lon
    */

    @TableField(value = "starting_lon")
    private BigDecimal        startingLon;
    /**
    * 出发点纬度        db_column: starting_lat
    */

    @TableField(value = "starting_lat")
    private BigDecimal        startingLat;
    /**
    * 目的点经度        db_column: ending_lon
    */

    @TableField(value = "ending_lon")
    private BigDecimal        endingLon;
    /**
    * 目的点纬度        db_column: ending_lat
    */

    @TableField(value = "ending_lat")
    private BigDecimal        endingLat;
    /**
    * 到达城市id        db_column: end_city_id
    */

    @TableField(value = "end_city_id")
    private Long              endCityId;
    /**
    * 到达城市名称        db_column: end_city_name
    */

    @TableField(value = "end_city_name")
    private String            endCityName;
    /**
    * 到达地址        db_column: end_address
    */

    @TableField(value = "end_address")
    private String            endAddress;
    /**
    * 目的地经纬度（lng,lat）        db_column: end_location
    */

    @TableField(value = "end_location")
    private String            endLocation;
    /**
    * 可提供座位数        db_column: can_seat_num
    */

    @TableField(value = "can_seat_num")
    private Integer               canSeatNum;
    /**
    * 预计出发时间        db_column: plan_start_time
    */

    @TableField(value = "plan_start_time")
    private Date              planStartTime;
    /**
    * 预计到达目的地时间        db_column: last_arrive_time
    */

    @TableField(value = "last_arrive_time")
    private Date              lastArriveTime;
    /**
    * 司机接单时间        db_column: driver_accept_time
    */

    @TableField(value = "driver_accept_time")
    private Date              driverAcceptTime;
    /**
    * 取消时间        db_column: cancel_time
    */

    @TableField(value = "cancel_time")
    private Date              cancelTime;
    /**
    * 取消类型（1-车主取消、2-系统取消、3-客服取消）        db_column: cancel_type
    */

    @TableField(value = "cancel_type")
    private Integer               cancelType;
    /**
    * 取消原因        db_column: cancel_reason
    */

    @TableField(value = "cancel_reason")
    private String            cancelReason;
    /**
    * 接单的配置json        db_column: accept_config
    */

    @TableField(value = "accept_config")
    private String            acceptConfig;
    /**
    * 状态：0-创建、1-已接单、2-已结束、3-已取消        db_column: status
    */

    @TableField(value = "status")
    private Integer               status;
    /**
    * 创建时间        db_column: create_time
    */

    @TableField(value = "create_time")
    private Date              createTime;
    /**
    * 最新修改时间        db_column: update_time
    */

    @TableField(value = "update_time")
    private Date              updateTime;
    /**
    * 自动播单        db_column: auto_broadcast_flag
    */

    @TableField(value = "auto_broadcast_flag")
    private Integer               autoBroadcastFlag;
    /**
    * 1 包车 2 拼车        db_column: type
    */

    @TableField(value = "type")
    private Integer               type;

    /**
    * 可接受时间 1 前后 1h 2 前后 2h 3 前后3h 4 后 1h 5 后 2h 6 自定义        db_column: recevice_time
    */

    @TableField(value = "receive_before_time")
    private Integer               receiveBeforeTime;
    /**
    * 自定义可接受时间        db_column: recevice_time_val
    */

    @TableField(value = "receive_after_time")
    private Integer               receiveAfterTime;
    /**
    * 1司机创建2系统自动        db_column: source
    */

    @TableField(value = "source")
    private Integer               source;

    @TableField(value = "distance")
    private BigDecimal        distance;

    /**
    * 剩余座位数        db_column: surplusSeatNum
    */
    @TableField(value = "surplus_seat_num")
    private Integer        surplusSeatNum;

    /**
     * 接单量        db_column: receive_order_num
     */
    @TableField(value = "receive_order_num")
    private Integer               receiveOrderNum;
    /**
     * 出发城市行政区code        db_column: start_district_code
     */

    @TableField(value = "start_district_code")
    private String startDistrictCode;
    /**
     * 出发城市行政区        db_column: start_district
     */

    @TableField(value = "start_district")
    private String startDistrict;
    /**
     * 目的城市行政区code        db_column: end_district_code
     */

    @TableField(value = "end_district_code")
    private String endDistrictCode;
    /**
     * 目的城市行政区        db_column: end_district
     */

    @TableField(value = "end_district")
    private String endDistrict;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return this.id;
    }

    public void setEnv(String env) {
        this.env = env;
    }

    public String getEnv() {
        return this.env;
    }

    public void setDriverTripNo(String driverTripNo) {
        this.driverTripNo = driverTripNo;
    }

    public String getDriverTripNo() {
        return this.driverTripNo;
    }

    public void setDriverId(Long driverId) {
        this.driverId = driverId;
    }

    public Long getDriverId() {
        return this.driverId;
    }

    public void setStartCityId(Long startCityId) {
        this.startCityId = startCityId;
    }

    public Long getStartCityId() {
        return this.startCityId;
    }

    public void setStartCityName(String startCityName) {
        this.startCityName = startCityName;
    }

    public String getStartCityName() {
        return this.startCityName;
    }

    public void setStartAddress(String startAddress) {
        this.startAddress = startAddress;
    }

    public String getStartAddress() {
        return this.startAddress;
    }

    public void setStartingLon(BigDecimal startingLon) {
        this.startingLon = startingLon;
    }

    public BigDecimal getStartingLon() {
        return this.startingLon;
    }

    public void setStartingLat(BigDecimal startingLat) {
        this.startingLat = startingLat;
    }

    public BigDecimal getStartingLat() {
        return this.startingLat;
    }

    public void setEndingLon(BigDecimal endingLon) {
        this.endingLon = endingLon;
    }

    public BigDecimal getEndingLon() {
        return this.endingLon;
    }

    public void setEndingLat(BigDecimal endingLat) {
        this.endingLat = endingLat;
    }

    public BigDecimal getEndingLat() {
        return this.endingLat;
    }

    public void setEndCityId(Long endCityId) {
        this.endCityId = endCityId;
    }

    public Long getEndCityId() {
        return this.endCityId;
    }

    public void setEndCityName(String endCityName) {
        this.endCityName = endCityName;
    }

    public String getEndCityName() {
        return this.endCityName;
    }

    public void setEndAddress(String endAddress) {
        this.endAddress = endAddress;
    }

    public String getEndAddress() {
        return this.endAddress;
    }

    public void setEndLocation(String endLocation) {
        this.endLocation = endLocation;
    }

    public String getEndLocation() {
        return this.endLocation;
    }

    public void setCanSeatNum(Integer canSeatNum) {
        this.canSeatNum = canSeatNum;
    }

    public Integer getCanSeatNum() {
        return this.canSeatNum;
    }

    public void setPlanStartTime(Date planStartTime) {
        this.planStartTime = planStartTime;
    }

    public Date getPlanStartTime() {
        return this.planStartTime;
    }

    public void setLastArriveTime(Date lastArriveTime) {
        this.lastArriveTime = lastArriveTime;
    }

    public Date getLastArriveTime() {
        return this.lastArriveTime;
    }

    public void setDriverAcceptTime(Date driverAcceptTime) {
        this.driverAcceptTime = driverAcceptTime;
    }

    public Date getDriverAcceptTime() {
        return this.driverAcceptTime;
    }

    public void setCancelTime(Date cancelTime) {
        this.cancelTime = cancelTime;
    }

    public Date getCancelTime() {
        return this.cancelTime;
    }

    public void setCancelType(Integer cancelType) {
        this.cancelType = cancelType;
    }

    public Integer getCancelType() {
        return this.cancelType;
    }

    public void setCancelReason(String cancelReason) {
        this.cancelReason = cancelReason;
    }

    public String getCancelReason() {
        return this.cancelReason;
    }

    public void setAcceptConfig(String acceptConfig) {
        this.acceptConfig = acceptConfig;
    }

    public String getAcceptConfig() {
        return this.acceptConfig;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getStatus() {
        return this.status;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return this.createTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getUpdateTime() {
        return this.updateTime;
    }

    public void setAutoBroadcastFlag(Integer autoBroadcastFlag) {
        this.autoBroadcastFlag = autoBroadcastFlag;
    }

    public Integer getAutoBroadcastFlag() {
        return this.autoBroadcastFlag;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getType() {
        return this.type;
    }

    public Integer getReceiveBeforeTime() {
        return receiveBeforeTime;
    }

    public void setReceiveBeforeTime(Integer receiveBeforeTime) {
        this.receiveBeforeTime = receiveBeforeTime;
    }

    public Integer getReceiveAfterTime() {
        return receiveAfterTime;
    }

    public void setReceiveAfterTime(Integer receiveAfterTime) {
        this.receiveAfterTime = receiveAfterTime;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public Integer getSource() {
        return this.source;
    }

    public BigDecimal getDistance() {
        return distance;
    }

    public void setDistance(BigDecimal distance) {
        this.distance = distance;
    }

    public void setSurplusSeatNum(Integer surplusSeatNum) {
        this.surplusSeatNum = surplusSeatNum;
    }

    public Integer getSurplusSeatNum() {
        return this.surplusSeatNum;
    }

    public Integer getReceiveOrderNum() {
        return receiveOrderNum;
    }

    public void setReceiveOrderNum(Integer receiveOrderNum) {
        this.receiveOrderNum = receiveOrderNum;
    }

    public void setStartDistrictCode(String startDistrictCode) {
        this.startDistrictCode = startDistrictCode;
    }

    public String getStartDistrictCode() {
        return this.startDistrictCode;
    }

    public void setStartDistrict(String startDistrict) {
        this.startDistrict = startDistrict;
    }

    public String getStartDistrict() {
        return this.startDistrict;
    }

    public void setEndDistrictCode(String endDistrictCode) {
        this.endDistrictCode = endDistrictCode;
    }

    public String getEndDistrictCode() {
        return this.endDistrictCode;
    }

    public void setEndDistrict(String endDistrict) {
        this.endDistrict = endDistrict;
    }

    public String getEndDistrict() {
        return this.endDistrict;
    }

    public String toString() {
        return new ToStringBuilder(this).append("Id", getId()).append("Env", getEnv()).append("DriverTripNo", getDriverTripNo()).append("DriverId", getDriverId())
            .append("StartCityId", getStartCityId()).append("StartCityName", getStartCityName()).append("StartAddress", getStartAddress()).append("StartingLon", getStartingLon())
            .append("StartingLat", getStartingLat()).append("EndingLon", getEndingLon()).append("EndingLat", getEndingLat()).append("EndCityId", getEndCityId())
            .append("EndCityName", getEndCityName()).append("EndAddress", getEndAddress()).append("EndLocation", getEndLocation()).append("CanSeatNum", getCanSeatNum())
            .append("PlanStartTime", getPlanStartTime()).append("LastArriveTime", getLastArriveTime()).append("DriverAcceptTime", getDriverAcceptTime())
            .append("CancelTime", getCancelTime()).append("CancelType", getCancelType()).append("CancelReason", getCancelReason()).append("AcceptConfig", getAcceptConfig())
            .append("Status", getStatus()).append("CreateTime", getCreateTime()).append("UpdateTime", getUpdateTime()).append("AutoBroadcastFlag", getAutoBroadcastFlag())
            .append("Type", getType()).append("ReceiveBeforeTime", getReceiveBeforeTime()).append("ReceiveAfterTime", getReceiveAfterTime())
                .append("Source", getSource())
                .append("Distance", getDistance())
                .append("SurplusSeatNum", getSurplusSeatNum())
                .append("ReceiveOrderNum", getReceiveOrderNum())
                .append("StartDistrictCode", getStartDistrictCode())
                .append("StartDistrict", getStartDistrict())
                .append("EndDistrictCode", getEndDistrictCode())
                .append("EndDistrict", getEndDistrict())
            .toString();
    }

    public int hashCode() {
        return new HashCodeBuilder().append(getId()).toHashCode();
    }

    public boolean equals(Object obj) {
        if (obj == null)
            return false;
        if (this == obj)
            return true;
        if (obj instanceof DriverTripInfoDO == false)
            return false;
        DriverTripInfoDO other = (DriverTripInfoDO) obj;
        return new EqualsBuilder().append(getId(), other.getId()).isEquals();
    }
}
