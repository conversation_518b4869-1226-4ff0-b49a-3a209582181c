/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.ly.travel.car.carowner.dal.dataobject;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.util.*;


/**
 * DriverWithdrawLogDO
 * database table: driver_withdraw_log
 * database table comments: DriverWithdrawLog
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * <p>
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to
 * directory <tt>(project-home)/dalgen</tt>
 *
 * <AUTHOR>
 **/
@TableName("driver_withdraw_log")
public class DriverWithdrawLogDO implements java.io.Serializable {
    private static final long serialVersionUID = -*********8046898601L;

    /**
     * id        db_column: id
     */

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 提现单号        db_column: withdraw_no
     */

    @TableField(value = "withdraw_no")
    private String withdrawNo;
    /**
     * 日志类型：1-申请提现、2-审核、3-支付        db_column: log_type
     */

    @TableField(value = "log_type")
    private Integer logType;
    /**
     * 备注        db_column: remark
     */

    @TableField(value = "remark")
    private String remark;
    /**
     * 创建人        db_column: create_user
     */

    @TableField(value = "create_user")
    private String createUser;
    /**
     * 创建时间        db_column: create_time
     */

    @TableField(value = "create_time")
    private Date createTime;
    /**
     * 司机id        db_column: driver_id
     */

    @TableField(value = "driver_id")
    private Long driverId;


    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return this.id;
    }

    public void setWithdrawNo(String withdrawNo) {
        this.withdrawNo = withdrawNo;
    }

    public String getWithdrawNo() {
        return this.withdrawNo;
    }

    public void setLogType(Integer logType) {
        this.logType = logType;
    }

    public Integer getLogType() {
        return this.logType;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getRemark() {
        return this.remark;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getCreateUser() {
        return this.createUser;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return this.createTime;
    }

    public void setDriverId(Long driverId) {
        this.driverId = driverId;
    }

    public Long getDriverId() {
        return this.driverId;
    }

    public String toString() {
        return new ToStringBuilder(this)
                .append("Id", getId())
                .append("WithdrawNo", getWithdrawNo())
                .append("LogType", getLogType())
                .append("Remark", getRemark())
                .append("CreateUser", getCreateUser())
                .append("CreateTime", getCreateTime())
                .append("DriverId", getDriverId())
                .toString();
    }

    public int hashCode() {
        return new HashCodeBuilder()
                .append(getId())
                .toHashCode();
    }

    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (this == obj) return true;
        if (obj instanceof DriverWithdrawLogDO == false) return false;
        DriverWithdrawLogDO other = (DriverWithdrawLogDO) obj;
        return new EqualsBuilder()
                .append(getId(), other.getId())
                .isEquals();
    }
}

