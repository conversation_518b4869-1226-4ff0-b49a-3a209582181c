/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.ly.travel.car.carowner.dal.dataobject;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.util.*;

/**
 * DriverWithdrawalDO
 * database table: driver_withdrawal
 * database table comments: DriverWithdrawal
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * <p>
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to
 * directory <tt>(project-home)/dalgen</tt>
 *
 * <AUTHOR>
 **/
@TableName("driver_withdrawal")
public class DriverWithdrawalDO implements java.io.Serializable {
    private static final long serialVersionUID = -5216457518046898601L;

    /**
     * 主键ID        db_column: id
     */

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 提现单号        db_column: withdraw_no
     */

    @TableField(value = "withdraw_no")
    private String withdrawNo;
    /**
     * 司机 id        db_column: driver_id
     */

    @TableField(value = "driver_id")
    private Long driverId;
    /**
     * 环境标识        db_column: env
     */

    @TableField(value = "env")
    private String env;
    /**
     * 司机银行卡ID        db_column: driver_bank_card_id
     */

    @TableField(value = "driver_bank_card_id")
    private String driverBankCardId;
    /**
     * 提现金额        db_column: amount
     */

    @TableField(value = "amount")
    private BigDecimal amount;
    /**
     * 易宝支付订单号        db_column: yee_pay_order_no
     */

    @TableField(value = "yee_pay_order_no")
    private String yeePayOrderNo;
    /**
     * 打款回调时间        db_column: remit_back_time
     */

    @TableField(value = "remit_back_time")
    private Date remitBackTime;
    /**
     * 申请状态 1-已申请 2-提现成功 3-提现失败        db_column: status
     */

    @TableField(value = "status")
    private Integer status;
    /**
     * 申请时间        db_column: apply_time
     */

    @TableField(value = "apply_time")
    private Date applyTime;
    /**
     * 申请通过时间        db_column: approval_time
     */

    @TableField(value = "approval_time")
    private Date approvalTime;
    /**
     * 创建时间        db_column: create_time
     */

    @TableField(value = "create_time")
    private Date createTime;
    /**
     * 创建人        db_column: create_user
     */

    @TableField(value = "create_user")
    private String createUser;
    /**
     * 修改时间        db_column: update_time
     */

    @TableField(value = "update_time")
    private Date updateTime;
    /**
     * 修改人        db_column: update_user
     */

    @TableField(value = "update_user")
    private String updateUser;
    /**
     * 重试次数        db_column: apply_times
     */

    @TableField(value = "apply_times")
    private Integer applyTimes;
    /**
     * 备注        db_column: remark
     */

    @TableField(value = "remark")
    private String remark;
    /**
     * 提现设备号        db_column: driver_device_id
     */

    @TableField(value = "driver_device_id")
    private String driverDeviceId;


    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return this.id;
    }

    public void setWithdrawNo(String withdrawNo) {
        this.withdrawNo = withdrawNo;
    }

    public String getWithdrawNo() {
        return this.withdrawNo;
    }

    public void setDriverId(Long driverId) {
        this.driverId = driverId;
    }

    public Long getDriverId() {
        return this.driverId;
    }

    public void setEnv(String env) {
        this.env = env;
    }

    public String getEnv() {
        return this.env;
    }

    public void setDriverBankCardId(String driverBankCardId) {
        this.driverBankCardId = driverBankCardId;
    }

    public String getDriverBankCardId() {
        return this.driverBankCardId;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getAmount() {
        return this.amount;
    }

    public void setYeePayOrderNo(String yeePayOrderNo) {
        this.yeePayOrderNo = yeePayOrderNo;
    }

    public String getYeePayOrderNo() {
        return this.yeePayOrderNo;
    }

    public void setRemitBackTime(Date remitBackTime) {
        this.remitBackTime = remitBackTime;
    }

    public Date getRemitBackTime() {
        return this.remitBackTime;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getStatus() {
        return this.status;
    }

    public void setApplyTime(Date applyTime) {
        this.applyTime = applyTime;
    }

    public Date getApplyTime() {
        return this.applyTime;
    }

    public void setApprovalTime(Date approvalTime) {
        this.approvalTime = approvalTime;
    }

    public Date getApprovalTime() {
        return this.approvalTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return this.createTime;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getCreateUser() {
        return this.createUser;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getUpdateTime() {
        return this.updateTime;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public String getUpdateUser() {
        return this.updateUser;
    }

    public void setApplyTimes(Integer applyTimes) {
        this.applyTimes = applyTimes;
    }

    public Integer getApplyTimes() {
        return this.applyTimes;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getRemark() {
        return this.remark;
    }

    public void setDriverDeviceId(String driverDeviceId) {
        this.driverDeviceId = driverDeviceId;
    }

    public String getDriverDeviceId() {
        return this.driverDeviceId;
    }

    public String toString() {
        return new ToStringBuilder(this)
                .append("Id", getId())
                .append("WithdrawNo", getWithdrawNo())
                .append("DriverId", getDriverId())
                .append("Env", getEnv())
                .append("DriverBankCardId", getDriverBankCardId())
                .append("Amount", getAmount())
                .append("YeePayOrderNo", getYeePayOrderNo())
                .append("RemitBackTime", getRemitBackTime())
                .append("Status", getStatus())
                .append("ApplyTime", getApplyTime())
                .append("ApprovalTime", getApprovalTime())
                .append("CreateTime", getCreateTime())
                .append("CreateUser", getCreateUser())
                .append("UpdateTime", getUpdateTime())
                .append("UpdateUser", getUpdateUser())
                .append("ApplyTimes", getApplyTimes())
                .append("Remark", getRemark())
                .append("DriverDeviceId", getDriverDeviceId())
                .toString();
    }

    public int hashCode() {
        return new HashCodeBuilder()
                .append(getId())
                .toHashCode();
    }

    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (this == obj) return true;
        if (obj instanceof DriverWithdrawalDO == false) return false;
        DriverWithdrawalDO other = (DriverWithdrawalDO) obj;
        return new EqualsBuilder()
                .append(getId(), other.getId())
                .isEquals();
    }
}

