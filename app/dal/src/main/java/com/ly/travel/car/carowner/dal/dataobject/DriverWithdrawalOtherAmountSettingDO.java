/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.ly.travel.car.carowner.dal.dataobject;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.util.*;

/**
 * DriverWithdrawalOtherAmountSettingDO
 * database table: driver_withdrawal_other_amount_setting
 * database table comments: DriverWithdrawalOtherAmountSetting
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * <p>
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to
 * directory <tt>(project-home)/dalgen</tt>
 *
 * <AUTHOR> Junxiang)
 **/
@TableName("driver_withdrawal_other_amount_setting")
public class DriverWithdrawalOtherAmountSettingDO implements java.io.Serializable {
    private static final long serialVersionUID = -5216457518046898601L;

    /**
     * 主键ID        db_column: id
     */

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 时间段多少小时内        db_column: limit_sum
     */

    @TableField(value = "limit_sum")
    private BigDecimal limitSum;
    /**
     * 提现金额不超过        db_column: limit_amount
     */

    @TableField(value = "limit_amount")
    private BigDecimal limitAmount;
    /**
     * 创建时间        db_column: create_time
     */

    @TableField(value = "create_time")
    private Date createTime;
    /**
     * 创建人        db_column: create_user
     */

    @TableField(value = "create_user")
    private String createUser;
    /**
     * 修改时间        db_column: update_time
     */

    @TableField(value = "update_time")
    private Date updateTime;
    /**
     * 修改人        db_column: update_user
     */

    @TableField(value = "update_user")
    private String updateUser;
    /**
     * 提现配置id        db_column: withdrawal_setting_id
     */

    @TableField(value = "withdrawal_setting_id")
    private Long withdrawalSettingId;


    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return this.id;
    }

    public void setLimitSum(BigDecimal limitSum) {
        this.limitSum = limitSum;
    }

    public BigDecimal getLimitSum() {
        return this.limitSum;
    }

    public void setLimitAmount(BigDecimal limitAmount) {
        this.limitAmount = limitAmount;
    }

    public BigDecimal getLimitAmount() {
        return this.limitAmount;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return this.createTime;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getCreateUser() {
        return this.createUser;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getUpdateTime() {
        return this.updateTime;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public String getUpdateUser() {
        return this.updateUser;
    }

    public void setWithdrawalSettingId(Long withdrawalSettingId) {
        this.withdrawalSettingId = withdrawalSettingId;
    }

    public Long getWithdrawalSettingId() {
        return this.withdrawalSettingId;
    }

    public String toString() {
        return new ToStringBuilder(this)
                .append("Id", getId())
                .append("LimitSum", getLimitSum())
                .append("LimitAmount", getLimitAmount())
                .append("CreateTime", getCreateTime())
                .append("CreateUser", getCreateUser())
                .append("UpdateTime", getUpdateTime())
                .append("UpdateUser", getUpdateUser())
                .append("WithdrawalSettingId", getWithdrawalSettingId())
                .toString();
    }

    public int hashCode() {
        return new HashCodeBuilder()
                .append(getId())
                .toHashCode();
    }

    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (this == obj) return true;
        if (obj instanceof DriverWithdrawalOtherAmountSettingDO == false) return false;
        DriverWithdrawalOtherAmountSettingDO other = (DriverWithdrawalOtherAmountSettingDO) obj;
        return new EqualsBuilder()
                .append(getId(), other.getId())
                .isEquals();
    }
}

