/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.ly.travel.car.carowner.dal.dataobject;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.util.*;

/**
 * DriverWithdrawalSettingDO
 * database table: driver_withdrawal_setting
 * database table comments: DriverWithdrawalSetting
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * <p>
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to
 * directory <tt>(project-home)/dalgen</tt>
 *
 * <AUTHOR> <PERSON>)
 **/
@TableName("driver_withdrawal_setting")
public class DriverWithdrawalSettingDO implements java.io.Serializable {
    private static final long serialVersionUID = -5216457518046898601L;

    /**
     * 主键ID        db_column: id
     */

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 提现配置名        db_column: name
     */

    @TableField(value = "name")
    private String name;
    /**
     * 冻结时长        db_column: limit_hour
     */

    @TableField(value = "limit_hour")
    private Integer limitHour;
    /**
     * 每日最大提现次数        db_column: limit_sum
     */

    @TableField(value = "limit_sum")
    private Integer limitSum;
    /**
     * 单次最大提现金额        db_column: limit_amount
     */

    @TableField(value = "limit_amount")
    private BigDecimal limitAmount;
    /**
     * 环境标识        db_column: env
     */

    @TableField(value = "env")
    private String env;
    /**
     * 创建时间        db_column: create_time
     */

    @TableField(value = "create_time")
    private Date createTime;
    /**
     * 创建人        db_column: create_user
     */

    @TableField(value = "create_user")
    private String createUser;
    /**
     * 修改时间        db_column: update_time
     */

    @TableField(value = "update_time")
    private Date updateTime;
    /**
     * 修改人        db_column: update_user
     */

    @TableField(value = "update_user")
    private String updateUser;


    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return this.id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return this.name;
    }

    public void setLimitHour(Integer limitHour) {
        this.limitHour = limitHour;
    }

    public Integer getLimitHour() {
        return this.limitHour;
    }

    public void setLimitSum(Integer limitSum) {
        this.limitSum = limitSum;
    }

    public Integer getLimitSum() {
        return this.limitSum;
    }

    public void setLimitAmount(BigDecimal limitAmount) {
        this.limitAmount = limitAmount;
    }

    public BigDecimal getLimitAmount() {
        return this.limitAmount;
    }

    public void setEnv(String env) {
        this.env = env;
    }

    public String getEnv() {
        return this.env;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return this.createTime;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getCreateUser() {
        return this.createUser;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getUpdateTime() {
        return this.updateTime;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public String getUpdateUser() {
        return this.updateUser;
    }

    public String toString() {
        return new ToStringBuilder(this)
                .append("Id", getId())
                .append("Name", getName())
                .append("LimitHour", getLimitHour())
                .append("LimitSum", getLimitSum())
                .append("LimitAmount", getLimitAmount())
                .append("Env", getEnv())
                .append("CreateTime", getCreateTime())
                .append("CreateUser", getCreateUser())
                .append("UpdateTime", getUpdateTime())
                .append("UpdateUser", getUpdateUser())
                .toString();
    }

    public int hashCode() {
        return new HashCodeBuilder()
                .append(getId())
                .toHashCode();
    }

    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (this == obj) return true;
        if (obj instanceof DriverWithdrawalSettingDO == false) return false;
        DriverWithdrawalSettingDO other = (DriverWithdrawalSettingDO) obj;
        return new EqualsBuilder()
                .append(getId(), other.getId())
                .isEquals();
    }
}

