/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.ly.travel.car.carowner.dal.dataobject;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;


import java.io.*;
import java.net.*;
import java.util.*;

import java.math.BigInteger;

import com.ly.sof.utils.page.PageQuery;
import com.ly.sof.utils.page.PageList;
import com.ly.sof.utils.page.PageQueryUtils;

/**
 * RideDispatchInfoDO
 * database table: ride_dispatch_info
 * database table comments: RideDispatchInfo
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * <p>
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to
 * directory <tt>(project-home)/dalgen</tt>
 *
 * <AUTHOR> Wang(Wang Junxiang)
 **/
@TableName("ride_dispatch_info")
public class RideDispatchInfoDO implements java.io.Serializable {
    private static final Long serialVersionUID = -5216457518046898601L;

    /**
     * 自增id        db_column: id
     */

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 订单号        db_column: order_no
     */

    @TableField(value = "order_no")
    private String orderNo;
    /**
     * 状态，1报单中         db_column: status
     */

    @TableField(value = "status")
    private Integer status;
    /**
     * 举手时间        db_column: accept_time
     */

    @TableField(value = "accept_time")
    private Date acceptTime;
    /**
     * 创建人        db_column: create_user
     */

    @TableField(value = "create_user")
    private String createUser;
    /**
     * 创建时间        db_column: create_time
     */

    @TableField(value = "create_time")
    private Date createTime;
    /**
     * 修改人        db_column: update_user
     */

    @TableField(value = "update_user")
    private String updateUser;
    /**
     * 修改时间        db_column: update_time
     */

    @TableField(value = "update_time")
    private Date updateTime;
    /**
     * 车牌        db_column: vehicle_no
     */

    @TableField(value = "vehicle_no")
    private String vehicleNo;
    /**
     * 司机姓名        db_column: driver_name
     */

    @TableField(value = "driver_name")
    private String driverName;
    /**
     * 司机电话        db_column: driver_mobile
     */

    @TableField(value = "driver_mobile")
    private String driverMobile;
    /**
     * 司机行程号        db_column: driver_trip_no
     */

    @TableField(value = "driver_trip_no")
    private String driverTripNo;
    /**
     * 司机id        db_column: driver_id
     */

    @TableField(value = "driver_id")
    private Long driverId;
    /**
     * 顺路度        db_column: hitch_percent
     */

    @TableField(value = "hitch_percent")
    private Integer hitchPercent;

    /**
     * 1自动接单
     */
    @TableField(value = "accept_type")
    private Integer acceptType;


    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return this.id;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOrderNo() {
        return this.orderNo;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getStatus() {
        return this.status;
    }

    public void setAcceptTime(Date acceptTime) {
        this.acceptTime = acceptTime;
    }

    public Date getAcceptTime() {
        return this.acceptTime;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getCreateUser() {
        return this.createUser;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return this.createTime;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public String getUpdateUser() {
        return this.updateUser;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getUpdateTime() {
        return this.updateTime;
    }

    public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
    }

    public String getVehicleNo() {
        return this.vehicleNo;
    }

    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    public String getDriverName() {
        return this.driverName;
    }

    public void setDriverMobile(String driverMobile) {
        this.driverMobile = driverMobile;
    }

    public String getDriverMobile() {
        return this.driverMobile;
    }

    public void setDriverTripNo(String driverTripNo) {
        this.driverTripNo = driverTripNo;
    }

    public String getDriverTripNo() {
        return this.driverTripNo;
    }

    public void setDriverId(Long driverId) {
        this.driverId = driverId;
    }

    public Long getDriverId() {
        return this.driverId;
    }

    public void setHitchPercent(Integer hitchPercent) {
        this.hitchPercent = hitchPercent;
    }

    public Integer getHitchPercent() {
        return this.hitchPercent;
    }

    public Integer getAcceptType() {
        return acceptType;
    }

    public void setAcceptType(Integer acceptType) {
        this.acceptType = acceptType;
    }

    public String toString() {
        return new ToStringBuilder(this)
                .append("Id", getId())
                .append("OrderNo", getOrderNo())
                .append("Status", getStatus())
                .append("AcceptTime", getAcceptTime())
                .append("CreateUser", getCreateUser())
                .append("CreateTime", getCreateTime())
                .append("UpdateUser", getUpdateUser())
                .append("UpdateTime", getUpdateTime())
                .append("VehicleNo", getVehicleNo())
                .append("DriverName", getDriverName())
                .append("DriverMobile", getDriverMobile())
                .append("DriverTripNo", getDriverTripNo())
                .append("DriverId", getDriverId())
                .append("HitchPercent", getHitchPercent())
                .append("AcceptType", getAcceptType())
                .toString();
    }

    public int hashCode() {
        return new HashCodeBuilder()
                .append(getId())
                .toHashCode();
    }

    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (this == obj) return true;
        if (obj instanceof RideDispatchInfoDO == false) return false;
        RideDispatchInfoDO other = (RideDispatchInfoDO) obj;
        return new EqualsBuilder()
                .append(getId(), other.getId())
                .isEquals();
    }
}

