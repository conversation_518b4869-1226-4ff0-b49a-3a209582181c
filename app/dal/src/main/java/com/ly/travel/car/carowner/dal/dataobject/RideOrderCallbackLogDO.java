package com.ly.travel.car.carowner.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @desc RideOrderCallbackLogDO
 */
@Data
@TableName("ride_order_callback_log")
public class RideOrderCallbackLogDO {

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 订单号
     */
    @TableField(value = "order_no")
    private String orderNo;
    /**
     * 订单号
     */
    @TableField(value = "partner_id")
    private String partnerId;
    /**
     * 环境变量
     */
    @TableField(value = "env")
    private String env;
    /**
     * 回调类型:1:取消回调 2:派车回调 3:退款回调 4:司机接单回调 5:订单完结回调 6:乘客上车回调 7:乘客下车回调
     */
    @TableField(value = "callback_type")
    private Integer callbackType;
    /**
     * 回调地址
     */
    @TableField(value = "callback_url")
    private String callbackUrl;
    /**
     * 回调参数
     */
    @TableField(value = "callback_params")
    private String callbackParams;
    /**
     * 已发起的重试次数
     */
    @TableField(value = "retry")
    private Integer retry;
    /**
     * 回调返回code
     */
    @TableField(value = "return_code")
    private String returnCode;
    /**
     * 状态-0：新增，1：成功，2：失败
     */
    @TableField(value = "status")
    private Integer status;
    /**
     * 创建人
     */
    @TableField(value = "create_user")
    private String createUser;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;
    /**
     * 修改人
     */
    @TableField(value = "update_user")
    private String updateUser;
    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;
}
