package com.ly.travel.car.carowner.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.Date;

/**
* RideOrderCancelDO
 * database table: ride_order_cancel
 * database table comments: RideOrderCancel
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to 
 * directory <tt>(project-home)/dalgen</tt>
 * 
 * <AUTHOR>
 **/
@TableName("ride_order_cancel")
public class RideOrderCancelDO implements Serializable {
private static final long serialVersionUID = -5216457518046898601L;

    /**
    * 自增id        db_column: id
    */

        @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
    * 订单号        db_column: order_no
    */

        @TableField(value = "order_no")
    private String orderNo;
    /**
    * 取消类型        db_column: cancel_type
    */

        @TableField(value = "cancel_type")
    private Integer cancelType;
    /**
    * 取消原因        db_column: cancel_reason
    */

        @TableField(value = "cancel_reason")
    private String cancelReason;
    /**
    * 取消备注        db_column: cancel_remark
    */

        @TableField(value = "cancel_remark")
    private String cancelRemark;
    /**
    * 状态-1 删除        db_column: status
    */

        @TableField(value = "status")
    private Integer status;
    /**
    * 创建人        db_column: create_user
    */

        @TableField(value = "create_user")
    private String createUser;
    /**
    * 创建时间        db_column: create_time
    */

        @TableField(value = "create_time")
    private Date createTime;
    /**
    * 修改人        db_column: update_user
    */

        @TableField(value = "update_user")
    private String updateUser;
    /**
    * 修改时间        db_column: update_time
    */

        @TableField(value = "update_time")
    private Date updateTime;


        public void setId(Long id) {
        this.id = id;
        }

        public Long getId() {
        return this.id;
        }

        public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        }

        public String getOrderNo() {
        return this.orderNo;
        }

        public void setCancelType(Integer cancelType) {
        this.cancelType = cancelType;
        }

        public Integer getCancelType() {
        return this.cancelType;
        }

        public void setCancelReason(String cancelReason) {
        this.cancelReason = cancelReason;
        }

        public String getCancelReason() {
        return this.cancelReason;
        }

        public void setCancelRemark(String cancelRemark) {
        this.cancelRemark = cancelRemark;
        }

        public String getCancelRemark() {
        return this.cancelRemark;
        }

        public void setStatus(Integer status) {
        this.status = status;
        }

        public Integer getStatus() {
        return this.status;
        }

        public void setCreateUser(String createUser) {
        this.createUser = createUser;
        }

        public String getCreateUser() {
        return this.createUser;
        }

        public void setCreateTime(Date createTime) {
        this.createTime = createTime;
        }

        public Date getCreateTime() {
        return this.createTime;
        }

        public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        }

        public String getUpdateUser() {
        return this.updateUser;
        }

        public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        }

        public Date getUpdateTime() {
        return this.updateTime;
        }

public String toString() {
return new ToStringBuilder(this)
    .append("Id",getId())
    .append("OrderNo",getOrderNo())
    .append("CancelType",getCancelType())
    .append("CancelReason",getCancelReason())
    .append("CancelRemark",getCancelRemark())
    .append("Status",getStatus())
    .append("CreateUser",getCreateUser())
    .append("CreateTime",getCreateTime())
    .append("UpdateUser",getUpdateUser())
    .append("UpdateTime",getUpdateTime())
.toString();
}

public int hashCode() {
return new HashCodeBuilder()
    .append(getId())
.toHashCode();
}

public boolean equals(Object obj) {
if(obj == null) return false;
if(this == obj) return true;
if(obj instanceof RideOrderCancelDO == false) return false;
RideOrderCancelDO other = (RideOrderCancelDO)obj;
return new EqualsBuilder()
    .append(getId(),other.getId())
.isEquals();
}
}

