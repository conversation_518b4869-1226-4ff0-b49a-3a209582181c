/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.ly.travel.car.carowner.dal.dataobject;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;


import java.io.*;
import java.math.BigDecimal;
import java.net.*;
import java.util.*;

import java.math.BigInteger;

import com.ly.sof.utils.page.PageQuery;
import com.ly.sof.utils.page.PageList;
import com.ly.sof.utils.page.PageQueryUtils;
/**
* RideOrderInfoDO
 * database table: ride_order_info
 * database table comments: RideOrderInfo
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 *
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to
 * directory <tt>(project-home)/dalgen</tt>
 *
 * <AUTHOR> Wang(Wang Junxiang)
 **/
@TableName("ride_order_info")
public class RideOrderInfoDO implements java.io.Serializable {
private static final long serialVersionUID = -5216457518046898601L;

    /**
    * 自增id        db_column: id
    */

        @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
    * 环境        db_column: env
    */

        @TableField(value = "env")
    private String env;
    /**
    * 订单号        db_column: order_no
    */

        @TableField(value = "order_no")
    private String orderNo;
    /**
    * 供应链订单号        db_column: distributor_order_no
    */

        @TableField(value = "distributor_order_no")
    private String distributorOrderNo;
    /**
    * 供应链主订单号        db_column: distributor_main_order_no
    */

        @TableField(value = "distributor_main_order_no")
    private String distributorMainOrderNo;
    /**
    * 司机行程号        db_column: driver_trip_no
    */

        @TableField(value = "driver_trip_no")
    private String driverTripNo;
    /**
    * 订单来源  0:未知 1:同程C端        db_column: source
    */

        @TableField(value = "source")
    private Integer source;
    /**
    * 顺路度        db_column: hitch_percent
    */

        @TableField(value = "hitch_percent")
    private Integer hitchPercent;
    /**
    * 出发城市id        db_column: start_city_id
    */

        @TableField(value = "start_city_id")
    private Long startCityId;
    /**
    * 到达城市id        db_column: end_city_id
    */

        @TableField(value = "end_city_id")
    private Long endCityId;
    /**
    * 出发城市        db_column: start_city
    */

        @TableField(value = "start_city")
    private String startCity;
    /**
    * 到达城市        db_column: end_city
    */

        @TableField(value = "end_city")
    private String endCity;
    /**
    * 出发城市行政区code        db_column: start_district_code
    */

        @TableField(value = "start_district_code")
    private String startDistrictCode;
    /**
    * 出发城市行政区        db_column: start_district
    */

        @TableField(value = "start_district")
    private String startDistrict;
    /**
    * 目的城市行政区code        db_column: end_district_code
    */

        @TableField(value = "end_district_code")
    private String endDistrictCode;
    /**
    * 目的城市行政区        db_column: end_district
    */

        @TableField(value = "end_district")
    private String endDistrict;
    /**
    * 出发地点        db_column: starting_add
    */

        @TableField(value = "starting_add")
    private String startingAdd;
    /**
    * 目的地点        db_column: ending_add
    */

        @TableField(value = "ending_add")
    private String endingAdd;
    /**
    * 出发点经度        db_column: starting_lon
    */

        @TableField(value = "starting_lon")
    private BigDecimal startingLon;
    /**
    * 出发点纬度        db_column: starting_lat
    */

        @TableField(value = "starting_lat")
    private BigDecimal startingLat;
    /**
    * 目的点经度        db_column: ending_lon
    */

        @TableField(value = "ending_lon")
    private BigDecimal endingLon;
    /**
    * 目的点纬度        db_column: ending_lat
    */

        @TableField(value = "ending_lat")
    private BigDecimal endingLat;
    /**
    * 用车时间        db_column: starting_time
    */

        @TableField(value = "starting_time")
    private Date startingTime;
    /**
    * 最晚用车时间        db_column: last_starting_time
    */

        @TableField(value = "last_starting_time")
    private Date lastStartingTime;
    /**
    * 乘客联系电号        db_column: telephone
    */

        @TableField(value = "telephone")
    private String telephone;
    /**
    * 真实号码        db_column: real_telephone
    */

        @TableField(value = "real_telephone")
    private String realTelephone;
    /**
    * 数量        db_column: passenger_count
    */

        @TableField(value = "passenger_count")
    private Integer passengerCount;
    /**
    * 订单金额        db_column: amount
    */

        @TableField(value = "amount")
    private BigDecimal amount;
    /**
    * 付款金额        db_column: pay_price
    */

        @TableField(value = "pay_price")
    private BigDecimal payPrice;
    /**
    * 订单状态   -1已删除    1.抢单中(c端决策之前)  2.已派车   3已完成   4已取消        db_column: status
    */

        @TableField(value = "status")
    private Integer status;
    /**
    * 支付状态   0未支付   1 已支付  2部分支付  3已退款  4部分退款        db_column: pay_status
    */

        @TableField(value = "pay_status")
    private Integer payStatus;
    /**
    * 支付类型   1普通  2信用分        db_column: pay_type
    */

        @TableField(value = "pay_type")
    private Integer payType;
    /**
    * refId        db_column: ref_id
    */

        @TableField(value = "ref_id")
    private String refId;
    /**
    * 订单类型：1、包车； 2、拼车；        db_column: order_type
    */

        @TableField(value = "order_type")
    private Integer orderType;
    /**
    * 用户id        db_column: member_id
    */

        @TableField(value = "member_id")
    private Long memberId;
    /**
    * 车牌        db_column: vehicle_no
    */

        @TableField(value = "vehicle_no")
    private String vehicleNo;
    /**
    * 司机姓名        db_column: driver_name
    */

        @TableField(value = "driver_name")
    private String driverName;
    /**
    * 司机电话        db_column: driver_mobile
    */

        @TableField(value = "driver_mobile")
    private String driverMobile;
    /**
    * 司机id        db_column: driver_id
    */

        @TableField(value = "driver_id")
    private Long driverId;
    /**
    * 下单时间        db_column: book_time
    */

        @TableField(value = "book_time")
    private Date bookTime;
    /**
    * 创建人        db_column: create_user
    */

        @TableField(value = "create_user")
    private String createUser;
    /**
    * 创建时间        db_column: create_time
    */

        @TableField(value = "create_time")
    private Date createTime;
    /**
    * 修改人        db_column: update_user
    */

        @TableField(value = "update_user")
    private String updateUser;
    /**
    * 修改时间        db_column: update_time
    */

        @TableField(value = "update_time")
    private Date updateTime;
    /**
    * 备注        db_column: remark
    */

        @TableField(value = "remark")
    private String remark;
    /**
    * 出发详细地址        db_column: starting_add_detail
    */

        @TableField(value = "starting_add_detail")
    private String startingAddDetail;
    /**
    * 到达详细地址        db_column: ending_add_detail
    */

        @TableField(value = "ending_add_detail")
    private String endingAddDetail;
    /**
    * 总里程数(单位：千米）        db_column: distance
    */

        @TableField(value = "distance")
    private BigDecimal distance;
    /**
    * 时长，分钟单位        db_column: duration
    */

        @TableField(value = "duration")
    private Integer duration;
    /**
    * 随单分佣比例0-1之间        db_column: order_commission_ratio
    */

        @TableField(value = "order_commission_ratio")
    private BigDecimal orderCommissionRatio;
    /**
    * 随单分佣上限(单位元)        db_column: order_commission_cap
    */

        @TableField(value = "order_commission_cap")
    private BigDecimal orderCommissionCap;

    @TableField(value = "pay_time")
        private Date payTime;

    @TableField(value = "finish_time")
    private Date finishTime;

        public void setId(Long id) {
        this.id = id;
        }

        public Long getId() {
        return this.id;
        }

        public void setEnv(String env) {
        this.env = env;
        }

        public String getEnv() {
        return this.env;
        }

        public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        }

        public String getOrderNo() {
        return this.orderNo;
        }

        public void setDistributorOrderNo(String distributorOrderNo) {
        this.distributorOrderNo = distributorOrderNo;
        }

        public String getDistributorOrderNo() {
        return this.distributorOrderNo;
        }

        public void setDistributorMainOrderNo(String distributorMainOrderNo) {
        this.distributorMainOrderNo = distributorMainOrderNo;
        }

        public String getDistributorMainOrderNo() {
        return this.distributorMainOrderNo;
        }

        public void setDriverTripNo(String driverTripNo) {
        this.driverTripNo = driverTripNo;
        }

        public String getDriverTripNo() {
        return this.driverTripNo;
        }

        public void setSource(Integer source) {
        this.source = source;
        }

        public Integer getSource() {
        return this.source;
        }

        public void setHitchPercent(Integer hitchPercent) {
        this.hitchPercent = hitchPercent;
        }

        public Integer getHitchPercent() {
        return this.hitchPercent;
        }

        public void setStartCityId(Long startCityId) {
        this.startCityId = startCityId;
        }

        public Long getStartCityId() {
        return this.startCityId;
        }

        public void setEndCityId(Long endCityId) {
        this.endCityId = endCityId;
        }

        public Long getEndCityId() {
        return this.endCityId;
        }

        public void setStartCity(String startCity) {
        this.startCity = startCity;
        }

        public String getStartCity() {
        return this.startCity;
        }

        public void setEndCity(String endCity) {
        this.endCity = endCity;
        }

        public String getEndCity() {
        return this.endCity;
        }

        public void setStartDistrictCode(String startDistrictCode) {
        this.startDistrictCode = startDistrictCode;
        }

        public String getStartDistrictCode() {
        return this.startDistrictCode;
        }

        public void setStartDistrict(String startDistrict) {
        this.startDistrict = startDistrict;
        }

        public String getStartDistrict() {
        return this.startDistrict;
        }

        public void setEndDistrictCode(String endDistrictCode) {
        this.endDistrictCode = endDistrictCode;
        }

        public String getEndDistrictCode() {
        return this.endDistrictCode;
        }

        public void setEndDistrict(String endDistrict) {
        this.endDistrict = endDistrict;
        }

        public String getEndDistrict() {
        return this.endDistrict;
        }

        public void setStartingAdd(String startingAdd) {
        this.startingAdd = startingAdd;
        }

        public String getStartingAdd() {
        return this.startingAdd;
        }

        public void setEndingAdd(String endingAdd) {
        this.endingAdd = endingAdd;
        }

        public String getEndingAdd() {
        return this.endingAdd;
        }

        public void setStartingLon(BigDecimal startingLon) {
        this.startingLon = startingLon;
        }

        public BigDecimal getStartingLon() {
        return this.startingLon;
        }

        public void setStartingLat(BigDecimal startingLat) {
        this.startingLat = startingLat;
        }

        public BigDecimal getStartingLat() {
        return this.startingLat;
        }

        public void setEndingLon(BigDecimal endingLon) {
        this.endingLon = endingLon;
        }

        public BigDecimal getEndingLon() {
        return this.endingLon;
        }

        public void setEndingLat(BigDecimal endingLat) {
        this.endingLat = endingLat;
        }

        public BigDecimal getEndingLat() {
        return this.endingLat;
        }

        public void setStartingTime(Date startingTime) {
        this.startingTime = startingTime;
        }

        public Date getStartingTime() {
        return this.startingTime;
        }

        public void setLastStartingTime(Date lastStartingTime) {
        this.lastStartingTime = lastStartingTime;
        }

        public Date getLastStartingTime() {
        return this.lastStartingTime;
        }

        public void setTelephone(String telephone) {
        this.telephone = telephone;
        }

        public String getTelephone() {
        return this.telephone;
        }

        public void setRealTelephone(String realTelephone) {
        this.realTelephone = realTelephone;
        }

        public String getRealTelephone() {
        return this.realTelephone;
        }

        public void setPassengerCount(Integer passengerCount) {
        this.passengerCount = passengerCount;
        }

        public Integer getPassengerCount() {
        return this.passengerCount;
        }

        public void setAmount(BigDecimal amount) {
        this.amount = amount;
        }

        public BigDecimal getAmount() {
        return this.amount;
        }

        public void setPayPrice(BigDecimal payPrice) {
        this.payPrice = payPrice;
        }

        public BigDecimal getPayPrice() {
        return this.payPrice;
        }

        public void setStatus(Integer status) {
        this.status = status;
        }

        public Integer getStatus() {
        return this.status;
        }

        public void setPayStatus(Integer payStatus) {
        this.payStatus = payStatus;
        }

        public Integer getPayStatus() {
        return this.payStatus;
        }

        public void setPayType(Integer payType) {
        this.payType = payType;
        }

        public Integer getPayType() {
        return this.payType;
        }

        public void setRefId(String refId) {
        this.refId = refId;
        }

        public String getRefId() {
        return this.refId;
        }

        public void setOrderType(Integer orderType) {
        this.orderType = orderType;
        }

        public Integer getOrderType() {
        return this.orderType;
        }

        public void setMemberId(Long memberId) {
        this.memberId = memberId;
        }

        public Long getMemberId() {
        return this.memberId;
        }

        public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
        }

        public String getVehicleNo() {
        return this.vehicleNo;
        }

        public void setDriverName(String driverName) {
        this.driverName = driverName;
        }

        public String getDriverName() {
        return this.driverName;
        }

        public void setDriverMobile(String driverMobile) {
        this.driverMobile = driverMobile;
        }

        public String getDriverMobile() {
        return this.driverMobile;
        }

        public void setDriverId(Long driverId) {
        this.driverId = driverId;
        }

        public Long getDriverId() {
        return this.driverId;
        }

        public void setBookTime(Date bookTime) {
        this.bookTime = bookTime;
        }

        public Date getBookTime() {
        return this.bookTime;
        }

        public void setCreateUser(String createUser) {
        this.createUser = createUser;
        }

        public String getCreateUser() {
        return this.createUser;
        }

        public void setCreateTime(Date createTime) {
        this.createTime = createTime;
        }

        public Date getCreateTime() {
        return this.createTime;
        }

        public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        }

        public String getUpdateUser() {
        return this.updateUser;
        }

        public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        }

        public Date getUpdateTime() {
        return this.updateTime;
        }

        public void setRemark(String remark) {
        this.remark = remark;
        }

        public String getRemark() {
        return this.remark;
        }

        public void setStartingAddDetail(String startingAddDetail) {
        this.startingAddDetail = startingAddDetail;
        }

        public String getStartingAddDetail() {
        return this.startingAddDetail;
        }

        public void setEndingAddDetail(String endingAddDetail) {
        this.endingAddDetail = endingAddDetail;
        }

        public String getEndingAddDetail() {
        return this.endingAddDetail;
        }

        public void setDistance(BigDecimal distance) {
        this.distance = distance;
        }

        public BigDecimal getDistance() {
        return this.distance;
        }

        public void setDuration(Integer duration) {
        this.duration = duration;
        }

        public Integer getDuration() {
        return this.duration;
        }

        public void setOrderCommissionRatio(BigDecimal orderCommissionRatio) {
        this.orderCommissionRatio = orderCommissionRatio;
        }

        public BigDecimal getOrderCommissionRatio() {
        return this.orderCommissionRatio;
        }

        public void setOrderCommissionCap(BigDecimal orderCommissionCap) {
        this.orderCommissionCap = orderCommissionCap;
        }

        public BigDecimal getOrderCommissionCap() {
        return this.orderCommissionCap;
        }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public Date getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(Date finishTime) {
        this.finishTime = finishTime;
    }

    public String toString() {
return new ToStringBuilder(this)
    .append("Id",getId())
    .append("Env",getEnv())
    .append("OrderNo",getOrderNo())
    .append("DistributorOrderNo",getDistributorOrderNo())
    .append("DistributorMainOrderNo",getDistributorMainOrderNo())
    .append("DriverTripNo",getDriverTripNo())
    .append("Source",getSource())
    .append("HitchPercent",getHitchPercent())
    .append("StartCityId",getStartCityId())
    .append("EndCityId",getEndCityId())
    .append("StartCity",getStartCity())
    .append("EndCity",getEndCity())
    .append("StartDistrictCode",getStartDistrictCode())
    .append("StartDistrict",getStartDistrict())
    .append("EndDistrictCode",getEndDistrictCode())
    .append("EndDistrict",getEndDistrict())
    .append("StartingAdd",getStartingAdd())
    .append("EndingAdd",getEndingAdd())
    .append("StartingLon",getStartingLon())
    .append("StartingLat",getStartingLat())
    .append("EndingLon",getEndingLon())
    .append("EndingLat",getEndingLat())
    .append("StartingTime",getStartingTime())
    .append("LastStartingTime",getLastStartingTime())
    .append("Telephone",getTelephone())
    .append("RealTelephone",getRealTelephone())
    .append("PassengerCount",getPassengerCount())
    .append("Amount",getAmount())
    .append("PayPrice",getPayPrice())
    .append("Status",getStatus())
    .append("PayStatus",getPayStatus())
    .append("PayType",getPayType())
    .append("RefId",getRefId())
    .append("OrderType",getOrderType())
    .append("MemberId",getMemberId())
    .append("VehicleNo",getVehicleNo())
    .append("DriverName",getDriverName())
    .append("DriverMobile",getDriverMobile())
    .append("DriverId",getDriverId())
    .append("BookTime",getBookTime())
    .append("CreateUser",getCreateUser())
    .append("CreateTime",getCreateTime())
    .append("UpdateUser",getUpdateUser())
    .append("UpdateTime",getUpdateTime())
    .append("Remark",getRemark())
    .append("StartingAddDetail",getStartingAddDetail())
    .append("EndingAddDetail",getEndingAddDetail())
    .append("Distance",getDistance())
    .append("Duration",getDuration())
    .append("OrderCommissionRatio",getOrderCommissionRatio())
    .append("OrderCommissionCap",getOrderCommissionCap())
    .append("PayTime",getPayTime())
    .append("FinishTime",getFinishTime())
.toString();
}

public int hashCode() {
return new HashCodeBuilder()
    .append(getId())
.toHashCode();
}

public boolean equals(Object obj) {
if(obj == null) return false;
if(this == obj) return true;
if(obj instanceof RideOrderInfoDO == false) return false;
RideOrderInfoDO other = (RideOrderInfoDO)obj;
return new EqualsBuilder()
    .append(getId(),other.getId())
.isEquals();
}
}

