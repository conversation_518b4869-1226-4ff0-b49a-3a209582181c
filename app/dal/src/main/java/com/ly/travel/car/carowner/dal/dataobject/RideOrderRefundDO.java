/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.ly.travel.car.carowner.dal.dataobject;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;


import java.io.*;
import java.math.BigDecimal;
import java.net.*;
import java.util.*;

import java.math.BigInteger;

import com.ly.sof.utils.page.PageQuery;
import com.ly.sof.utils.page.PageList;
import com.ly.sof.utils.page.PageQueryUtils;
/**
* RideOrderRefundDO
 * database table: ride_order_refund
 * database table comments: RideOrderRefund
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 *
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to
 * directory <tt>(project-home)/dalgen</tt>
 *
 * <AUTHOR> Wang(Wang Junxiang)
 **/
@TableName("ride_order_refund")
public class RideOrderRefundDO implements java.io.Serializable {
private static final long serialVersionUID = -5216457518046898601L;

    /**
    * 自增id        db_column: id
    */

        @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
    * 环境标识        db_column: env
    */

        @TableField(value = "env")
    private String env;
    /**
    * 订单编号        db_column: order_no
    */

        @TableField(value = "order_no")
    private String orderNo;
    /**
    * 退款金额        db_column: refund_amount
    */

        @TableField(value = "refund_amount")
    private BigDecimal refundAmount;
    /**
    * 退款状态- 0:退款, 1:退款成功, 2:退款失败        db_column: refund_status
    */

        @TableField(value = "refund_status")
    private Integer refundStatus;
    /**
    * 退款原因备注        db_column: refund_remark
    */

        @TableField(value = "refund_remark")
    private String refundRemark;
    /**
    * 退款时间        db_column: refund_time
    */

        @TableField(value = "refund_time")
    private Date refundTime;
    /**
    * 创建人        db_column: create_user
    */

        @TableField(value = "create_user")
    private String createUser;
    /**
    * 创建时间        db_column: create_time
    */

        @TableField(value = "create_time")
    private Date createTime;
    /**
    * 备注        db_column: remark
    */

        @TableField(value = "remark")
    private String remark;


        public void setId(Long id) {
        this.id = id;
        }

        public Long getId() {
        return this.id;
        }

        public void setEnv(String env) {
        this.env = env;
        }

        public String getEnv() {
        return this.env;
        }

        public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        }

        public String getOrderNo() {
        return this.orderNo;
        }

        public void setRefundAmount(BigDecimal refundAmount) {
        this.refundAmount = refundAmount;
        }

        public BigDecimal getRefundAmount() {
        return this.refundAmount;
        }

        public void setRefundStatus(Integer refundStatus) {
        this.refundStatus = refundStatus;
        }

        public Integer getRefundStatus() {
        return this.refundStatus;
        }

        public void setRefundRemark(String refundRemark) {
        this.refundRemark = refundRemark;
        }

        public String getRefundRemark() {
        return this.refundRemark;
        }

        public void setRefundTime(Date refundTime) {
        this.refundTime = refundTime;
        }

        public Date getRefundTime() {
        return this.refundTime;
        }

        public void setCreateUser(String createUser) {
        this.createUser = createUser;
        }

        public String getCreateUser() {
        return this.createUser;
        }

        public void setCreateTime(Date createTime) {
        this.createTime = createTime;
        }

        public Date getCreateTime() {
        return this.createTime;
        }

        public void setRemark(String remark) {
        this.remark = remark;
        }

        public String getRemark() {
        return this.remark;
        }

public String toString() {
return new ToStringBuilder(this)
    .append("Id",getId())
    .append("Env",getEnv())
    .append("OrderNo",getOrderNo())
    .append("RefundAmount",getRefundAmount())
    .append("RefundStatus",getRefundStatus())
    .append("RefundRemark",getRefundRemark())
    .append("RefundTime",getRefundTime())
    .append("CreateUser",getCreateUser())
    .append("CreateTime",getCreateTime())
    .append("Remark",getRemark())
.toString();
}

public int hashCode() {
return new HashCodeBuilder()
    .append(getId())
.toHashCode();
}

public boolean equals(Object obj) {
if(obj == null) return false;
if(this == obj) return true;
if(obj instanceof RideOrderRefundDO == false) return false;
RideOrderRefundDO other = (RideOrderRefundDO)obj;
return new EqualsBuilder()
    .append(getId(),other.getId())
.isEquals();
}
}

