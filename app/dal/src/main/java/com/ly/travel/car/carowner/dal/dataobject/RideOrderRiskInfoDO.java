/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.ly.travel.car.carowner.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.Date;

/**
* RideOrderRiskInfoDO
 * database table: ride_order_risk_info
 * database table comments: RideOrderRiskInfo
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 *
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to
 * directory <tt>(project-home)/dalgen</tt>
 *
 * <AUTHOR> <PERSON>)
 **/
@TableName("ride_order_risk_info")
public class RideOrderRiskInfoDO implements Serializable {
private static final long serialVersionUID = -5216457518046898601L;

    /**
    * 主键        db_column: id
    */

        @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
    * 订单号        db_column: order_no
    */

        @TableField(value = "order_no")
    private String orderNo;
    /**
    * 环境        db_column: env
    */

        @TableField(value = "env")
    private String env;
    /**
    * 是否冻结 0-否 1-是        db_column: is_freeze
    */

        @TableField(value = "is_freeze")
    private Integer isFreeze;
    /**
    * 解冻时间        db_column: unfreeze_time
    */

        @TableField(value = "unfreeze_time")
    private Date unfreezeTime;
    /**
    * 创建时间        db_column: create_time
    */

        @TableField(value = "create_time")
    private Date createTime;
    /**
    * 修改时间        db_column: update_time
    */

        @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 提现风控标识 0-正常  1-提现后风控        db_column: withdrawal_risk_status
     */

    @TableField(value = "withdrawal_risk_status")
    private Integer withdrawalRiskStatus;


        public void setId(Long id) {
        this.id = id;
        }

        public Long getId() {
        return this.id;
        }

        public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        }

        public String getOrderNo() {
        return this.orderNo;
        }

        public void setEnv(String env) {
        this.env = env;
        }

        public String getEnv() {
        return this.env;
        }

        public void setIsFreeze(Integer isFreeze) {
        this.isFreeze = isFreeze;
        }

        public Integer getIsFreeze() {
        return this.isFreeze;
        }

        public void setUnfreezeTime(Date unfreezeTime) {
        this.unfreezeTime = unfreezeTime;
        }

        public Date getUnfreezeTime() {
        return this.unfreezeTime;
        }

        public void setCreateTime(Date createTime) {
        this.createTime = createTime;
        }

        public Date getCreateTime() {
        return this.createTime;
        }

        public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        }

        public Date getUpdateTime() {
        return this.updateTime;
        }

        public void setWithdrawalRiskStatus(Integer withdrawalRiskStatus) {
            this.withdrawalRiskStatus = withdrawalRiskStatus;
        }
        public Integer getWithdrawalRiskStatus() {
            return this.withdrawalRiskStatus;
        }

public String toString() {
return new ToStringBuilder(this)
    .append("Id",getId())
    .append("OrderNo",getOrderNo())
    .append("Env",getEnv())
    .append("IsFreeze",getIsFreeze())
    .append("UnfreezeTime",getUnfreezeTime())
    .append("CreateTime",getCreateTime())
    .append("UpdateTime",getUpdateTime())
.toString();
}

public int hashCode() {
return new HashCodeBuilder()
    .append(getId())
.toHashCode();
}

public boolean equals(Object obj) {
if(obj == null) return false;
if(this == obj) return true;
if(obj instanceof RideOrderRiskInfoDO == false) return false;
RideOrderRiskInfoDO other = (RideOrderRiskInfoDO)obj;
return new EqualsBuilder()
    .append(getId(),other.getId())
.isEquals();
}
}

