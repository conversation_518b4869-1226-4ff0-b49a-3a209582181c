/**
 * LY.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.ly.travel.car.carowner.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * RideOrderRiskLogDO
 * database table: ride_order_risk_log
 * database table comments: RideOrderRiskLog
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * <p>
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to
 * directory <tt>(project-home)/dalgen</tt>
 *
 * <AUTHOR>
 */
@Data
@TableName("ride_order_risk_log")
public class RideOrderRiskLogDO implements Serializable {
    private static final long serialVersionUID = -5216457518046898601L;

    /**
     * id 		db_column: id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 订单 		db_column: order_no
     */
    @TableField(value = "order_no")
    private String orderNo;
    /**
     * 场景：1-提现 		db_column: scene
     */
    @TableField(value = "scene")
    private Integer scene;
    /**
     * 风控状态：1-命中风控 0-未命中风控 		db_column: state
     */
    @TableField(value = "state")
    private Integer state;
    /**
     * 风控内容 		db_column: content
     */
    @TableField(value = "content")
    private String content;
    /**
     * 风控策略 		db_column: ext
     */
    @TableField(value = "ext")
    private String ext;
    /**
     * 环境 		db_column: env
     */
    @TableField(value = "env")
    private String env;
    /**
     * 操作人 		db_column: operator
     */
    @TableField(value = "operator")
    private String operator;
    /**
     * 操作时间 		db_column: gmt_operated
     */
    @TableField(value = "gmt_operated")
    private Date gmtOperated;

    /**
     * 解冻时间 		db_column: freeze_time
     */
    @TableField(value = "freeze_time")
    private Date freezeTime;
}

