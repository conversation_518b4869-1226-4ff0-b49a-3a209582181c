/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.ly.travel.car.carowner.dal.dataobject;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;


import java.io.*;
import java.math.BigDecimal;
import java.net.*;
import java.util.*;

import java.math.BigInteger;

import com.ly.sof.utils.page.PageQuery;
import com.ly.sof.utils.page.PageList;
import com.ly.sof.utils.page.PageQueryUtils;
/**
* RideOrderSettlementDO
 * database table: ride_order_settlement
 * database table comments: RideOrderSettlement
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 *
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to
 * directory <tt>(project-home)/dalgen</tt>
 *
 * <AUTHOR> Wang(Wang Junxiang)
 **/
@TableName("ride_order_settlement")
public class RideOrderSettlementDO implements java.io.Serializable {

    /**
    * 主键        db_column: id
    */

        @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
    * 订单号        db_column: order_no
    */

        @TableField(value = "order_no")
    private String orderNo;
    /**
    * 司机ID        db_column: driver_id
    */

        @TableField(value = "driver_id")
    private Long driverId;
    /**
    * 供应商结算价        db_column: supplier_settlement_price
    */

        @TableField(value = "supplier_settlement_price")
    private BigDecimal supplierSettlementPrice;
    /**
    * 平台结算收益        db_column: platform_settlement_price
    */

        @TableField(value = "platform_settlement_price")
    private BigDecimal platformSettlementPrice;
    /**
    * 供应商分佣比例        db_column: supplier_maid_rate
    */

        @TableField(value = "supplier_maid_rate")
    private BigDecimal supplierMaidRate;
    /**
    * 平台分佣比例        db_column: platform_maid_rate
    */

        @TableField(value = "platform_maid_rate")
    private BigDecimal platformMaidRate;
    /**
    * 司机结算价        db_column: driver_settlement_price
    */

        @TableField(value = "driver_settlement_price")
    private BigDecimal driverSettlementPrice;

    @TableField(value = "old_driver_settlement_price")
    private BigDecimal oldDriverSettlementPrice;
    /**
    * 创建人        db_column: create_user
    */

        @TableField(value = "create_user")
    private String createUser;
    /**
    * 创建时间        db_column: create_time
    */

        @TableField(value = "create_time")
    private Date createTime;
    /**
    * 修改人        db_column: update_user
    */

        @TableField(value = "update_user")
    private String updateUser;
    /**
    * 修改时间        db_column: update_time
    */

        @TableField(value = "update_time")
    private Date updateTime;
    /**
    * 状态        db_column: status
    */

        @TableField(value = "status")
    private Integer status;

    @TableField(value = "supplier_limit_max_amount")
    private  BigDecimal supplierLimitMaxAmount;

    @TableField(value = "driver_extra_settlement_price")
    private  BigDecimal driverExtraSettlementPrice;

    public BigDecimal getDriverExtraSettlementPrice() {
        return driverExtraSettlementPrice;
    }

    public void setDriverExtraSettlementPrice(BigDecimal driverExtraSettlementPrice) {
        this.driverExtraSettlementPrice = driverExtraSettlementPrice;
    }

    public BigDecimal getSupplierLimitMaxAmount() {
        return supplierLimitMaxAmount;
    }

    public void setSupplierLimitMaxAmount(BigDecimal supplierLimitMaxAmount) {
        this.supplierLimitMaxAmount = supplierLimitMaxAmount;
    }

    public BigDecimal getOldDriverSettlementPrice() {
        return oldDriverSettlementPrice;
    }

    public void setOldDriverSettlementPrice(BigDecimal oldDriverSettlementPrice) {
        this.oldDriverSettlementPrice = oldDriverSettlementPrice;
    }

    public void setId(Long id) {
        this.id = id;
        }

        public Long getId() {
        return this.id;
        }

        public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        }

        public String getOrderNo() {
        return this.orderNo;
        }

        public void setDriverId(Long driverId) {
        this.driverId = driverId;
        }

        public Long getDriverId() {
        return this.driverId;
        }

        public void setSupplierSettlementPrice(BigDecimal supplierSettlementPrice) {
        this.supplierSettlementPrice = supplierSettlementPrice;
        }

        public BigDecimal getSupplierSettlementPrice() {
        return this.supplierSettlementPrice;
        }

        public void setPlatformSettlementPrice(BigDecimal platformSettlementPrice) {
        this.platformSettlementPrice = platformSettlementPrice;
        }

        public BigDecimal getPlatformSettlementPrice() {
        return this.platformSettlementPrice;
        }

        public void setSupplierMaidRate(BigDecimal supplierMaidRate) {
        this.supplierMaidRate = supplierMaidRate;
        }

        public BigDecimal getSupplierMaidRate() {
        return this.supplierMaidRate;
        }

        public void setPlatformMaidRate(BigDecimal platformMaidRate) {
        this.platformMaidRate = platformMaidRate;
        }

        public BigDecimal getPlatformMaidRate() {
        return this.platformMaidRate;
        }

        public void setDriverSettlementPrice(BigDecimal driverSettlementPrice) {
        this.driverSettlementPrice = driverSettlementPrice;
        }

        public BigDecimal getDriverSettlementPrice() {
        return this.driverSettlementPrice;
        }

        public void setCreateUser(String createUser) {
        this.createUser = createUser;
        }

        public String getCreateUser() {
        return this.createUser;
        }

        public void setCreateTime(Date createTime) {
        this.createTime = createTime;
        }

        public Date getCreateTime() {
        return this.createTime;
        }

        public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        }

        public String getUpdateUser() {
        return this.updateUser;
        }

        public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        }

        public Date getUpdateTime() {
        return this.updateTime;
        }

        public void setStatus(Integer status) {
        this.status = status;
        }

        public Integer getStatus() {
        return this.status;
        }

public String toString() {
return new ToStringBuilder(this)
    .append("Id",getId())
    .append("OrderNo",getOrderNo())
    .append("DriverId",getDriverId())
    .append("SupplierSettlementPrice",getSupplierSettlementPrice())
    .append("PlatformSettlementPrice",getPlatformSettlementPrice())
    .append("SupplierMaidRate",getSupplierMaidRate())
    .append("PlatformMaidRate",getPlatformMaidRate())
    .append("DriverSettlementPrice",getDriverSettlementPrice())
    .append("OldDriverSettlementPrice",getOldDriverSettlementPrice())
    .append("CreateUser",getCreateUser())
    .append("CreateTime",getCreateTime())
    .append("UpdateUser",getUpdateUser())
    .append("UpdateTime",getUpdateTime())
    .append("Status",getStatus())
    .append("DriverExtraSettlementPrice",getDriverExtraSettlementPrice())
.toString();
}

public int hashCode() {
return new HashCodeBuilder()
    .append(getId())
.toHashCode();
}

public boolean equals(Object obj) {
if(obj == null) return false;
if(this == obj) return true;
if(obj instanceof RideOrderSettlementDO == false) return false;
RideOrderSettlementDO other = (RideOrderSettlementDO)obj;
return new EqualsBuilder()
    .append(getId(),other.getId())
.isEquals();
}
}

