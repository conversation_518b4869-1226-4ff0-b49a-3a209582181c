package com.ly.travel.car.carowner.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @desc RideOrderTripInfoDO
 */
@Data
@TableName("ride_order_trip_info")
public class RideOrderTripInfoDO implements Serializable {

    private static final long serialVersionUID = -5216457518046898601L;

    /**
     * 自增id        db_column: id
     */

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 订单号        db_column: order_no
     */

    @TableField(value = "order_no")
    private String orderNo;
    /**
     * 实际行驶里程        db_column: real_mileage
     */

    @TableField(value = "real_mileage")
    private BigDecimal realMileage;

    /**
     * '司机端订单行驶状态( 1:去接乘客 2:到达接客地 3:上车 4:下车 )'
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 创建人        db_column: create_user
     */

    @TableField(value = "create_user")
    private String createUser;
    /**
     * 创建时间        db_column: create_time
     */

    @TableField(value = "create_time")
    private Date createTime;
    /**
     * 修改人        db_column: update_user
     */

    @TableField(value = "update_user")
    private String updateUser;
    /**
     * 修改时间        db_column: update_time
     */

    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 司机开始去接乘客经度        db_column: start_empty_driving_lon
     */

    @TableField(value = "start_empty_driving_lon")
    private BigDecimal startEmptyDrivingLon;
    /**
     * 司机开始去接乘客纬度        db_column: start_empty_driving_lat
     */

    @TableField(value = "start_empty_driving_lat")
    private BigDecimal startEmptyDrivingLat;

    /**
     * 司机开始去接乘客经度        db_column: getstarting_lat
     */

    @TableField(value = "getstarting_lon")
    private BigDecimal getStartingLon;
    /**
     * 司机开始去接乘客纬度        db_column: getstarting_lat
     */

    @TableField(value = "getstarting_lat")
    private BigDecimal getStartingLat;

    /**
     * 司机开始去接乘客经度        db_column: geton_lon
     */

    @TableField(value = "geton_lon")
    private BigDecimal getOnLon;
    /**
     * 司机开始去接乘客纬度        db_column: geton_lat
     */

    @TableField(value = "geton_lat")
    private BigDecimal getOnLat;

    /**
     * 乘客下车司机经度        db_column: getoff_lon
     */

    @TableField(value = "getoff_lon")
    private BigDecimal getOffLon;
    /**
     * 乘客下车司机纬度        db_column: getoff_lat
     */

    @TableField(value = "getoff_lat")
    private BigDecimal getOffLat;

    /**
     * 乘客下车司机经度        db_column: driver_start_time
     */
    @TableField(value = "driver_start_time")
    private Date driverStartTime;

    /**
     * 乘客下车司机经度        db_column: driver_start_address_time
     */

    @TableField(value = "driver_start_address_time")
    private Date driverStartAddressTime;

    /**
     * 乘客下车司机经度        db_column: driver_receive_passenger_time
     */

    @TableField(value = "driver_receive_passenger_time")
    private Date driverReceivePassengerTime;

    /**
     * 乘客下车司机经度        db_column: driver_sent_passenger_time
     */
    @TableField(value = "driver_sent_passenger_time")
    private Date driverSentPassengerTime;

    /**
     * 自动完单标识  0-不是 ;1-是        db_column: auto_completion_flag
     */

    @TableField(value = "auto_completion_flag")
    private Integer autoCompletionFlag;
}
