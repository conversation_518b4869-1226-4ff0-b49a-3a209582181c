/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.ly.travel.car.carowner.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.Date;

/**
* SysConfigDO
 * database table: sys_config
 * database table comments: SysConfig
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 *
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to
 * directory <tt>(project-home)/dalgen</tt>
 *
 * <AUTHOR>
 **/
@TableName("sys_config")
public class SysConfigDO implements Serializable {
private static final long serialVersionUID = -5216457518046898601L;

    /**
    * 主键ID        db_column: id
    */

        @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
    * 配置项        db_column: cfg_key
    */

        @TableField(value = "cfg_key")
    private String cfgKey;
    /**
    * 值        db_column: cfg_value
    */

        @TableField(value = "cfg_value")
    private String cfgValue;
    /**
    * 状态             0.已删除             1.正常        db_column: status
    */

        @TableField(value = "status")
    private Integer status;
    /**
    * 备注        db_column: remark
    */

        @TableField(value = "remark")
    private String remark;
    /**
    * 创建人        db_column: create_user
    */

        @TableField(value = "create_user")
    private String createUser;
    /**
    * 创建时间        db_column: create_time
    */

        @TableField(value = "create_time")
    private Date createTime;
    /**
    * 修改人        db_column: update_user
    */

        @TableField(value = "update_user")
    private String updateUser;
    /**
    * 修改时间        db_column: update_time
    */

        @TableField(value = "update_time")
    private Date updateTime;
    /**
    * 环境标识        db_column: env
    */

        @TableField(value = "env")
    private String env;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCfgKey() {
        return cfgKey;
    }

    public void setCfgKey(String cfgKey) {
        this.cfgKey = cfgKey;
    }

    public String getCfgValue() {
        return cfgValue;
    }

    public void setCfgValue(String cfgValue) {
        this.cfgValue = cfgValue;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getEnv() {
        return env;
    }

    public void setEnv(String env) {
        this.env = env;
    }

    public String toString() {
return new ToStringBuilder(this)
    .append("Id",getId())
    .append("CfgKey",getCfgKey())
    .append("CfgValue",getCfgValue())
    .append("Status",getStatus())
    .append("Remark",getRemark())
    .append("CreateUser",getCreateUser())
    .append("CreateTime",getCreateTime())
    .append("UpdateUser",getUpdateUser())
    .append("UpdateTime",getUpdateTime())
    .append("Env",getEnv())
.toString();
}

public int hashCode() {
return new HashCodeBuilder()
    .append(getId())
.toHashCode();
}

public boolean equals(Object obj) {
if(obj == null) return false;
if(this == obj) return true;
if(obj instanceof SysConfigDO == false) return false;
SysConfigDO other = (SysConfigDO)obj;
return new EqualsBuilder()
    .append(getId(),other.getId())
.isEquals();
}
}

