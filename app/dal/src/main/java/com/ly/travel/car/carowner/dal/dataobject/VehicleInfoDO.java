/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.ly.travel.car.carowner.dal.dataobject;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.util.*;

/**
 * VehicleInfoDO
 * database table: vehicle_info
 * database table comments: VehicleInfo
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * <p>
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to
 * directory <tt>(project-home)/dalgen</tt>
 *
 * <AUTHOR>
 **/
@TableName("vehicle_info")
public class VehicleInfoDO implements java.io.Serializable {
    private static final long serialVersionUID = -5216457518046898601L;

    /**
     * 自增id        db_column: id
     */

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 环境        db_column: env
     */

    @TableField(value = "env")
    private String env;
    /**
     * 车牌        db_column: vehicle_no
     */

    @TableField(value = "vehicle_no")
    private String vehicleNo;
    /**
     * 司机，默认为0        db_column: driver_id
     */

    @TableField(value = "driver_id")
    private Long driverId;
    /**
     * 车牌号城市        db_column: vehicle_no_city
     */

    @TableField(value = "vehicle_no_city")
    private String vehicleNoCity;
    /**
     * 车辆性质 02小型汽车、52新能源小型车        db_column: vehicle_type
     */

    @TableField(value = "vehicle_type")
    private String vehicleType;
    /**
     * 车辆所有人        db_column: vehicle_owner
     */

    @TableField(value = "vehicle_owner")
    private String vehicleOwner;
    /**
     * 车辆使用性质 非营运、营转非、出租转非、预约出租转非        db_column: use_character
     */

    @TableField(value = "use_character")
    private String useCharacter;
    /**
     * 品牌编号        db_column: car_brand
     */

    @TableField(value = "car_brand")
    private String carBrand;
    /**
     * 车型编号        db_column: car_model
     */

    @TableField(value = "car_model")
    private String carModel;
    /**
     * 车辆识别代号        db_column: vin
     */

    @TableField(value = "vin")
    private String vin;
    /**
     * 颜色        db_column: color
     */

    @TableField(value = "color")
    private String color;
    /**
     * 注册日期        db_column: register_date
     */

    @TableField(value = "register_date")
    private String registerDate;
    /**
     * 发动机号码        db_column: engine_no
     */

    @TableField(value = "engine_no")
    private String engineNo;
    /**
     * 发证日期        db_column: issue_date
     */

    @TableField(value = "issue_date")
    private String issueDate;
    /**
     * 核定座数        db_column: auth_seat_num
     */

    @TableField(value = "auth_seat_num")
    private Integer authSeatNum;
    /**
     * 乘客座数        db_column: pass_seat_num
     */

    @TableField(value = "pass_seat_num")
    private Integer passSeatNum;
    /**
     * 行驶证图片        db_column: driving_license_picture
     */

    @TableField(value = "driving_license_picture")
    private String drivingLicensePicture;
    /**
     * 燃料类型 柴油 汽油 电动 混动        db_column: fuel_type
     */

    @TableField(value = "fuel_type")
    private String fuelType;
    /**
     * 品牌型号 行驶证识别        db_column: brand_model
     */

    @TableField(value = "brand_model")
    private String brandModel;
    /**
     * 车辆状态        db_column: vehicle_status
     */

    @TableField(value = "vehicle_status")
    private String vehicleStatus;
    /**
     * 检验有效期止        db_column: inspect_end_date
     */

    @TableField(value = "inspect_end_date")
    private String inspectEndDate;
    /**
     * 年检日期        db_column: check_date
     */

    @TableField(value = "check_date")
    private String checkDate;
    /**
     * 状态 1.未认证 2.待系统认证 3.待人工认证 4.认证通过 5.认证不通过        db_column: status
     */

    @TableField(value = "status")
    private Integer status;
    /**
     * 风控ocr不通过原因        db_column: verify_remark
     */

    @TableField(value = "verify_remark")
    private String verifyRemark;
    /**
     * 审核不通过原因 1.证件照片不清晰 2.证件照片作假 3.驾驶证已过期 4.准驾车型不符 5.驾龄不满足要求 6.驾驶证状态异常 7.其他        db_column: unaudit_reason
     */

    @TableField(value = "unaudit_reason")
    private Integer unauditReason;
    /**
     * 审核不通过其他原因        db_column: unaudit_remark
     */

    @TableField(value = "unaudit_remark")
    private String unauditRemark;
    /**
     * 车辆照片识别时间        db_column: vehicle_picture_start_date
     */

    @TableField(value = "vehicle_picture_start_date")
    private Date vehiclePictureStartDate;
    /**
     * 车辆照片识别有效时长        db_column: vehicle_valid_period
     */

    @TableField(value = "vehicle_valid_period")
    private Integer vehicleValidPeriod;
    /**
     * 车辆图片        db_column: vehicle_picture
     */

    @TableField(value = "vehicle_picture")
    private String vehiclePicture;
    /**
     * 创建人        db_column: create_user
     */

    @TableField(value = "create_user")
    private String createUser;
    /**
     * 创建时间        db_column: create_time
     */

    @TableField(value = "create_time")
    private Date createTime;
    /**
     * 修改人        db_column: update_user
     */

    @TableField(value = "update_user")
    private String updateUser;
    /**
     * 修改时间        db_column: update_time
     */

    @TableField(value = "update_time")
    private Date updateTime;


    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return this.id;
    }

    public void setEnv(String env) {
        this.env = env;
    }

    public String getEnv() {
        return this.env;
    }

    public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
    }

    public String getVehicleNo() {
        return this.vehicleNo;
    }

    public void setDriverId(Long driverId) {
        this.driverId = driverId;
    }

    public Long getDriverId() {
        return this.driverId;
    }

    public void setVehicleNoCity(String vehicleNoCity) {
        this.vehicleNoCity = vehicleNoCity;
    }

    public String getVehicleNoCity() {
        return this.vehicleNoCity;
    }

    public void setVehicleType(String vehicleType) {
        this.vehicleType = vehicleType;
    }

    public String getVehicleType() {
        return this.vehicleType;
    }

    public void setVehicleOwner(String vehicleOwner) {
        this.vehicleOwner = vehicleOwner;
    }

    public String getVehicleOwner() {
        return this.vehicleOwner;
    }

    public void setUseCharacter(String useCharacter) {
        this.useCharacter = useCharacter;
    }

    public String getUseCharacter() {
        return this.useCharacter;
    }

    public void setCarBrand(String carBrand) {
        this.carBrand = carBrand;
    }

    public String getCarBrand() {
        return this.carBrand;
    }

    public void setCarModel(String carModel) {
        this.carModel = carModel;
    }

    public String getCarModel() {
        return this.carModel;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getVin() {
        return this.vin;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getColor() {
        return this.color;
    }

    public void setRegisterDate(String registerDate) {
        this.registerDate = registerDate;
    }

    public String getRegisterDate() {
        return this.registerDate;
    }

    public void setEngineNo(String engineNo) {
        this.engineNo = engineNo;
    }

    public String getEngineNo() {
        return this.engineNo;
    }

    public void setIssueDate(String issueDate) {
        this.issueDate = issueDate;
    }

    public String getIssueDate() {
        return this.issueDate;
    }

    public void setAuthSeatNum(Integer authSeatNum) {
        this.authSeatNum = authSeatNum;
    }

    public Integer getAuthSeatNum() {
        return this.authSeatNum;
    }

    public void setPassSeatNum(Integer passSeatNum) {
        this.passSeatNum = passSeatNum;
    }

    public Integer getPassSeatNum() {
        return this.passSeatNum;
    }

    public void setDrivingLicensePicture(String drivingLicensePicture) {
        this.drivingLicensePicture = drivingLicensePicture;
    }

    public String getDrivingLicensePicture() {
        return this.drivingLicensePicture;
    }

    public void setFuelType(String fuelType) {
        this.fuelType = fuelType;
    }

    public String getFuelType() {
        return this.fuelType;
    }

    public void setBrandModel(String brandModel) {
        this.brandModel = brandModel;
    }

    public String getBrandModel() {
        return this.brandModel;
    }

    public void setVehicleStatus(String vehicleStatus) {
        this.vehicleStatus = vehicleStatus;
    }

    public String getVehicleStatus() {
        return this.vehicleStatus;
    }

    public void setInspectEndDate(String inspectEndDate) {
        this.inspectEndDate = inspectEndDate;
    }

    public String getInspectEndDate() {
        return this.inspectEndDate;
    }

    public void setCheckDate(String checkDate) {
        this.checkDate = checkDate;
    }

    public String getCheckDate() {
        return this.checkDate;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getStatus() {
        return this.status;
    }

    public void setVerifyRemark(String verifyRemark) {
        this.verifyRemark = verifyRemark;
    }

    public String getVerifyRemark() {
        return this.verifyRemark;
    }

    public void setUnauditReason(Integer unauditReason) {
        this.unauditReason = unauditReason;
    }

    public Integer getUnauditReason() {
        return this.unauditReason;
    }

    public void setUnauditRemark(String unauditRemark) {
        this.unauditRemark = unauditRemark;
    }

    public String getUnauditRemark() {
        return this.unauditRemark;
    }

    public void setVehiclePictureStartDate(Date vehiclePictureStartDate) {
        this.vehiclePictureStartDate = vehiclePictureStartDate;
    }

    public Date getVehiclePictureStartDate() {
        return this.vehiclePictureStartDate;
    }

    public void setVehicleValidPeriod(Integer vehicleValidPeriod) {
        this.vehicleValidPeriod = vehicleValidPeriod;
    }

    public Integer getVehicleValidPeriod() {
        return this.vehicleValidPeriod;
    }

    public void setVehiclePicture(String vehiclePicture) {
        this.vehiclePicture = vehiclePicture;
    }

    public String getVehiclePicture() {
        return this.vehiclePicture;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getCreateUser() {
        return this.createUser;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return this.createTime;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public String getUpdateUser() {
        return this.updateUser;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getUpdateTime() {
        return this.updateTime;
    }

    public String toString() {
        return new ToStringBuilder(this)
                .append("Id", getId())
                .append("Env", getEnv())
                .append("VehicleNo", getVehicleNo())
                .append("DriverId", getDriverId())
                .append("VehicleNoCity", getVehicleNoCity())
                .append("VehicleType", getVehicleType())
                .append("VehicleOwner", getVehicleOwner())
                .append("UseCharacter", getUseCharacter())
                .append("CarBrand", getCarBrand())
                .append("CarModel", getCarModel())
                .append("Vin", getVin())
                .append("Color", getColor())
                .append("RegisterDate", getRegisterDate())
                .append("EngineNo", getEngineNo())
                .append("IssueDate", getIssueDate())
                .append("AuthSeatNum", getAuthSeatNum())
                .append("PassSeatNum", getPassSeatNum())
                .append("DrivingLicensePicture", getDrivingLicensePicture())
                .append("FuelType", getFuelType())
                .append("BrandModel", getBrandModel())
                .append("VehicleStatus", getVehicleStatus())
                .append("InspectEndDate", getInspectEndDate())
                .append("CheckDate", getCheckDate())
                .append("Status", getStatus())
                .append("VerifyRemark", getVerifyRemark())
                .append("UnauditReason", getUnauditReason())
                .append("UnauditRemark", getUnauditRemark())
                .append("VehiclePictureStartDate", getVehiclePictureStartDate())
                .append("VehicleValidPeriod", getVehicleValidPeriod())
                .append("VehiclePicture", getVehiclePicture())
                .append("CreateUser", getCreateUser())
                .append("CreateTime", getCreateTime())
                .append("UpdateUser", getUpdateUser())
                .append("UpdateTime", getUpdateTime())
                .toString();
    }

    public int hashCode() {
        return new HashCodeBuilder()
                .append(getId())
                .toHashCode();
    }

    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (this == obj) return true;
        if (obj instanceof VehicleInfoDO == false) return false;
        VehicleInfoDO other = (VehicleInfoDO) obj;
        return new EqualsBuilder()
                .append(getId(), other.getId())
                .isEquals();
    }
}

