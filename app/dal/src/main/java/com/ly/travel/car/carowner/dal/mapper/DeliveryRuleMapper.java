/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.ly.travel.car.carowner.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ly.travel.car.carowner.dal.dataobject.DeliveryRuleDO;

/**
 * DeliveryRuleMapper
 * database table: delivery_rules
 * database table comments: DeliveryRule
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * <p>
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to
 * directory <tt>(project-home)/dalgen</tt>
 *
 * <AUTHOR>
 **/

public interface DeliveryRuleMapper extends BaseMapper
        <DeliveryRuleDO> {
}


