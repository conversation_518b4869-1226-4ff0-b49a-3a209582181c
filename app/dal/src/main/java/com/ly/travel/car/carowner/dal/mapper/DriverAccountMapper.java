/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.ly.travel.car.carowner.dal.mapper;

import org.springframework.dao.DataAccessException;
import com.ly.travel.car.carowner.dal.operation.driveraccount.*;
import com.ly.travel.car.carowner.dal.dataobject.*;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.io.*;
import java.net.*;
import java.util.*;

import java.math.BigInteger;

import com.ly.sof.utils.page.PageQuery;
import com.ly.sof.utils.page.PageList;
import com.ly.sof.utils.page.PageQueryUtils;

/**
 * DriverAccountMapper
 * database table: driver_account
 * database table comments: DriverAccount
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * <p>
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to
 * directory <tt>(project-home)/dalgen</tt>
 *
 * <AUTHOR> <PERSON>)
 **/

public interface DriverAccountMapper extends BaseMapper
        <DriverAccountDO> {
    int updateAmountByDriverId(DriverAccountDO driverAccount);
}


