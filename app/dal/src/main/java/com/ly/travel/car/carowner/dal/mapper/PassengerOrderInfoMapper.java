/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */package com.ly.travel.car.carowner.dal.mapper;
import org.springframework.dao.DataAccessException;
import com.ly.travel.car.carowner.dal.operation.passengerorderinfo.*;
import com.ly.travel.car.carowner.dal.dataobject.*;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.io.*;
import java.net.*;
import java.util.*;

import java.math.BigInteger;

import com.ly.sof.utils.page.PageQuery;
import com.ly.sof.utils.page.PageList;
import com.ly.sof.utils.page.PageQueryUtils;
/**
* PassengerOrderInfoMapper
 * database table: passenger_order_info
 * database table comments: PassengerOrderInfo
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to 
 * directory <tt>(project-home)/dalgen</tt>
 * 
 * <AUTHOR> <PERSON>xiang)
 **/

public interface PassengerOrderInfoMapper extends BaseMapper
<PassengerOrderInfoDO> {
    }


