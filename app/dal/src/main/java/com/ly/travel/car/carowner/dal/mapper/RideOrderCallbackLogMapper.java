package com.ly.travel.car.carowner.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ly.travel.car.carowner.dal.dataobject.RideOrderCallbackLogDO;

/**
* RideOrderCancelMapper
 * database table: ride_order_cancel
 * database table comments: RideOrderCancel
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to 
 * directory <tt>(project-home)/dalgen</tt>
 * 
 * <AUTHOR>
 **/

public interface RideOrderCallbackLogMapper extends BaseMapper<RideOrderCallbackLogDO> {

}
