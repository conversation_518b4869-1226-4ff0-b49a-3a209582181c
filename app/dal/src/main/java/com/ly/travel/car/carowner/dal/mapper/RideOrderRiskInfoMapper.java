/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */package com.ly.travel.car.carowner.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ly.travel.car.carowner.dal.dataobject.RideOrderRiskInfoDO;

/**
* RideOrderRiskInfoMapper
 * database table: ride_order_risk_info
 * database table comments: RideOrderRiskInfo
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to 
 * directory <tt>(project-home)/dalgen</tt>
 * 
 * <AUTHOR> <PERSON>)
 **/

public interface RideOrderRiskInfoMapper extends BaseMapper
<RideOrderRiskInfoDO> {
    }


