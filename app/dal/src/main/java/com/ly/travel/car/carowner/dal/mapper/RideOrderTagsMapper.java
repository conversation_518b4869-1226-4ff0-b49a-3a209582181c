/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */package com.ly.travel.car.carowner.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ly.travel.car.carowner.dal.dataobject.RideOrderLogDO;
import com.ly.travel.car.carowner.dal.dataobject.RideOrderTagsDO;

/**
* DriverBillMapper
 * database table: driver_bill
 * database table comments: DriverBill
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 *
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to
 * directory <tt>(project-home)/dalgen</tt>
 *
 * <AUTHOR>
 **/

public interface RideOrderTagsMapper extends BaseMapper
<RideOrderTagsDO> {
    }


