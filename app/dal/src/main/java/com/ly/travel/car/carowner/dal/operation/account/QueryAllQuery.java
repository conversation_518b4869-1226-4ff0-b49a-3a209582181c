
/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.ly.travel.car.carowner.dal.operation.account;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;


import java.io.*;
import java.net.*;
import java.util.*;

import java.math.BigInteger;

import com.ly.sof.utils.page.PageQuery;
import com.ly.sof.utils.page.PageList;
import com.ly.sof.utils.page.PageQueryUtils;
/**
 * database table: account
 * database table comments: Account
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to 
 * directory <tt>(project-home)/dalgen</tt>
 * 
 * <AUTHOR>
 **/
public class QueryAllQuery  implements java.io.Serializable {
private static final long serialVersionUID = -5216457518046898601L;

    /** 自增id */
    private Long id;
    /** 账户 */
    private String vcode;
    /** 公司全称 */
    private String companyName;
    /** 公司简称 */
    private String companyShortName;
    /** 到期日期 */
    private java.util.Date expirationDateStart;
    /** 到期日期 */
    private java.util.Date expirationDateEnd;
    /** 子域名 */
    private String domain;
    /** 联系地址 */
    private String address;
    /** 联系人 */
    private String contact;
    /** 联系电话 */
    private String telephone;
    /** 是否可提现 0-不可提现 1-可提现 */
    private Integer isWithdrawable;
    /** 创建人 */
    private String createUser;
    /** 创建时间 */
    private java.util.Date createTimeStart;
    /** 创建时间 */
    private java.util.Date createTimeEnd;
    /** 修改人 */
    private String updateUser;
    /** 修改时间 */
    private java.util.Date updateTimeStart;
    /** 修改时间 */
    private java.util.Date updateTimeEnd;
    /** 状态 0 已删除 1 正常 2 停用 3 未审核 4 审核未通过(驳回)             0 已删除             1 正常             2 停用 */
    private Integer status;
    /** 邮箱 */
    private String email;
    /** 开通产品编号 */
    private String productId;
    /** 用户点评分 */
    private Double commentScore;
    /** 是否提供电子发票 0: 不提供 1:提供 */
    private Integer hasElectronicInvoice;
    /** 帐号标签1：管理员，2：供应商帐号 */
    private Integer accountLabel;
    /** 保费承担人 0：不承担 1：承担 */
    private Integer isAssumeInsurance;
    /** 是否支持司机自主注册 0不支持1支持 */
    private Integer isSupportRandom;
    /** 计价策略表Id */
    private Long priceStrategyId;
    /** 客服电话 */
    private String customServicePhoneNo;
    /** 公司税号 */
    private String companyEin;
    /** 驳回意见(审核结果) */
    private String checkText;
    /** 合作推荐人 */
    private String referees;
    /** 营业执照 */
    private String businessLicense;
    /** 司机端抢单开关 0关闭 1开启 */
    private Integer driverRobOrderSwitch;
    /** 取消手续费开关 */
    private Integer cancelfeeCommissionSwitch;
    /** 平台佣金比例 */
    private Double cancelfeePlatformCommissionRate;
    /** 供应商佣金比例 */
    private Double cancelfeeSupplierCommissionRate;
    /** 司机议价开关 */
    private Integer driverNegotiateSwitch;
    /** 调度员议价开关 */
    private Integer dispatcherNegotiateSwitch;
    /** 价格调整审核开关 */
    private Integer linePriceChangeAuditSwitch;
    /** 平台定价开关0关闭，1打开  */
    private Integer supportFixSwitch;
    /** 易宝账号AppKey */
    private String yeePayAppKey;
    /** 易宝账号密钥 */
    private String yeePaySecret;
    /** 易宝账号商户号 */
    private String yeePayMerchantNo;
    /** 商户自动抢单开关0关闭，1打开  */
    private Integer autoGrabOrderSwitch;
    /** 司机自动抢单开关0关闭，1打开  */
    private Integer driverAutoGrabOrderSwitch;
    /** 司机主动取消权限  0:关闭  1:打开 */
    private Integer driverCancelAccess;
    /** 司机每日可取消次数 */
    private Integer driverDayCancelTimes;
    /** 最晚发车前多少分钟外可取消 */
    private Integer cancelBeforeMinutes;
    /** 是否展示未支付订单开关  0关闭 1打开 */
    private Integer unpaidSwitch;
    /** 是否支持全域：0 不支持 1 支持 */
    private Integer supportAllArea;
    /** 全域时的佣金比策略id */
    private Long commissionPriceStrategyId;
    /** 司机im开关（0:关闭 1:打开） */
    private Integer driverImSwitch;
    /** 司机录单开关(0:关闭 1:开启) */
    private Integer offlineOrderSwitch;
    /** 微信邀请接单开关 */
    private Integer dispatchShareSwitch;
    /** 允许非注册司机接单开关 */
    private Integer allowOtherDriverGrab;
    /** 调度虚拟号开关 */
    private Integer dispatcherVirtualSwitch;
    /** 调度录单开关 */
    private Integer dispatcherOfflineOrderSwitch;
    /** 接单时间当天出行：0否 1是 */
    private Integer orderAcceptToday;
    /** 当天提前下单时间分钟 */
    private Integer orderAcceptTodayMin;
    /** 接单时间明天出行：0否 1是 */
    private Integer orderAcceptTomorrow;
    /** 接单时间后天出行：0否 1是 */
    private Integer orderAcceptAfterTomorrow;
    /** 接单时间三天后出行：0否 1是 */
    private Integer orderAcceptAfterThreeDays;
    /** 调度司机最大数 */
    private Integer dispathcherDriverLimit;
    /** 是否开启负余额账户限制：1开启 0不开启 */
    private Integer negativeAccountLimit;
    /** 负余额账户总额限制 */
    private Integer negativeAccountLimitAmount;
    /** 是否冻结提现：1冻结 0不冻结 */
    private Integer freezeWithdraw;
    /** 是否支付快速注册 */
    private Integer fastRegisterDriver;
    /** 司机注册后有效接单时间 */
    private Integer fastRegisterDriverVaildHour;
    /** 快速注册后能接单数量 */
    private Integer fastRegisterDriverOrderLimit;
    /** 0为普通供应商，1为车队 */
    private Integer supplierType;

public QueryAllQuery() {
}

public QueryAllQuery(Long id ,String vcode ,String companyName ,String companyShortName ,java.util.Date expirationDateStart ,java.util.Date expirationDateEnd ,String domain ,String address ,String contact ,String telephone ,Integer isWithdrawable ,String createUser ,java.util.Date createTimeStart ,java.util.Date createTimeEnd ,String updateUser ,java.util.Date updateTimeStart ,java.util.Date updateTimeEnd ,Integer status ,String email ,String productId ,Double commentScore ,Integer hasElectronicInvoice ,Integer accountLabel ,Integer isAssumeInsurance ,Integer isSupportRandom ,Long priceStrategyId ,String customServicePhoneNo ,String companyEin ,String checkText ,String referees ,String businessLicense ,Integer driverRobOrderSwitch ,Integer cancelfeeCommissionSwitch ,Double cancelfeePlatformCommissionRate ,Double cancelfeeSupplierCommissionRate ,Integer driverNegotiateSwitch ,Integer dispatcherNegotiateSwitch ,Integer linePriceChangeAuditSwitch ,Integer supportFixSwitch ,String yeePayAppKey ,String yeePaySecret ,String yeePayMerchantNo ,Integer autoGrabOrderSwitch ,Integer driverAutoGrabOrderSwitch ,Integer driverCancelAccess ,Integer driverDayCancelTimes ,Integer cancelBeforeMinutes ,Integer unpaidSwitch ,Integer supportAllArea ,Long commissionPriceStrategyId ,Integer driverImSwitch ,Integer offlineOrderSwitch ,Integer dispatchShareSwitch ,Integer allowOtherDriverGrab ,Integer dispatcherVirtualSwitch ,Integer dispatcherOfflineOrderSwitch ,Integer orderAcceptToday ,Integer orderAcceptTodayMin ,Integer orderAcceptTomorrow ,Integer orderAcceptAfterTomorrow ,Integer orderAcceptAfterThreeDays ,Integer dispathcherDriverLimit ,Integer negativeAccountLimit ,Integer negativeAccountLimitAmount ,Integer freezeWithdraw ,Integer fastRegisterDriver ,Integer fastRegisterDriverVaildHour ,Integer fastRegisterDriverOrderLimit ,Integer supplierType ) {
    this.id = id;
    this.vcode = vcode;
    this.companyName = companyName;
    this.companyShortName = companyShortName;
    this.expirationDateStart = expirationDateStart;
    this.expirationDateEnd = expirationDateEnd;
    this.domain = domain;
    this.address = address;
    this.contact = contact;
    this.telephone = telephone;
    this.isWithdrawable = isWithdrawable;
    this.createUser = createUser;
    this.createTimeStart = createTimeStart;
    this.createTimeEnd = createTimeEnd;
    this.updateUser = updateUser;
    this.updateTimeStart = updateTimeStart;
    this.updateTimeEnd = updateTimeEnd;
    this.status = status;
    this.email = email;
    this.productId = productId;
    this.commentScore = commentScore;
    this.hasElectronicInvoice = hasElectronicInvoice;
    this.accountLabel = accountLabel;
    this.isAssumeInsurance = isAssumeInsurance;
    this.isSupportRandom = isSupportRandom;
    this.priceStrategyId = priceStrategyId;
    this.customServicePhoneNo = customServicePhoneNo;
    this.companyEin = companyEin;
    this.checkText = checkText;
    this.referees = referees;
    this.businessLicense = businessLicense;
    this.driverRobOrderSwitch = driverRobOrderSwitch;
    this.cancelfeeCommissionSwitch = cancelfeeCommissionSwitch;
    this.cancelfeePlatformCommissionRate = cancelfeePlatformCommissionRate;
    this.cancelfeeSupplierCommissionRate = cancelfeeSupplierCommissionRate;
    this.driverNegotiateSwitch = driverNegotiateSwitch;
    this.dispatcherNegotiateSwitch = dispatcherNegotiateSwitch;
    this.linePriceChangeAuditSwitch = linePriceChangeAuditSwitch;
    this.supportFixSwitch = supportFixSwitch;
    this.yeePayAppKey = yeePayAppKey;
    this.yeePaySecret = yeePaySecret;
    this.yeePayMerchantNo = yeePayMerchantNo;
    this.autoGrabOrderSwitch = autoGrabOrderSwitch;
    this.driverAutoGrabOrderSwitch = driverAutoGrabOrderSwitch;
    this.driverCancelAccess = driverCancelAccess;
    this.driverDayCancelTimes = driverDayCancelTimes;
    this.cancelBeforeMinutes = cancelBeforeMinutes;
    this.unpaidSwitch = unpaidSwitch;
    this.supportAllArea = supportAllArea;
    this.commissionPriceStrategyId = commissionPriceStrategyId;
    this.driverImSwitch = driverImSwitch;
    this.offlineOrderSwitch = offlineOrderSwitch;
    this.dispatchShareSwitch = dispatchShareSwitch;
    this.allowOtherDriverGrab = allowOtherDriverGrab;
    this.dispatcherVirtualSwitch = dispatcherVirtualSwitch;
    this.dispatcherOfflineOrderSwitch = dispatcherOfflineOrderSwitch;
    this.orderAcceptToday = orderAcceptToday;
    this.orderAcceptTodayMin = orderAcceptTodayMin;
    this.orderAcceptTomorrow = orderAcceptTomorrow;
    this.orderAcceptAfterTomorrow = orderAcceptAfterTomorrow;
    this.orderAcceptAfterThreeDays = orderAcceptAfterThreeDays;
    this.dispathcherDriverLimit = dispathcherDriverLimit;
    this.negativeAccountLimit = negativeAccountLimit;
    this.negativeAccountLimitAmount = negativeAccountLimitAmount;
    this.freezeWithdraw = freezeWithdraw;
    this.fastRegisterDriver = fastRegisterDriver;
    this.fastRegisterDriverVaildHour = fastRegisterDriverVaildHour;
    this.fastRegisterDriverOrderLimit = fastRegisterDriverOrderLimit;
    this.supplierType = supplierType;
}

    public Long getId() {
    return id;
    }
    public void setId(Long id) {
    this.id = id;
    }
    public String getVcode() {
    return vcode;
    }
    public void setVcode(String vcode) {
    this.vcode = vcode;
    }
    public String getCompanyName() {
    return companyName;
    }
    public void setCompanyName(String companyName) {
    this.companyName = companyName;
    }
    public String getCompanyShortName() {
    return companyShortName;
    }
    public void setCompanyShortName(String companyShortName) {
    this.companyShortName = companyShortName;
    }
    public java.util.Date getExpirationDateStart() {
    return expirationDateStart;
    }
    public void setExpirationDateStart(java.util.Date expirationDateStart) {
    this.expirationDateStart = expirationDateStart;
    }
    public java.util.Date getExpirationDateEnd() {
    return expirationDateEnd;
    }
    public void setExpirationDateEnd(java.util.Date expirationDateEnd) {
    this.expirationDateEnd = expirationDateEnd;
    }
    public String getDomain() {
    return domain;
    }
    public void setDomain(String domain) {
    this.domain = domain;
    }
    public String getAddress() {
    return address;
    }
    public void setAddress(String address) {
    this.address = address;
    }
    public String getContact() {
    return contact;
    }
    public void setContact(String contact) {
    this.contact = contact;
    }
    public String getTelephone() {
    return telephone;
    }
    public void setTelephone(String telephone) {
    this.telephone = telephone;
    }
    public Integer getIsWithdrawable() {
    return isWithdrawable;
    }
    public void setIsWithdrawable(Integer isWithdrawable) {
    this.isWithdrawable = isWithdrawable;
    }
    public String getCreateUser() {
    return createUser;
    }
    public void setCreateUser(String createUser) {
    this.createUser = createUser;
    }
    public java.util.Date getCreateTimeStart() {
    return createTimeStart;
    }
    public void setCreateTimeStart(java.util.Date createTimeStart) {
    this.createTimeStart = createTimeStart;
    }
    public java.util.Date getCreateTimeEnd() {
    return createTimeEnd;
    }
    public void setCreateTimeEnd(java.util.Date createTimeEnd) {
    this.createTimeEnd = createTimeEnd;
    }
    public String getUpdateUser() {
    return updateUser;
    }
    public void setUpdateUser(String updateUser) {
    this.updateUser = updateUser;
    }
    public java.util.Date getUpdateTimeStart() {
    return updateTimeStart;
    }
    public void setUpdateTimeStart(java.util.Date updateTimeStart) {
    this.updateTimeStart = updateTimeStart;
    }
    public java.util.Date getUpdateTimeEnd() {
    return updateTimeEnd;
    }
    public void setUpdateTimeEnd(java.util.Date updateTimeEnd) {
    this.updateTimeEnd = updateTimeEnd;
    }
    public Integer getStatus() {
    return status;
    }
    public void setStatus(Integer status) {
    this.status = status;
    }
    public String getEmail() {
    return email;
    }
    public void setEmail(String email) {
    this.email = email;
    }
    public String getProductId() {
    return productId;
    }
    public void setProductId(String productId) {
    this.productId = productId;
    }
    public Double getCommentScore() {
    return commentScore;
    }
    public void setCommentScore(Double commentScore) {
    this.commentScore = commentScore;
    }
    public Integer getHasElectronicInvoice() {
    return hasElectronicInvoice;
    }
    public void setHasElectronicInvoice(Integer hasElectronicInvoice) {
    this.hasElectronicInvoice = hasElectronicInvoice;
    }
    public Integer getAccountLabel() {
    return accountLabel;
    }
    public void setAccountLabel(Integer accountLabel) {
    this.accountLabel = accountLabel;
    }
    public Integer getIsAssumeInsurance() {
    return isAssumeInsurance;
    }
    public void setIsAssumeInsurance(Integer isAssumeInsurance) {
    this.isAssumeInsurance = isAssumeInsurance;
    }
    public Integer getIsSupportRandom() {
    return isSupportRandom;
    }
    public void setIsSupportRandom(Integer isSupportRandom) {
    this.isSupportRandom = isSupportRandom;
    }
    public Long getPriceStrategyId() {
    return priceStrategyId;
    }
    public void setPriceStrategyId(Long priceStrategyId) {
    this.priceStrategyId = priceStrategyId;
    }
    public String getCustomServicePhoneNo() {
    return customServicePhoneNo;
    }
    public void setCustomServicePhoneNo(String customServicePhoneNo) {
    this.customServicePhoneNo = customServicePhoneNo;
    }
    public String getCompanyEin() {
    return companyEin;
    }
    public void setCompanyEin(String companyEin) {
    this.companyEin = companyEin;
    }
    public String getCheckText() {
    return checkText;
    }
    public void setCheckText(String checkText) {
    this.checkText = checkText;
    }
    public String getReferees() {
    return referees;
    }
    public void setReferees(String referees) {
    this.referees = referees;
    }
    public String getBusinessLicense() {
    return businessLicense;
    }
    public void setBusinessLicense(String businessLicense) {
    this.businessLicense = businessLicense;
    }
    public Integer getDriverRobOrderSwitch() {
    return driverRobOrderSwitch;
    }
    public void setDriverRobOrderSwitch(Integer driverRobOrderSwitch) {
    this.driverRobOrderSwitch = driverRobOrderSwitch;
    }
    public Integer getCancelfeeCommissionSwitch() {
    return cancelfeeCommissionSwitch;
    }
    public void setCancelfeeCommissionSwitch(Integer cancelfeeCommissionSwitch) {
    this.cancelfeeCommissionSwitch = cancelfeeCommissionSwitch;
    }
    public Double getCancelfeePlatformCommissionRate() {
    return cancelfeePlatformCommissionRate;
    }
    public void setCancelfeePlatformCommissionRate(Double cancelfeePlatformCommissionRate) {
    this.cancelfeePlatformCommissionRate = cancelfeePlatformCommissionRate;
    }
    public Double getCancelfeeSupplierCommissionRate() {
    return cancelfeeSupplierCommissionRate;
    }
    public void setCancelfeeSupplierCommissionRate(Double cancelfeeSupplierCommissionRate) {
    this.cancelfeeSupplierCommissionRate = cancelfeeSupplierCommissionRate;
    }
    public Integer getDriverNegotiateSwitch() {
    return driverNegotiateSwitch;
    }
    public void setDriverNegotiateSwitch(Integer driverNegotiateSwitch) {
    this.driverNegotiateSwitch = driverNegotiateSwitch;
    }
    public Integer getDispatcherNegotiateSwitch() {
    return dispatcherNegotiateSwitch;
    }
    public void setDispatcherNegotiateSwitch(Integer dispatcherNegotiateSwitch) {
    this.dispatcherNegotiateSwitch = dispatcherNegotiateSwitch;
    }
    public Integer getLinePriceChangeAuditSwitch() {
    return linePriceChangeAuditSwitch;
    }
    public void setLinePriceChangeAuditSwitch(Integer linePriceChangeAuditSwitch) {
    this.linePriceChangeAuditSwitch = linePriceChangeAuditSwitch;
    }
    public Integer getSupportFixSwitch() {
    return supportFixSwitch;
    }
    public void setSupportFixSwitch(Integer supportFixSwitch) {
    this.supportFixSwitch = supportFixSwitch;
    }
    public String getYeePayAppKey() {
    return yeePayAppKey;
    }
    public void setYeePayAppKey(String yeePayAppKey) {
    this.yeePayAppKey = yeePayAppKey;
    }
    public String getYeePaySecret() {
    return yeePaySecret;
    }
    public void setYeePaySecret(String yeePaySecret) {
    this.yeePaySecret = yeePaySecret;
    }
    public String getYeePayMerchantNo() {
    return yeePayMerchantNo;
    }
    public void setYeePayMerchantNo(String yeePayMerchantNo) {
    this.yeePayMerchantNo = yeePayMerchantNo;
    }
    public Integer getAutoGrabOrderSwitch() {
    return autoGrabOrderSwitch;
    }
    public void setAutoGrabOrderSwitch(Integer autoGrabOrderSwitch) {
    this.autoGrabOrderSwitch = autoGrabOrderSwitch;
    }
    public Integer getDriverAutoGrabOrderSwitch() {
    return driverAutoGrabOrderSwitch;
    }
    public void setDriverAutoGrabOrderSwitch(Integer driverAutoGrabOrderSwitch) {
    this.driverAutoGrabOrderSwitch = driverAutoGrabOrderSwitch;
    }
    public Integer getDriverCancelAccess() {
    return driverCancelAccess;
    }
    public void setDriverCancelAccess(Integer driverCancelAccess) {
    this.driverCancelAccess = driverCancelAccess;
    }
    public Integer getDriverDayCancelTimes() {
    return driverDayCancelTimes;
    }
    public void setDriverDayCancelTimes(Integer driverDayCancelTimes) {
    this.driverDayCancelTimes = driverDayCancelTimes;
    }
    public Integer getCancelBeforeMinutes() {
    return cancelBeforeMinutes;
    }
    public void setCancelBeforeMinutes(Integer cancelBeforeMinutes) {
    this.cancelBeforeMinutes = cancelBeforeMinutes;
    }
    public Integer getUnpaidSwitch() {
    return unpaidSwitch;
    }
    public void setUnpaidSwitch(Integer unpaidSwitch) {
    this.unpaidSwitch = unpaidSwitch;
    }
    public Integer getSupportAllArea() {
    return supportAllArea;
    }
    public void setSupportAllArea(Integer supportAllArea) {
    this.supportAllArea = supportAllArea;
    }
    public Long getCommissionPriceStrategyId() {
    return commissionPriceStrategyId;
    }
    public void setCommissionPriceStrategyId(Long commissionPriceStrategyId) {
    this.commissionPriceStrategyId = commissionPriceStrategyId;
    }
    public Integer getDriverImSwitch() {
    return driverImSwitch;
    }
    public void setDriverImSwitch(Integer driverImSwitch) {
    this.driverImSwitch = driverImSwitch;
    }
    public Integer getOfflineOrderSwitch() {
    return offlineOrderSwitch;
    }
    public void setOfflineOrderSwitch(Integer offlineOrderSwitch) {
    this.offlineOrderSwitch = offlineOrderSwitch;
    }
    public Integer getDispatchShareSwitch() {
    return dispatchShareSwitch;
    }
    public void setDispatchShareSwitch(Integer dispatchShareSwitch) {
    this.dispatchShareSwitch = dispatchShareSwitch;
    }
    public Integer getAllowOtherDriverGrab() {
    return allowOtherDriverGrab;
    }
    public void setAllowOtherDriverGrab(Integer allowOtherDriverGrab) {
    this.allowOtherDriverGrab = allowOtherDriverGrab;
    }
    public Integer getDispatcherVirtualSwitch() {
    return dispatcherVirtualSwitch;
    }
    public void setDispatcherVirtualSwitch(Integer dispatcherVirtualSwitch) {
    this.dispatcherVirtualSwitch = dispatcherVirtualSwitch;
    }
    public Integer getDispatcherOfflineOrderSwitch() {
    return dispatcherOfflineOrderSwitch;
    }
    public void setDispatcherOfflineOrderSwitch(Integer dispatcherOfflineOrderSwitch) {
    this.dispatcherOfflineOrderSwitch = dispatcherOfflineOrderSwitch;
    }
    public Integer getOrderAcceptToday() {
    return orderAcceptToday;
    }
    public void setOrderAcceptToday(Integer orderAcceptToday) {
    this.orderAcceptToday = orderAcceptToday;
    }
    public Integer getOrderAcceptTodayMin() {
    return orderAcceptTodayMin;
    }
    public void setOrderAcceptTodayMin(Integer orderAcceptTodayMin) {
    this.orderAcceptTodayMin = orderAcceptTodayMin;
    }
    public Integer getOrderAcceptTomorrow() {
    return orderAcceptTomorrow;
    }
    public void setOrderAcceptTomorrow(Integer orderAcceptTomorrow) {
    this.orderAcceptTomorrow = orderAcceptTomorrow;
    }
    public Integer getOrderAcceptAfterTomorrow() {
    return orderAcceptAfterTomorrow;
    }
    public void setOrderAcceptAfterTomorrow(Integer orderAcceptAfterTomorrow) {
    this.orderAcceptAfterTomorrow = orderAcceptAfterTomorrow;
    }
    public Integer getOrderAcceptAfterThreeDays() {
    return orderAcceptAfterThreeDays;
    }
    public void setOrderAcceptAfterThreeDays(Integer orderAcceptAfterThreeDays) {
    this.orderAcceptAfterThreeDays = orderAcceptAfterThreeDays;
    }
    public Integer getDispathcherDriverLimit() {
    return dispathcherDriverLimit;
    }
    public void setDispathcherDriverLimit(Integer dispathcherDriverLimit) {
    this.dispathcherDriverLimit = dispathcherDriverLimit;
    }
    public Integer getNegativeAccountLimit() {
    return negativeAccountLimit;
    }
    public void setNegativeAccountLimit(Integer negativeAccountLimit) {
    this.negativeAccountLimit = negativeAccountLimit;
    }
    public Integer getNegativeAccountLimitAmount() {
    return negativeAccountLimitAmount;
    }
    public void setNegativeAccountLimitAmount(Integer negativeAccountLimitAmount) {
    this.negativeAccountLimitAmount = negativeAccountLimitAmount;
    }
    public Integer getFreezeWithdraw() {
    return freezeWithdraw;
    }
    public void setFreezeWithdraw(Integer freezeWithdraw) {
    this.freezeWithdraw = freezeWithdraw;
    }
    public Integer getFastRegisterDriver() {
    return fastRegisterDriver;
    }
    public void setFastRegisterDriver(Integer fastRegisterDriver) {
    this.fastRegisterDriver = fastRegisterDriver;
    }
    public Integer getFastRegisterDriverVaildHour() {
    return fastRegisterDriverVaildHour;
    }
    public void setFastRegisterDriverVaildHour(Integer fastRegisterDriverVaildHour) {
    this.fastRegisterDriverVaildHour = fastRegisterDriverVaildHour;
    }
    public Integer getFastRegisterDriverOrderLimit() {
    return fastRegisterDriverOrderLimit;
    }
    public void setFastRegisterDriverOrderLimit(Integer fastRegisterDriverOrderLimit) {
    this.fastRegisterDriverOrderLimit = fastRegisterDriverOrderLimit;
    }
    public Integer getSupplierType() {
    return supplierType;
    }
    public void setSupplierType(Integer supplierType) {
    this.supplierType = supplierType;
    }

public String toString() {
return ToStringBuilder.reflectionToString(this);
}
}
