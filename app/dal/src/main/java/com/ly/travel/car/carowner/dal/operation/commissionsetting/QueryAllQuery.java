
/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.ly.travel.car.carowner.dal.operation.commissionsetting;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;


import java.io.*;
import java.net.*;
import java.util.*;

import java.math.BigInteger;

import com.ly.sof.utils.page.PageQuery;
import com.ly.sof.utils.page.PageList;
import com.ly.sof.utils.page.PageQueryUtils;
/**
 * database table: commission_setting
 * database table comments: CommissionSetting
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to 
 * directory <tt>(project-home)/dalgen</tt>
 * 
 * <AUTHOR>
 **/
public class QueryAllQuery  implements java.io.Serializable {
private static final long serialVersionUID = -5216457518046898601L;

    /** 自增id */
    private Long id;
    /** 环境 */
    private String env;
    /** 佣金计算类型 1 比例 2 固定金额 */
    private Integer type;
    /** 佣金计算比例 包车 */
    private Double commRateAll;
    /** 佣金计算比例 拼车 */
    private Double commRateJoin;
    /** 佣金上限 包车 */
    private Double commLimitAll;
    /** 佣金上限 拼车 */
    private Double commLimitJoin;
    /** 创建人 */
    private String createUser;
    /** 创建时间 */
    private java.util.Date createTimeStart;
    /** 创建时间 */
    private java.util.Date createTimeEnd;
    /** 修改人 */
    private String updateUser;
    /** 修改时间 */
    private java.util.Date updateTimeStart;
    /** 修改时间 */
    private java.util.Date updateTimeEnd;

public QueryAllQuery() {
}

public QueryAllQuery(Long id ,String env ,Integer type ,Double commRateAll ,Double commRateJoin ,Double commLimitAll ,Double commLimitJoin ,String createUser ,java.util.Date createTimeStart ,java.util.Date createTimeEnd ,String updateUser ,java.util.Date updateTimeStart ,java.util.Date updateTimeEnd ) {
    this.id = id;
    this.env = env;
    this.type = type;
    this.commRateAll = commRateAll;
    this.commRateJoin = commRateJoin;
    this.commLimitAll = commLimitAll;
    this.commLimitJoin = commLimitJoin;
    this.createUser = createUser;
    this.createTimeStart = createTimeStart;
    this.createTimeEnd = createTimeEnd;
    this.updateUser = updateUser;
    this.updateTimeStart = updateTimeStart;
    this.updateTimeEnd = updateTimeEnd;
}

    public Long getId() {
    return id;
    }
    public void setId(Long id) {
    this.id = id;
    }
    public String getEnv() {
    return env;
    }
    public void setEnv(String env) {
    this.env = env;
    }
    public Integer getType() {
    return type;
    }
    public void setType(Integer type) {
    this.type = type;
    }
    public Double getCommRateAll() {
    return commRateAll;
    }
    public void setCommRateAll(Double commRateAll) {
    this.commRateAll = commRateAll;
    }
    public Double getCommRateJoin() {
    return commRateJoin;
    }
    public void setCommRateJoin(Double commRateJoin) {
    this.commRateJoin = commRateJoin;
    }
    public Double getCommLimitAll() {
    return commLimitAll;
    }
    public void setCommLimitAll(Double commLimitAll) {
    this.commLimitAll = commLimitAll;
    }
    public Double getCommLimitJoin() {
    return commLimitJoin;
    }
    public void setCommLimitJoin(Double commLimitJoin) {
    this.commLimitJoin = commLimitJoin;
    }
    public String getCreateUser() {
    return createUser;
    }
    public void setCreateUser(String createUser) {
    this.createUser = createUser;
    }
    public java.util.Date getCreateTimeStart() {
    return createTimeStart;
    }
    public void setCreateTimeStart(java.util.Date createTimeStart) {
    this.createTimeStart = createTimeStart;
    }
    public java.util.Date getCreateTimeEnd() {
    return createTimeEnd;
    }
    public void setCreateTimeEnd(java.util.Date createTimeEnd) {
    this.createTimeEnd = createTimeEnd;
    }
    public String getUpdateUser() {
    return updateUser;
    }
    public void setUpdateUser(String updateUser) {
    this.updateUser = updateUser;
    }
    public java.util.Date getUpdateTimeStart() {
    return updateTimeStart;
    }
    public void setUpdateTimeStart(java.util.Date updateTimeStart) {
    this.updateTimeStart = updateTimeStart;
    }
    public java.util.Date getUpdateTimeEnd() {
    return updateTimeEnd;
    }
    public void setUpdateTimeEnd(java.util.Date updateTimeEnd) {
    this.updateTimeEnd = updateTimeEnd;
    }

public String toString() {
return ToStringBuilder.reflectionToString(this);
}
}
