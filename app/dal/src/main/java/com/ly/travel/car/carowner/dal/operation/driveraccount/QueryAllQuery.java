
/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.ly.travel.car.carowner.dal.operation.driveraccount;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;


import java.io.*;
import java.net.*;
import java.util.*;

import java.math.BigInteger;

import com.ly.sof.utils.page.PageQuery;
import com.ly.sof.utils.page.PageList;
import com.ly.sof.utils.page.PageQueryUtils;
/**
 * database table: driver_account
 * database table comments: DriverAccount
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to 
 * directory <tt>(project-home)/dalgen</tt>
 * 
 * <AUTHOR>
 **/
public class QueryAllQuery  implements java.io.Serializable {
private static final long serialVersionUID = -5216457518046898601L;

    /** 主键ID */
    private Long id;
    /** 司机 ID */
    private Long driverId;
    /** 环境标识 */
    private String env;
    /** 总金额 */
    private Double totalAmount;
    /** 冻结金额 */
    private Double freezeAmount;
    /** 提现中金额 */
    private Double withdrawingAmount;
    /** 已提现金额 */
    private Double withdrawnAmount;
    /** 可用金额 */
    private Double availableAmount;
    /** 是否冻结 0-否 1-是 */
    private Integer isFreeze;
    /** 冻结时间 */
    private java.util.Date freezeTimeStart;
    /** 冻结时间 */
    private java.util.Date freezeTimeEnd;
    /** 解冻时间 */
    private java.util.Date unfreezeTimeStart;
    /** 解冻时间 */
    private java.util.Date unfreezeTimeEnd;
    /** 提现密码 */
    private String withdrawalPassword;
    /** 创建人 */
    private String createUser;
    /** 创建时间 */
    private java.util.Date createTimeStart;
    /** 创建时间 */
    private java.util.Date createTimeEnd;
    /** 修改人 */
    private String updateUser;
    /** 修改时间 */
    private java.util.Date updateTimeStart;
    /** 修改时间 */
    private java.util.Date updateTimeEnd;

public QueryAllQuery() {
}

public QueryAllQuery(Long id ,Long driverId ,String env ,Double totalAmount ,Double freezeAmount ,Double withdrawingAmount ,Double withdrawnAmount ,Double availableAmount ,Integer isFreeze ,java.util.Date freezeTimeStart ,java.util.Date freezeTimeEnd ,java.util.Date unfreezeTimeStart ,java.util.Date unfreezeTimeEnd ,String withdrawalPassword ,String createUser ,java.util.Date createTimeStart ,java.util.Date createTimeEnd ,String updateUser ,java.util.Date updateTimeStart ,java.util.Date updateTimeEnd ) {
    this.id = id;
    this.driverId = driverId;
    this.env = env;
    this.totalAmount = totalAmount;
    this.freezeAmount = freezeAmount;
    this.withdrawingAmount = withdrawingAmount;
    this.withdrawnAmount = withdrawnAmount;
    this.availableAmount = availableAmount;
    this.isFreeze = isFreeze;
    this.freezeTimeStart = freezeTimeStart;
    this.freezeTimeEnd = freezeTimeEnd;
    this.unfreezeTimeStart = unfreezeTimeStart;
    this.unfreezeTimeEnd = unfreezeTimeEnd;
    this.withdrawalPassword = withdrawalPassword;
    this.createUser = createUser;
    this.createTimeStart = createTimeStart;
    this.createTimeEnd = createTimeEnd;
    this.updateUser = updateUser;
    this.updateTimeStart = updateTimeStart;
    this.updateTimeEnd = updateTimeEnd;
}

    public Long getId() {
    return id;
    }
    public void setId(Long id) {
    this.id = id;
    }
    public Long getDriverId() {
    return driverId;
    }
    public void setDriverId(Long driverId) {
    this.driverId = driverId;
    }
    public String getEnv() {
    return env;
    }
    public void setEnv(String env) {
    this.env = env;
    }
    public Double getTotalAmount() {
    return totalAmount;
    }
    public void setTotalAmount(Double totalAmount) {
    this.totalAmount = totalAmount;
    }
    public Double getFreezeAmount() {
    return freezeAmount;
    }
    public void setFreezeAmount(Double freezeAmount) {
    this.freezeAmount = freezeAmount;
    }
    public Double getWithdrawingAmount() {
    return withdrawingAmount;
    }
    public void setWithdrawingAmount(Double withdrawingAmount) {
    this.withdrawingAmount = withdrawingAmount;
    }
    public Double getWithdrawnAmount() {
    return withdrawnAmount;
    }
    public void setWithdrawnAmount(Double withdrawnAmount) {
    this.withdrawnAmount = withdrawnAmount;
    }
    public Double getAvailableAmount() {
    return availableAmount;
    }
    public void setAvailableAmount(Double availableAmount) {
    this.availableAmount = availableAmount;
    }
    public Integer getIsFreeze() {
    return isFreeze;
    }
    public void setIsFreeze(Integer isFreeze) {
    this.isFreeze = isFreeze;
    }
    public java.util.Date getFreezeTimeStart() {
    return freezeTimeStart;
    }
    public void setFreezeTimeStart(java.util.Date freezeTimeStart) {
    this.freezeTimeStart = freezeTimeStart;
    }
    public java.util.Date getFreezeTimeEnd() {
    return freezeTimeEnd;
    }
    public void setFreezeTimeEnd(java.util.Date freezeTimeEnd) {
    this.freezeTimeEnd = freezeTimeEnd;
    }
    public java.util.Date getUnfreezeTimeStart() {
    return unfreezeTimeStart;
    }
    public void setUnfreezeTimeStart(java.util.Date unfreezeTimeStart) {
    this.unfreezeTimeStart = unfreezeTimeStart;
    }
    public java.util.Date getUnfreezeTimeEnd() {
    return unfreezeTimeEnd;
    }
    public void setUnfreezeTimeEnd(java.util.Date unfreezeTimeEnd) {
    this.unfreezeTimeEnd = unfreezeTimeEnd;
    }
    public String getWithdrawalPassword() {
    return withdrawalPassword;
    }
    public void setWithdrawalPassword(String withdrawalPassword) {
    this.withdrawalPassword = withdrawalPassword;
    }
    public String getCreateUser() {
    return createUser;
    }
    public void setCreateUser(String createUser) {
    this.createUser = createUser;
    }
    public java.util.Date getCreateTimeStart() {
    return createTimeStart;
    }
    public void setCreateTimeStart(java.util.Date createTimeStart) {
    this.createTimeStart = createTimeStart;
    }
    public java.util.Date getCreateTimeEnd() {
    return createTimeEnd;
    }
    public void setCreateTimeEnd(java.util.Date createTimeEnd) {
    this.createTimeEnd = createTimeEnd;
    }
    public String getUpdateUser() {
    return updateUser;
    }
    public void setUpdateUser(String updateUser) {
    this.updateUser = updateUser;
    }
    public java.util.Date getUpdateTimeStart() {
    return updateTimeStart;
    }
    public void setUpdateTimeStart(java.util.Date updateTimeStart) {
    this.updateTimeStart = updateTimeStart;
    }
    public java.util.Date getUpdateTimeEnd() {
    return updateTimeEnd;
    }
    public void setUpdateTimeEnd(java.util.Date updateTimeEnd) {
    this.updateTimeEnd = updateTimeEnd;
    }

public String toString() {
return ToStringBuilder.reflectionToString(this);
}
}
