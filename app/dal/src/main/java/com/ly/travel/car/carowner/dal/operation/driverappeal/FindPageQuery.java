
/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.ly.travel.car.carowner.dal.operation.driverappeal;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;


import java.io.*;
import java.net.*;
import java.util.*;

import java.math.BigInteger;

import com.ly.sof.utils.page.PageQuery;
import com.ly.sof.utils.page.PageList;
import com.ly.sof.utils.page.PageQueryUtils;
/**
 * database table: driver_appeal
 * database table comments: DriverAppeal
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to 
 * directory <tt>(project-home)/dalgen</tt>
 * 
 * <AUTHOR>
 **/
public class FindPageQuery  extends Page<PERSON>uery implements java.io.Serializable {
private static final long serialVersionUID = -5216457518046898601L;

    /** 主键ID */
    private Long id;
    /** 环境标识 */
    private String env;
    /** 申诉编号 */
    private String appealNo;
    /** 司机ID */
    private Long driverId;
    /** 判定时间 */
    private java.util.Date arbitrationTimeStart;
    /** 判定时间 */
    private java.util.Date arbitrationTimeEnd;
    /** 金额 */
    private Double appealAmount;
    /** 申诉内容 */
    private String appealContent;
    /** 备注 */
    private String remark;
    /** 创建人 */
    private String createUser;
    /** 创建时间 */
    private java.util.Date createTimeStart;
    /** 创建时间 */
    private java.util.Date createTimeEnd;
    /** 订单号 */
    private String orderNo;

public FindPageQuery() {
}

public FindPageQuery(Long id ,String env ,String appealNo ,Long driverId ,java.util.Date arbitrationTimeStart ,java.util.Date arbitrationTimeEnd ,Double appealAmount ,String appealContent ,String remark ,String createUser ,java.util.Date createTimeStart ,java.util.Date createTimeEnd ,String orderNo ) {
    this.id = id;
    this.env = env;
    this.appealNo = appealNo;
    this.driverId = driverId;
    this.arbitrationTimeStart = arbitrationTimeStart;
    this.arbitrationTimeEnd = arbitrationTimeEnd;
    this.appealAmount = appealAmount;
    this.appealContent = appealContent;
    this.remark = remark;
    this.createUser = createUser;
    this.createTimeStart = createTimeStart;
    this.createTimeEnd = createTimeEnd;
    this.orderNo = orderNo;
}

    public Long getId() {
    return id;
    }
    public void setId(Long id) {
    this.id = id;
    }
    public String getEnv() {
    return env;
    }
    public void setEnv(String env) {
    this.env = env;
    }
    public String getAppealNo() {
    return appealNo;
    }
    public void setAppealNo(String appealNo) {
    this.appealNo = appealNo;
    }
    public Long getDriverId() {
    return driverId;
    }
    public void setDriverId(Long driverId) {
    this.driverId = driverId;
    }
    public java.util.Date getArbitrationTimeStart() {
    return arbitrationTimeStart;
    }
    public void setArbitrationTimeStart(java.util.Date arbitrationTimeStart) {
    this.arbitrationTimeStart = arbitrationTimeStart;
    }
    public java.util.Date getArbitrationTimeEnd() {
    return arbitrationTimeEnd;
    }
    public void setArbitrationTimeEnd(java.util.Date arbitrationTimeEnd) {
    this.arbitrationTimeEnd = arbitrationTimeEnd;
    }
    public Double getAppealAmount() {
    return appealAmount;
    }
    public void setAppealAmount(Double appealAmount) {
    this.appealAmount = appealAmount;
    }
    public String getAppealContent() {
    return appealContent;
    }
    public void setAppealContent(String appealContent) {
    this.appealContent = appealContent;
    }
    public String getRemark() {
    return remark;
    }
    public void setRemark(String remark) {
    this.remark = remark;
    }
    public String getCreateUser() {
    return createUser;
    }
    public void setCreateUser(String createUser) {
    this.createUser = createUser;
    }
    public java.util.Date getCreateTimeStart() {
    return createTimeStart;
    }
    public void setCreateTimeStart(java.util.Date createTimeStart) {
    this.createTimeStart = createTimeStart;
    }
    public java.util.Date getCreateTimeEnd() {
    return createTimeEnd;
    }
    public void setCreateTimeEnd(java.util.Date createTimeEnd) {
    this.createTimeEnd = createTimeEnd;
    }
    public String getOrderNo() {
    return orderNo;
    }
    public void setOrderNo(String orderNo) {
    this.orderNo = orderNo;
    }

public String toString() {
return ToStringBuilder.reflectionToString(this);
}
}
