
/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.ly.travel.car.carowner.dal.operation.driverbankcard;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;


import java.io.*;
import java.net.*;
import java.util.*;

import java.math.BigInteger;

import com.ly.sof.utils.page.PageQuery;
import com.ly.sof.utils.page.PageList;
import com.ly.sof.utils.page.PageQueryUtils;
/**
 * database table: driver_bank_card
 * database table comments: DriverBankCard
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to 
 * directory <tt>(project-home)/dalgen</tt>
 * 
 * <AUTHOR>
 **/
public class QueryAllQuery  implements java.io.Serializable {
private static final long serialVersionUID = -5216457518046898601L;

    /** 主键 */
    private Long id;
    /** 环境标识 */
    private String env;
    /** 司机 ID */
    private Long driverId;
    /** 持卡人姓名 */
    private String ownerName;
    /** 持卡人身份证号 */
    private String ownerIdCard;
    /** 持卡人银行卡号 */
    private String bankCardNo;
    /** 卡类型：DEBIT:借记卡 CREDIT:贷记卡 CORPORATE:对公银行账户 */
    private String bankCardType;
    /** 银行卡code */
    private String bankCardCode;
    /** 储蓄行ID */
    private Long depositBankId;
    /** 持卡人预留手机号 */
    private String ownerMobile;
    /** 状态 0-无效 1-有效 */
    private Integer status;
    /** 创建人 */
    private String createUser;
    /** 创建时间 */
    private java.util.Date createTimeStart;
    /** 创建时间 */
    private java.util.Date createTimeEnd;
    /** 修改人 */
    private String updateUser;
    /** 修改时间 */
    private java.util.Date updateTimeStart;
    /** 修改时间 */
    private java.util.Date updateTimeEnd;

public QueryAllQuery() {
}

public QueryAllQuery(Long id ,String env ,Long driverId ,String ownerName ,String ownerIdCard ,String bankCardNo ,String bankCardType ,String bankCardCode ,Long depositBankId ,String ownerMobile ,Integer status ,String createUser ,java.util.Date createTimeStart ,java.util.Date createTimeEnd ,String updateUser ,java.util.Date updateTimeStart ,java.util.Date updateTimeEnd ) {
    this.id = id;
    this.env = env;
    this.driverId = driverId;
    this.ownerName = ownerName;
    this.ownerIdCard = ownerIdCard;
    this.bankCardNo = bankCardNo;
    this.bankCardType = bankCardType;
    this.bankCardCode = bankCardCode;
    this.depositBankId = depositBankId;
    this.ownerMobile = ownerMobile;
    this.status = status;
    this.createUser = createUser;
    this.createTimeStart = createTimeStart;
    this.createTimeEnd = createTimeEnd;
    this.updateUser = updateUser;
    this.updateTimeStart = updateTimeStart;
    this.updateTimeEnd = updateTimeEnd;
}

    public Long getId() {
    return id;
    }
    public void setId(Long id) {
    this.id = id;
    }
    public String getEnv() {
    return env;
    }
    public void setEnv(String env) {
    this.env = env;
    }
    public Long getDriverId() {
    return driverId;
    }
    public void setDriverId(Long driverId) {
    this.driverId = driverId;
    }
    public String getOwnerName() {
    return ownerName;
    }
    public void setOwnerName(String ownerName) {
    this.ownerName = ownerName;
    }
    public String getOwnerIdCard() {
    return ownerIdCard;
    }
    public void setOwnerIdCard(String ownerIdCard) {
    this.ownerIdCard = ownerIdCard;
    }
    public String getBankCardNo() {
    return bankCardNo;
    }
    public void setBankCardNo(String bankCardNo) {
    this.bankCardNo = bankCardNo;
    }
    public String getBankCardType() {
    return bankCardType;
    }
    public void setBankCardType(String bankCardType) {
    this.bankCardType = bankCardType;
    }
    public String getBankCardCode() {
    return bankCardCode;
    }
    public void setBankCardCode(String bankCardCode) {
    this.bankCardCode = bankCardCode;
    }
    public Long getDepositBankId() {
    return depositBankId;
    }
    public void setDepositBankId(Long depositBankId) {
    this.depositBankId = depositBankId;
    }
    public String getOwnerMobile() {
    return ownerMobile;
    }
    public void setOwnerMobile(String ownerMobile) {
    this.ownerMobile = ownerMobile;
    }
    public Integer getStatus() {
    return status;
    }
    public void setStatus(Integer status) {
    this.status = status;
    }
    public String getCreateUser() {
    return createUser;
    }
    public void setCreateUser(String createUser) {
    this.createUser = createUser;
    }
    public java.util.Date getCreateTimeStart() {
    return createTimeStart;
    }
    public void setCreateTimeStart(java.util.Date createTimeStart) {
    this.createTimeStart = createTimeStart;
    }
    public java.util.Date getCreateTimeEnd() {
    return createTimeEnd;
    }
    public void setCreateTimeEnd(java.util.Date createTimeEnd) {
    this.createTimeEnd = createTimeEnd;
    }
    public String getUpdateUser() {
    return updateUser;
    }
    public void setUpdateUser(String updateUser) {
    this.updateUser = updateUser;
    }
    public java.util.Date getUpdateTimeStart() {
    return updateTimeStart;
    }
    public void setUpdateTimeStart(java.util.Date updateTimeStart) {
    this.updateTimeStart = updateTimeStart;
    }
    public java.util.Date getUpdateTimeEnd() {
    return updateTimeEnd;
    }
    public void setUpdateTimeEnd(java.util.Date updateTimeEnd) {
    this.updateTimeEnd = updateTimeEnd;
    }

public String toString() {
return ToStringBuilder.reflectionToString(this);
}
}
