
/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.ly.travel.car.carowner.dal.operation.driverbillfreeze;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;


import java.io.*;
import java.net.*;
import java.util.*;

import java.math.BigInteger;

import com.ly.sof.utils.page.PageQuery;
import com.ly.sof.utils.page.PageList;
import com.ly.sof.utils.page.PageQueryUtils;
/**
 * database table: driver_bill_freeze
 * database table comments: DriverBillFreeze
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to 
 * directory <tt>(project-home)/dalgen</tt>
 * 
 * <AUTHOR>
 **/
public class FindPageQuery  extends PageQuery implements java.io.Serializable {
private static final long serialVersionUID = -5216457518046898601L;

    /** 主键ID */
    private Long id;
    /** 流水号 */
    private String billNo;
    /** 环境标识 */
    private String env;
    /** 司机账户ID */
    private Long driverId;
    /** 是否已解冻 0未解冻 1已解冻 */
    private Integer unfreezeFlag;
    /** 备注 */
    private String freezeRemark;
    /** 创建时间 */
    private java.util.Date createTimeStart;
    /** 创建时间 */
    private java.util.Date createTimeEnd;
    /** 创建人 */
    private String createUser;
    /** 修改时间 */
    private java.util.Date updateTimeStart;
    /** 修改时间 */
    private java.util.Date updateTimeEnd;
    /** 修改人 */
    private String updateUser;
    /** 解冻时间 */
    private java.util.Date unfreezeTimeStart;
    /** 解冻时间 */
    private java.util.Date unfreezeTimeEnd;
    /** 冻结类型 */
    private Integer freezeType;

public FindPageQuery() {
}

public FindPageQuery(Long id ,String billNo ,String env ,Long driverId ,Integer unfreezeFlag ,String freezeRemark ,java.util.Date createTimeStart ,java.util.Date createTimeEnd ,String createUser ,java.util.Date updateTimeStart ,java.util.Date updateTimeEnd ,String updateUser ,java.util.Date unfreezeTimeStart ,java.util.Date unfreezeTimeEnd ,Integer freezeType ) {
    this.id = id;
    this.billNo = billNo;
    this.env = env;
    this.driverId = driverId;
    this.unfreezeFlag = unfreezeFlag;
    this.freezeRemark = freezeRemark;
    this.createTimeStart = createTimeStart;
    this.createTimeEnd = createTimeEnd;
    this.createUser = createUser;
    this.updateTimeStart = updateTimeStart;
    this.updateTimeEnd = updateTimeEnd;
    this.updateUser = updateUser;
    this.unfreezeTimeStart = unfreezeTimeStart;
    this.unfreezeTimeEnd = unfreezeTimeEnd;
    this.freezeType = freezeType;
}

    public Long getId() {
    return id;
    }
    public void setId(Long id) {
    this.id = id;
    }
    public String getBillNo() {
    return billNo;
    }
    public void setBillNo(String billNo) {
    this.billNo = billNo;
    }
    public String getEnv() {
    return env;
    }
    public void setEnv(String env) {
    this.env = env;
    }
    public Long getDriverId() {
    return driverId;
    }
    public void setDriverId(Long driverId) {
    this.driverId = driverId;
    }
    public Integer getUnfreezeFlag() {
    return unfreezeFlag;
    }
    public void setUnfreezeFlag(Integer unfreezeFlag) {
    this.unfreezeFlag = unfreezeFlag;
    }
    public String getFreezeRemark() {
    return freezeRemark;
    }
    public void setFreezeRemark(String freezeRemark) {
    this.freezeRemark = freezeRemark;
    }
    public java.util.Date getCreateTimeStart() {
    return createTimeStart;
    }
    public void setCreateTimeStart(java.util.Date createTimeStart) {
    this.createTimeStart = createTimeStart;
    }
    public java.util.Date getCreateTimeEnd() {
    return createTimeEnd;
    }
    public void setCreateTimeEnd(java.util.Date createTimeEnd) {
    this.createTimeEnd = createTimeEnd;
    }
    public String getCreateUser() {
    return createUser;
    }
    public void setCreateUser(String createUser) {
    this.createUser = createUser;
    }
    public java.util.Date getUpdateTimeStart() {
    return updateTimeStart;
    }
    public void setUpdateTimeStart(java.util.Date updateTimeStart) {
    this.updateTimeStart = updateTimeStart;
    }
    public java.util.Date getUpdateTimeEnd() {
    return updateTimeEnd;
    }
    public void setUpdateTimeEnd(java.util.Date updateTimeEnd) {
    this.updateTimeEnd = updateTimeEnd;
    }
    public String getUpdateUser() {
    return updateUser;
    }
    public void setUpdateUser(String updateUser) {
    this.updateUser = updateUser;
    }
    public java.util.Date getUnfreezeTimeStart() {
    return unfreezeTimeStart;
    }
    public void setUnfreezeTimeStart(java.util.Date unfreezeTimeStart) {
    this.unfreezeTimeStart = unfreezeTimeStart;
    }
    public java.util.Date getUnfreezeTimeEnd() {
    return unfreezeTimeEnd;
    }
    public void setUnfreezeTimeEnd(java.util.Date unfreezeTimeEnd) {
    this.unfreezeTimeEnd = unfreezeTimeEnd;
    }
    public Integer getFreezeType() {
    return freezeType;
    }
    public void setFreezeType(Integer freezeType) {
    this.freezeType = freezeType;
    }

public String toString() {
return ToStringBuilder.reflectionToString(this);
}
}
