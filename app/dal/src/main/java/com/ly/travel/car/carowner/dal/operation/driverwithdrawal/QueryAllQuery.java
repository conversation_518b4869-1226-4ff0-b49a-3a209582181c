
/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.ly.travel.car.carowner.dal.operation.driverwithdrawal;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;


import java.io.*;
import java.net.*;
import java.util.*;

import java.math.BigInteger;

import com.ly.sof.utils.page.PageQuery;
import com.ly.sof.utils.page.PageList;
import com.ly.sof.utils.page.PageQueryUtils;
/**
 * database table: driver_withdrawal
 * database table comments: DriverWithdrawal
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to 
 * directory <tt>(project-home)/dalgen</tt>
 * 
 * <AUTHOR>
 **/
public class QueryAllQuery  implements java.io.Serializable {
private static final long serialVersionUID = -5216457518046898601L;

    /** 主键ID */
    private Long id;
    /** 提现单号 */
    private String withdrawNo;
    /** 司机 id */
    private Long driverId;
    /** 环境标识 */
    private String env;
    /** 司机银行卡ID */
    private String driverBankCardId;
    /** 提现金额 */
    private Double amount;
    /** 易宝支付订单号 */
    private String yeePayOrderNo;
    /** 打款回调时间 */
    private java.util.Date remitBackTimeStart;
    /** 打款回调时间 */
    private java.util.Date remitBackTimeEnd;
    /** 申请状态 1-已申请 2-提现成功 3-提现失败 */
    private Integer status;
    /** 申请时间 */
    private java.util.Date applyTimeStart;
    /** 申请时间 */
    private java.util.Date applyTimeEnd;
    /** 申请通过时间 */
    private java.util.Date approvalTimeStart;
    /** 申请通过时间 */
    private java.util.Date approvalTimeEnd;
    /** 创建时间 */
    private java.util.Date createTimeStart;
    /** 创建时间 */
    private java.util.Date createTimeEnd;
    /** 创建人 */
    private String createUser;
    /** 修改时间 */
    private java.util.Date updateTimeStart;
    /** 修改时间 */
    private java.util.Date updateTimeEnd;
    /** 修改人 */
    private String updateUser;
    /** 申请时间 */
    private java.util.Date applyTimes;
    /** 备注 */
    private String remark;
    /** 提现设备号 */
    private String driverDeviceId;

public QueryAllQuery() {
}

public QueryAllQuery(Long id ,String withdrawNo ,Long driverId ,String env ,String driverBankCardId ,Double amount ,String yeePayOrderNo ,java.util.Date remitBackTimeStart ,java.util.Date remitBackTimeEnd ,Integer status ,java.util.Date applyTimeStart ,java.util.Date applyTimeEnd ,java.util.Date approvalTimeStart ,java.util.Date approvalTimeEnd ,java.util.Date createTimeStart ,java.util.Date createTimeEnd ,String createUser ,java.util.Date updateTimeStart ,java.util.Date updateTimeEnd ,String updateUser ,java.util.Date applyTimes ,String remark ,String driverDeviceId ) {
    this.id = id;
    this.withdrawNo = withdrawNo;
    this.driverId = driverId;
    this.env = env;
    this.driverBankCardId = driverBankCardId;
    this.amount = amount;
    this.yeePayOrderNo = yeePayOrderNo;
    this.remitBackTimeStart = remitBackTimeStart;
    this.remitBackTimeEnd = remitBackTimeEnd;
    this.status = status;
    this.applyTimeStart = applyTimeStart;
    this.applyTimeEnd = applyTimeEnd;
    this.approvalTimeStart = approvalTimeStart;
    this.approvalTimeEnd = approvalTimeEnd;
    this.createTimeStart = createTimeStart;
    this.createTimeEnd = createTimeEnd;
    this.createUser = createUser;
    this.updateTimeStart = updateTimeStart;
    this.updateTimeEnd = updateTimeEnd;
    this.updateUser = updateUser;
    this.applyTimes = applyTimes;
    this.remark = remark;
    this.driverDeviceId = driverDeviceId;
}

    public Long getId() {
    return id;
    }
    public void setId(Long id) {
    this.id = id;
    }
    public String getWithdrawNo() {
    return withdrawNo;
    }
    public void setWithdrawNo(String withdrawNo) {
    this.withdrawNo = withdrawNo;
    }
    public Long getDriverId() {
    return driverId;
    }
    public void setDriverId(Long driverId) {
    this.driverId = driverId;
    }
    public String getEnv() {
    return env;
    }
    public void setEnv(String env) {
    this.env = env;
    }
    public String getDriverBankCardId() {
    return driverBankCardId;
    }
    public void setDriverBankCardId(String driverBankCardId) {
    this.driverBankCardId = driverBankCardId;
    }
    public Double getAmount() {
    return amount;
    }
    public void setAmount(Double amount) {
    this.amount = amount;
    }
    public String getYeePayOrderNo() {
    return yeePayOrderNo;
    }
    public void setYeePayOrderNo(String yeePayOrderNo) {
    this.yeePayOrderNo = yeePayOrderNo;
    }
    public java.util.Date getRemitBackTimeStart() {
    return remitBackTimeStart;
    }
    public void setRemitBackTimeStart(java.util.Date remitBackTimeStart) {
    this.remitBackTimeStart = remitBackTimeStart;
    }
    public java.util.Date getRemitBackTimeEnd() {
    return remitBackTimeEnd;
    }
    public void setRemitBackTimeEnd(java.util.Date remitBackTimeEnd) {
    this.remitBackTimeEnd = remitBackTimeEnd;
    }
    public Integer getStatus() {
    return status;
    }
    public void setStatus(Integer status) {
    this.status = status;
    }
    public java.util.Date getApplyTimeStart() {
    return applyTimeStart;
    }
    public void setApplyTimeStart(java.util.Date applyTimeStart) {
    this.applyTimeStart = applyTimeStart;
    }
    public java.util.Date getApplyTimeEnd() {
    return applyTimeEnd;
    }
    public void setApplyTimeEnd(java.util.Date applyTimeEnd) {
    this.applyTimeEnd = applyTimeEnd;
    }
    public java.util.Date getApprovalTimeStart() {
    return approvalTimeStart;
    }
    public void setApprovalTimeStart(java.util.Date approvalTimeStart) {
    this.approvalTimeStart = approvalTimeStart;
    }
    public java.util.Date getApprovalTimeEnd() {
    return approvalTimeEnd;
    }
    public void setApprovalTimeEnd(java.util.Date approvalTimeEnd) {
    this.approvalTimeEnd = approvalTimeEnd;
    }
    public java.util.Date getCreateTimeStart() {
    return createTimeStart;
    }
    public void setCreateTimeStart(java.util.Date createTimeStart) {
    this.createTimeStart = createTimeStart;
    }
    public java.util.Date getCreateTimeEnd() {
    return createTimeEnd;
    }
    public void setCreateTimeEnd(java.util.Date createTimeEnd) {
    this.createTimeEnd = createTimeEnd;
    }
    public String getCreateUser() {
    return createUser;
    }
    public void setCreateUser(String createUser) {
    this.createUser = createUser;
    }
    public java.util.Date getUpdateTimeStart() {
    return updateTimeStart;
    }
    public void setUpdateTimeStart(java.util.Date updateTimeStart) {
    this.updateTimeStart = updateTimeStart;
    }
    public java.util.Date getUpdateTimeEnd() {
    return updateTimeEnd;
    }
    public void setUpdateTimeEnd(java.util.Date updateTimeEnd) {
    this.updateTimeEnd = updateTimeEnd;
    }
    public String getUpdateUser() {
    return updateUser;
    }
    public void setUpdateUser(String updateUser) {
    this.updateUser = updateUser;
    }
    public java.util.Date getApplyTimes() {
    return applyTimes;
    }
    public void setApplyTimes(java.util.Date applyTimes) {
    this.applyTimes = applyTimes;
    }
    public String getRemark() {
    return remark;
    }
    public void setRemark(String remark) {
    this.remark = remark;
    }
    public String getDriverDeviceId() {
    return driverDeviceId;
    }
    public void setDriverDeviceId(String driverDeviceId) {
    this.driverDeviceId = driverDeviceId;
    }

public String toString() {
return ToStringBuilder.reflectionToString(this);
}
}
