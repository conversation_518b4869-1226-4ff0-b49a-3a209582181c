
/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.ly.travel.car.carowner.dal.operation.driverwithdrawalsetting;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;


import java.io.*;
import java.net.*;
import java.util.*;

import java.math.BigInteger;

import com.ly.sof.utils.page.PageQuery;
import com.ly.sof.utils.page.PageList;
import com.ly.sof.utils.page.PageQueryUtils;
/**
 * database table: driver_withdrawal_setting
 * database table comments: DriverWithdrawalSetting
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to 
 * directory <tt>(project-home)/dalgen</tt>
 * 
 * <AUTHOR>
 **/
public class QueryAllQuery  implements java.io.Serializable {
private static final long serialVersionUID = -*********8046898601L;

    /** 主键ID */
    private Long id;
    /** 提现配置名 */
    private String name;
    /** 冻结时长 */
    private Integer limitHour;
    /** 每日最大提现次数 */
    private Integer limitSum;
    /** 单次最大提现金额 */
    private Double limitAmount;
    /** 环境标识 */
    private String env;
    /** 创建时间 */
    private java.util.Date createTimeStart;
    /** 创建时间 */
    private java.util.Date createTimeEnd;
    /** 创建人 */
    private String createUser;
    /** 修改时间 */
    private java.util.Date updateTimeStart;
    /** 修改时间 */
    private java.util.Date updateTimeEnd;
    /** 修改人 */
    private String updateUser;

public QueryAllQuery() {
}

public QueryAllQuery(Long id ,String name ,Integer limitHour ,Integer limitSum ,Double limitAmount ,String env ,java.util.Date createTimeStart ,java.util.Date createTimeEnd ,String createUser ,java.util.Date updateTimeStart ,java.util.Date updateTimeEnd ,String updateUser ) {
    this.id = id;
    this.name = name;
    this.limitHour = limitHour;
    this.limitSum = limitSum;
    this.limitAmount = limitAmount;
    this.env = env;
    this.createTimeStart = createTimeStart;
    this.createTimeEnd = createTimeEnd;
    this.createUser = createUser;
    this.updateTimeStart = updateTimeStart;
    this.updateTimeEnd = updateTimeEnd;
    this.updateUser = updateUser;
}

    public Long getId() {
    return id;
    }
    public void setId(Long id) {
    this.id = id;
    }
    public String getName() {
    return name;
    }
    public void setName(String name) {
    this.name = name;
    }
    public Integer getLimitHour() {
    return limitHour;
    }
    public void setLimitHour(Integer limitHour) {
    this.limitHour = limitHour;
    }
    public Integer getLimitSum() {
    return limitSum;
    }
    public void setLimitSum(Integer limitSum) {
    this.limitSum = limitSum;
    }
    public Double getLimitAmount() {
    return limitAmount;
    }
    public void setLimitAmount(Double limitAmount) {
    this.limitAmount = limitAmount;
    }
    public String getEnv() {
    return env;
    }
    public void setEnv(String env) {
    this.env = env;
    }
    public java.util.Date getCreateTimeStart() {
    return createTimeStart;
    }
    public void setCreateTimeStart(java.util.Date createTimeStart) {
    this.createTimeStart = createTimeStart;
    }
    public java.util.Date getCreateTimeEnd() {
    return createTimeEnd;
    }
    public void setCreateTimeEnd(java.util.Date createTimeEnd) {
    this.createTimeEnd = createTimeEnd;
    }
    public String getCreateUser() {
    return createUser;
    }
    public void setCreateUser(String createUser) {
    this.createUser = createUser;
    }
    public java.util.Date getUpdateTimeStart() {
    return updateTimeStart;
    }
    public void setUpdateTimeStart(java.util.Date updateTimeStart) {
    this.updateTimeStart = updateTimeStart;
    }
    public java.util.Date getUpdateTimeEnd() {
    return updateTimeEnd;
    }
    public void setUpdateTimeEnd(java.util.Date updateTimeEnd) {
    this.updateTimeEnd = updateTimeEnd;
    }
    public String getUpdateUser() {
    return updateUser;
    }
    public void setUpdateUser(String updateUser) {
    this.updateUser = updateUser;
    }

public String toString() {
return ToStringBuilder.reflectionToString(this);
}
}
