
/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.ly.travel.car.carowner.dal.operation.driverwithdrawlog;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;


import java.io.*;
import java.net.*;
import java.util.*;

import java.math.BigInteger;

import com.ly.sof.utils.page.PageQuery;
import com.ly.sof.utils.page.PageList;
import com.ly.sof.utils.page.PageQueryUtils;
/**
 * database table: driver_withdraw_log
 * database table comments: DriverWithdrawLog
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to 
 * directory <tt>(project-home)/dalgen</tt>
 * 
 * <AUTHOR>
 **/
public class QueryAllQuery  implements java.io.Serializable {
private static final long serialVersionUID = -5216457518046898601L;

    /** id */
    private Long id;
    /** 提现单号 */
    private String withdrawNo;
    /** 日志类型：1-申请提现、2-审核、3-支付 */
    private Integer logType;
    /** 备注 */
    private String remark;
    /** 创建人 */
    private String createUser;
    /** 创建时间 */
    private java.util.Date createTimeStart;
    /** 创建时间 */
    private java.util.Date createTimeEnd;
    /** 司机id */
    private Long driverId;

public QueryAllQuery() {
}

public QueryAllQuery(Long id ,String withdrawNo ,Integer logType ,String remark ,String createUser ,java.util.Date createTimeStart ,java.util.Date createTimeEnd ,Long driverId ) {
    this.id = id;
    this.withdrawNo = withdrawNo;
    this.logType = logType;
    this.remark = remark;
    this.createUser = createUser;
    this.createTimeStart = createTimeStart;
    this.createTimeEnd = createTimeEnd;
    this.driverId = driverId;
}

    public Long getId() {
    return id;
    }
    public void setId(Long id) {
    this.id = id;
    }
    public String getWithdrawNo() {
    return withdrawNo;
    }
    public void setWithdrawNo(String withdrawNo) {
    this.withdrawNo = withdrawNo;
    }
    public Integer getLogType() {
    return logType;
    }
    public void setLogType(Integer logType) {
    this.logType = logType;
    }
    public String getRemark() {
    return remark;
    }
    public void setRemark(String remark) {
    this.remark = remark;
    }
    public String getCreateUser() {
    return createUser;
    }
    public void setCreateUser(String createUser) {
    this.createUser = createUser;
    }
    public java.util.Date getCreateTimeStart() {
    return createTimeStart;
    }
    public void setCreateTimeStart(java.util.Date createTimeStart) {
    this.createTimeStart = createTimeStart;
    }
    public java.util.Date getCreateTimeEnd() {
    return createTimeEnd;
    }
    public void setCreateTimeEnd(java.util.Date createTimeEnd) {
    this.createTimeEnd = createTimeEnd;
    }
    public Long getDriverId() {
    return driverId;
    }
    public void setDriverId(Long driverId) {
    this.driverId = driverId;
    }

public String toString() {
return ToStringBuilder.reflectionToString(this);
}
}
