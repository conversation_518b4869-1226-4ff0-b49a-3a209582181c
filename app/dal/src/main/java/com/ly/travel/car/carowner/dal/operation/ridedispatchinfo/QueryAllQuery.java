
/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.ly.travel.car.carowner.dal.operation.ridedispatchinfo;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;


import java.io.*;
import java.net.*;
import java.util.*;

import java.math.BigInteger;

import com.ly.sof.utils.page.PageQuery;
import com.ly.sof.utils.page.PageList;
import com.ly.sof.utils.page.PageQueryUtils;
/**
 * database table: ride_dispatch_info
 * database table comments: RideDispatchInfo
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to 
 * directory <tt>(project-home)/dalgen</tt>
 * 
 * <AUTHOR>
 **/
public class QueryAllQuery  implements java.io.Serializable {
private static final long serialVersionUID = -5216457518046898601L;

    /** 自增id */
    private Long id;
    /** 订单号 */
    private String orderNo;
    /** 状态，1报单中  */
    private Integer status;
    /** 举手时间 */
    private java.util.Date acceptTimeStart;
    /** 举手时间 */
    private java.util.Date acceptTimeEnd;
    /** 创建人 */
    private String createUser;
    /** 创建时间 */
    private java.util.Date createTimeStart;
    /** 创建时间 */
    private java.util.Date createTimeEnd;
    /** 修改人 */
    private String updateUser;
    /** 修改时间 */
    private java.util.Date updateTimeStart;
    /** 修改时间 */
    private java.util.Date updateTimeEnd;
    /** 车牌 */
    private String vehicleNo;
    /** 司机姓名 */
    private String driverName;
    /** 司机电话 */
    private String driverMobile;
    /** 司机行程号 */
    private String driverTripNo;
    /** 司机id */
    private Long driverId;
    /** 顺路度 */
    private Integer hitchPercent;

public QueryAllQuery() {
}

public QueryAllQuery(Long id ,String orderNo ,Integer status ,java.util.Date acceptTimeStart ,java.util.Date acceptTimeEnd ,String createUser ,java.util.Date createTimeStart ,java.util.Date createTimeEnd ,String updateUser ,java.util.Date updateTimeStart ,java.util.Date updateTimeEnd ,String vehicleNo ,String driverName ,String driverMobile ,String driverTripNo ,Long driverId ,Integer hitchPercent ) {
    this.id = id;
    this.orderNo = orderNo;
    this.status = status;
    this.acceptTimeStart = acceptTimeStart;
    this.acceptTimeEnd = acceptTimeEnd;
    this.createUser = createUser;
    this.createTimeStart = createTimeStart;
    this.createTimeEnd = createTimeEnd;
    this.updateUser = updateUser;
    this.updateTimeStart = updateTimeStart;
    this.updateTimeEnd = updateTimeEnd;
    this.vehicleNo = vehicleNo;
    this.driverName = driverName;
    this.driverMobile = driverMobile;
    this.driverTripNo = driverTripNo;
    this.driverId = driverId;
    this.hitchPercent = hitchPercent;
}

    public Long getId() {
    return id;
    }
    public void setId(Long id) {
    this.id = id;
    }
    public String getOrderNo() {
    return orderNo;
    }
    public void setOrderNo(String orderNo) {
    this.orderNo = orderNo;
    }
    public Integer getStatus() {
    return status;
    }
    public void setStatus(Integer status) {
    this.status = status;
    }
    public java.util.Date getAcceptTimeStart() {
    return acceptTimeStart;
    }
    public void setAcceptTimeStart(java.util.Date acceptTimeStart) {
    this.acceptTimeStart = acceptTimeStart;
    }
    public java.util.Date getAcceptTimeEnd() {
    return acceptTimeEnd;
    }
    public void setAcceptTimeEnd(java.util.Date acceptTimeEnd) {
    this.acceptTimeEnd = acceptTimeEnd;
    }
    public String getCreateUser() {
    return createUser;
    }
    public void setCreateUser(String createUser) {
    this.createUser = createUser;
    }
    public java.util.Date getCreateTimeStart() {
    return createTimeStart;
    }
    public void setCreateTimeStart(java.util.Date createTimeStart) {
    this.createTimeStart = createTimeStart;
    }
    public java.util.Date getCreateTimeEnd() {
    return createTimeEnd;
    }
    public void setCreateTimeEnd(java.util.Date createTimeEnd) {
    this.createTimeEnd = createTimeEnd;
    }
    public String getUpdateUser() {
    return updateUser;
    }
    public void setUpdateUser(String updateUser) {
    this.updateUser = updateUser;
    }
    public java.util.Date getUpdateTimeStart() {
    return updateTimeStart;
    }
    public void setUpdateTimeStart(java.util.Date updateTimeStart) {
    this.updateTimeStart = updateTimeStart;
    }
    public java.util.Date getUpdateTimeEnd() {
    return updateTimeEnd;
    }
    public void setUpdateTimeEnd(java.util.Date updateTimeEnd) {
    this.updateTimeEnd = updateTimeEnd;
    }
    public String getVehicleNo() {
    return vehicleNo;
    }
    public void setVehicleNo(String vehicleNo) {
    this.vehicleNo = vehicleNo;
    }
    public String getDriverName() {
    return driverName;
    }
    public void setDriverName(String driverName) {
    this.driverName = driverName;
    }
    public String getDriverMobile() {
    return driverMobile;
    }
    public void setDriverMobile(String driverMobile) {
    this.driverMobile = driverMobile;
    }
    public String getDriverTripNo() {
    return driverTripNo;
    }
    public void setDriverTripNo(String driverTripNo) {
    this.driverTripNo = driverTripNo;
    }
    public Long getDriverId() {
    return driverId;
    }
    public void setDriverId(Long driverId) {
    this.driverId = driverId;
    }
    public Integer getHitchPercent() {
    return hitchPercent;
    }
    public void setHitchPercent(Integer hitchPercent) {
    this.hitchPercent = hitchPercent;
    }

public String toString() {
return ToStringBuilder.reflectionToString(this);
}
}
