
/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.ly.travel.car.carowner.dal.operation.rideorderinfo;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;


import java.io.*;
import java.net.*;
import java.util.*;

import java.math.BigInteger;

import com.ly.sof.utils.page.PageQuery;
import com.ly.sof.utils.page.PageList;
import com.ly.sof.utils.page.PageQueryUtils;
/**
 * database table: ride_order_info
 * database table comments: RideOrderInfo
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to 
 * directory <tt>(project-home)/dalgen</tt>
 * 
 * <AUTHOR>
 **/
public class QueryAllQuery  implements java.io.Serializable {
private static final long serialVersionUID = -5216457518046898601L;

    /** 自增id */
    private Long id;
    /** 环境 */
    private String env;
    /** 订单号 */
    private String orderNo;
    /** 供应链订单号 */
    private String distributorOrderNo;
    /** 供应链主订单号 */
    private String distributorMainOrderNo;
    /** 司机行程号 */
    private String driverTripNo;
    /** 订单来源  0:未知 1:同程C端 */
    private Integer source;
    /** 顺路度 */
    private Integer hitchPercent;
    /** 出发城市id */
    private Long startCityId;
    /** 到达城市id */
    private Long endCityId;
    /** 出发城市id */
    private Long startCity;
    /** 到达城市id */
    private Long endCity;
    /** 出发城市行政区code */
    private String startDistrictCode;
    /** 出发城市行政区 */
    private String startDistrict;
    /** 目的城市行政区code */
    private String endDistrictCode;
    /** 目的城市行政区 */
    private String endDistrict;
    /** 出发地点 */
    private String startingAdd;
    /** 目的地点 */
    private String endingAdd;
    /** 出发点经度 */
    private Double startingLon;
    /** 出发点纬度 */
    private Double startingLat;
    /** 目的点经度 */
    private Double endingLon;
    /** 目的点纬度 */
    private Double endingLat;
    /** 用车时间 */
    private java.util.Date startingTimeStart;
    /** 用车时间 */
    private java.util.Date startingTimeEnd;
    /** 最晚用车时间 */
    private java.util.Date lastStartingTimeStart;
    /** 最晚用车时间 */
    private java.util.Date lastStartingTimeEnd;
    /** 乘客联系电号 */
    private String telephone;
    /** 真实号码 */
    private String realTelephone;
    /** 数量 */
    private Integer passengerCount;
    /** 订单金额 */
    private Double amount;
    /** 付款金额 */
    private Double payPrice;
    /** 订单状态   -1已删除    1.抢单中(c端决策之前)  2.已派车   3已完成   4已取消 */
    private Integer status;
    /** 支付状态   0未支付   1 已支付  2部分支付  3已退款  4部分退款 */
    private Integer payStatus;
    /** 支付类型   1普通  2信用分 */
    private Integer payType;
    /** refId */
    private String refId;
    /** 订单类型：1、包车； 2、拼车； */
    private Integer orderType;
    /** 用户id */
    private Long memberId;
    /** 车牌 */
    private String vehicleNo;
    /** 司机姓名 */
    private String driverName;
    /** 司机电话 */
    private String driverMobile;
    /** 司机id */
    private Long driverId;
    /** 下单时间 */
    private java.util.Date bookTimeStart;
    /** 下单时间 */
    private java.util.Date bookTimeEnd;
    /** 创建人 */
    private String createUser;
    /** 创建时间 */
    private java.util.Date createTimeStart;
    /** 创建时间 */
    private java.util.Date createTimeEnd;
    /** 修改人 */
    private String updateUser;
    /** 修改时间 */
    private java.util.Date updateTimeStart;
    /** 修改时间 */
    private java.util.Date updateTimeEnd;
    /** 备注 */
    private String remark;
    /** 出发详细地址 */
    private String startingAddDetail;
    /** 到达详细地址 */
    private String endingAddDetail;
    /** 总里程数(单位：千米） */
    private Double distance;
    /** 时长，分钟单位 */
    private Integer duration;
    /** 随单分佣比例0-1之间 */
    private Double orderCommissionRatio;
    /** 随单分佣上限(单位元) */
    private Double orderCommissionCap;

public QueryAllQuery() {
}

public QueryAllQuery(Long id ,String env ,String orderNo ,String distributorOrderNo ,String distributorMainOrderNo ,String driverTripNo ,Integer source ,Integer hitchPercent ,Long startCityId ,Long endCityId ,Long startCity ,Long endCity ,String startDistrictCode ,String startDistrict ,String endDistrictCode ,String endDistrict ,String startingAdd ,String endingAdd ,Double startingLon ,Double startingLat ,Double endingLon ,Double endingLat ,java.util.Date startingTimeStart ,java.util.Date startingTimeEnd ,java.util.Date lastStartingTimeStart ,java.util.Date lastStartingTimeEnd ,String telephone ,String realTelephone ,Integer passengerCount ,Double amount ,Double payPrice ,Integer status ,Integer payStatus ,Integer payType ,String refId ,Integer orderType ,Long memberId ,String vehicleNo ,String driverName ,String driverMobile ,Long driverId ,java.util.Date bookTimeStart ,java.util.Date bookTimeEnd ,String createUser ,java.util.Date createTimeStart ,java.util.Date createTimeEnd ,String updateUser ,java.util.Date updateTimeStart ,java.util.Date updateTimeEnd ,String remark ,String startingAddDetail ,String endingAddDetail ,Double distance ,Integer duration ,Double orderCommissionRatio ,Double orderCommissionCap ) {
    this.id = id;
    this.env = env;
    this.orderNo = orderNo;
    this.distributorOrderNo = distributorOrderNo;
    this.distributorMainOrderNo = distributorMainOrderNo;
    this.driverTripNo = driverTripNo;
    this.source = source;
    this.hitchPercent = hitchPercent;
    this.startCityId = startCityId;
    this.endCityId = endCityId;
    this.startCity = startCity;
    this.endCity = endCity;
    this.startDistrictCode = startDistrictCode;
    this.startDistrict = startDistrict;
    this.endDistrictCode = endDistrictCode;
    this.endDistrict = endDistrict;
    this.startingAdd = startingAdd;
    this.endingAdd = endingAdd;
    this.startingLon = startingLon;
    this.startingLat = startingLat;
    this.endingLon = endingLon;
    this.endingLat = endingLat;
    this.startingTimeStart = startingTimeStart;
    this.startingTimeEnd = startingTimeEnd;
    this.lastStartingTimeStart = lastStartingTimeStart;
    this.lastStartingTimeEnd = lastStartingTimeEnd;
    this.telephone = telephone;
    this.realTelephone = realTelephone;
    this.passengerCount = passengerCount;
    this.amount = amount;
    this.payPrice = payPrice;
    this.status = status;
    this.payStatus = payStatus;
    this.payType = payType;
    this.refId = refId;
    this.orderType = orderType;
    this.memberId = memberId;
    this.vehicleNo = vehicleNo;
    this.driverName = driverName;
    this.driverMobile = driverMobile;
    this.driverId = driverId;
    this.bookTimeStart = bookTimeStart;
    this.bookTimeEnd = bookTimeEnd;
    this.createUser = createUser;
    this.createTimeStart = createTimeStart;
    this.createTimeEnd = createTimeEnd;
    this.updateUser = updateUser;
    this.updateTimeStart = updateTimeStart;
    this.updateTimeEnd = updateTimeEnd;
    this.remark = remark;
    this.startingAddDetail = startingAddDetail;
    this.endingAddDetail = endingAddDetail;
    this.distance = distance;
    this.duration = duration;
    this.orderCommissionRatio = orderCommissionRatio;
    this.orderCommissionCap = orderCommissionCap;
}

    public Long getId() {
    return id;
    }
    public void setId(Long id) {
    this.id = id;
    }
    public String getEnv() {
    return env;
    }
    public void setEnv(String env) {
    this.env = env;
    }
    public String getOrderNo() {
    return orderNo;
    }
    public void setOrderNo(String orderNo) {
    this.orderNo = orderNo;
    }
    public String getDistributorOrderNo() {
    return distributorOrderNo;
    }
    public void setDistributorOrderNo(String distributorOrderNo) {
    this.distributorOrderNo = distributorOrderNo;
    }
    public String getDistributorMainOrderNo() {
    return distributorMainOrderNo;
    }
    public void setDistributorMainOrderNo(String distributorMainOrderNo) {
    this.distributorMainOrderNo = distributorMainOrderNo;
    }
    public String getDriverTripNo() {
    return driverTripNo;
    }
    public void setDriverTripNo(String driverTripNo) {
    this.driverTripNo = driverTripNo;
    }
    public Integer getSource() {
    return source;
    }
    public void setSource(Integer source) {
    this.source = source;
    }
    public Integer getHitchPercent() {
    return hitchPercent;
    }
    public void setHitchPercent(Integer hitchPercent) {
    this.hitchPercent = hitchPercent;
    }
    public Long getStartCityId() {
    return startCityId;
    }
    public void setStartCityId(Long startCityId) {
    this.startCityId = startCityId;
    }
    public Long getEndCityId() {
    return endCityId;
    }
    public void setEndCityId(Long endCityId) {
    this.endCityId = endCityId;
    }
    public Long getStartCity() {
    return startCity;
    }
    public void setStartCity(Long startCity) {
    this.startCity = startCity;
    }
    public Long getEndCity() {
    return endCity;
    }
    public void setEndCity(Long endCity) {
    this.endCity = endCity;
    }
    public String getStartDistrictCode() {
    return startDistrictCode;
    }
    public void setStartDistrictCode(String startDistrictCode) {
    this.startDistrictCode = startDistrictCode;
    }
    public String getStartDistrict() {
    return startDistrict;
    }
    public void setStartDistrict(String startDistrict) {
    this.startDistrict = startDistrict;
    }
    public String getEndDistrictCode() {
    return endDistrictCode;
    }
    public void setEndDistrictCode(String endDistrictCode) {
    this.endDistrictCode = endDistrictCode;
    }
    public String getEndDistrict() {
    return endDistrict;
    }
    public void setEndDistrict(String endDistrict) {
    this.endDistrict = endDistrict;
    }
    public String getStartingAdd() {
    return startingAdd;
    }
    public void setStartingAdd(String startingAdd) {
    this.startingAdd = startingAdd;
    }
    public String getEndingAdd() {
    return endingAdd;
    }
    public void setEndingAdd(String endingAdd) {
    this.endingAdd = endingAdd;
    }
    public Double getStartingLon() {
    return startingLon;
    }
    public void setStartingLon(Double startingLon) {
    this.startingLon = startingLon;
    }
    public Double getStartingLat() {
    return startingLat;
    }
    public void setStartingLat(Double startingLat) {
    this.startingLat = startingLat;
    }
    public Double getEndingLon() {
    return endingLon;
    }
    public void setEndingLon(Double endingLon) {
    this.endingLon = endingLon;
    }
    public Double getEndingLat() {
    return endingLat;
    }
    public void setEndingLat(Double endingLat) {
    this.endingLat = endingLat;
    }
    public java.util.Date getStartingTimeStart() {
    return startingTimeStart;
    }
    public void setStartingTimeStart(java.util.Date startingTimeStart) {
    this.startingTimeStart = startingTimeStart;
    }
    public java.util.Date getStartingTimeEnd() {
    return startingTimeEnd;
    }
    public void setStartingTimeEnd(java.util.Date startingTimeEnd) {
    this.startingTimeEnd = startingTimeEnd;
    }
    public java.util.Date getLastStartingTimeStart() {
    return lastStartingTimeStart;
    }
    public void setLastStartingTimeStart(java.util.Date lastStartingTimeStart) {
    this.lastStartingTimeStart = lastStartingTimeStart;
    }
    public java.util.Date getLastStartingTimeEnd() {
    return lastStartingTimeEnd;
    }
    public void setLastStartingTimeEnd(java.util.Date lastStartingTimeEnd) {
    this.lastStartingTimeEnd = lastStartingTimeEnd;
    }
    public String getTelephone() {
    return telephone;
    }
    public void setTelephone(String telephone) {
    this.telephone = telephone;
    }
    public String getRealTelephone() {
    return realTelephone;
    }
    public void setRealTelephone(String realTelephone) {
    this.realTelephone = realTelephone;
    }
    public Integer getPassengerCount() {
    return passengerCount;
    }
    public void setPassengerCount(Integer passengerCount) {
    this.passengerCount = passengerCount;
    }
    public Double getAmount() {
    return amount;
    }
    public void setAmount(Double amount) {
    this.amount = amount;
    }
    public Double getPayPrice() {
    return payPrice;
    }
    public void setPayPrice(Double payPrice) {
    this.payPrice = payPrice;
    }
    public Integer getStatus() {
    return status;
    }
    public void setStatus(Integer status) {
    this.status = status;
    }
    public Integer getPayStatus() {
    return payStatus;
    }
    public void setPayStatus(Integer payStatus) {
    this.payStatus = payStatus;
    }
    public Integer getPayType() {
    return payType;
    }
    public void setPayType(Integer payType) {
    this.payType = payType;
    }
    public String getRefId() {
    return refId;
    }
    public void setRefId(String refId) {
    this.refId = refId;
    }
    public Integer getOrderType() {
    return orderType;
    }
    public void setOrderType(Integer orderType) {
    this.orderType = orderType;
    }
    public Long getMemberId() {
    return memberId;
    }
    public void setMemberId(Long memberId) {
    this.memberId = memberId;
    }
    public String getVehicleNo() {
    return vehicleNo;
    }
    public void setVehicleNo(String vehicleNo) {
    this.vehicleNo = vehicleNo;
    }
    public String getDriverName() {
    return driverName;
    }
    public void setDriverName(String driverName) {
    this.driverName = driverName;
    }
    public String getDriverMobile() {
    return driverMobile;
    }
    public void setDriverMobile(String driverMobile) {
    this.driverMobile = driverMobile;
    }
    public Long getDriverId() {
    return driverId;
    }
    public void setDriverId(Long driverId) {
    this.driverId = driverId;
    }
    public java.util.Date getBookTimeStart() {
    return bookTimeStart;
    }
    public void setBookTimeStart(java.util.Date bookTimeStart) {
    this.bookTimeStart = bookTimeStart;
    }
    public java.util.Date getBookTimeEnd() {
    return bookTimeEnd;
    }
    public void setBookTimeEnd(java.util.Date bookTimeEnd) {
    this.bookTimeEnd = bookTimeEnd;
    }
    public String getCreateUser() {
    return createUser;
    }
    public void setCreateUser(String createUser) {
    this.createUser = createUser;
    }
    public java.util.Date getCreateTimeStart() {
    return createTimeStart;
    }
    public void setCreateTimeStart(java.util.Date createTimeStart) {
    this.createTimeStart = createTimeStart;
    }
    public java.util.Date getCreateTimeEnd() {
    return createTimeEnd;
    }
    public void setCreateTimeEnd(java.util.Date createTimeEnd) {
    this.createTimeEnd = createTimeEnd;
    }
    public String getUpdateUser() {
    return updateUser;
    }
    public void setUpdateUser(String updateUser) {
    this.updateUser = updateUser;
    }
    public java.util.Date getUpdateTimeStart() {
    return updateTimeStart;
    }
    public void setUpdateTimeStart(java.util.Date updateTimeStart) {
    this.updateTimeStart = updateTimeStart;
    }
    public java.util.Date getUpdateTimeEnd() {
    return updateTimeEnd;
    }
    public void setUpdateTimeEnd(java.util.Date updateTimeEnd) {
    this.updateTimeEnd = updateTimeEnd;
    }
    public String getRemark() {
    return remark;
    }
    public void setRemark(String remark) {
    this.remark = remark;
    }
    public String getStartingAddDetail() {
    return startingAddDetail;
    }
    public void setStartingAddDetail(String startingAddDetail) {
    this.startingAddDetail = startingAddDetail;
    }
    public String getEndingAddDetail() {
    return endingAddDetail;
    }
    public void setEndingAddDetail(String endingAddDetail) {
    this.endingAddDetail = endingAddDetail;
    }
    public Double getDistance() {
    return distance;
    }
    public void setDistance(Double distance) {
    this.distance = distance;
    }
    public Integer getDuration() {
    return duration;
    }
    public void setDuration(Integer duration) {
    this.duration = duration;
    }
    public Double getOrderCommissionRatio() {
    return orderCommissionRatio;
    }
    public void setOrderCommissionRatio(Double orderCommissionRatio) {
    this.orderCommissionRatio = orderCommissionRatio;
    }
    public Double getOrderCommissionCap() {
    return orderCommissionCap;
    }
    public void setOrderCommissionCap(Double orderCommissionCap) {
    this.orderCommissionCap = orderCommissionCap;
    }

public String toString() {
return ToStringBuilder.reflectionToString(this);
}
}
