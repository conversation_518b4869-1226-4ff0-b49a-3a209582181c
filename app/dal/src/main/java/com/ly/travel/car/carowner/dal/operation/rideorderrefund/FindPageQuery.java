
/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.ly.travel.car.carowner.dal.operation.rideorderrefund;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;


import java.io.*;
import java.net.*;
import java.util.*;

import java.math.BigInteger;

import com.ly.sof.utils.page.PageQuery;
import com.ly.sof.utils.page.PageList;
import com.ly.sof.utils.page.PageQueryUtils;
/**
 * database table: ride_order_refund
 * database table comments: RideOrderRefund
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to 
 * directory <tt>(project-home)/dalgen</tt>
 * 
 * <AUTHOR> <PERSON>)
 **/
public class FindPageQuery  extends PageQuery implements java.io.Serializable {
private static final long serialVersionUID = -5216457518046898601L;

    /** 自增id */
    private Long id;
    /** 环境标识 */
    private String env;
    /** 订单编号 */
    private String orderNo;
    /** 退款金额 */
    private Double refundAmount;
    /** 退款状态- 0:退款, 1:退款成功, 2:退款失败 */
    private Integer refundStatus;
    /** 退款原因备注 */
    private String refundRemark;
    /** 退款时间 */
    private java.util.Date refundTimeStart;
    /** 退款时间 */
    private java.util.Date refundTimeEnd;
    /** 创建人 */
    private String createUser;
    /** 创建时间 */
    private java.util.Date createTimeStart;
    /** 创建时间 */
    private java.util.Date createTimeEnd;
    /** 备注 */
    private String remark;

public FindPageQuery() {
}

public FindPageQuery(Long id ,String env ,String orderNo ,Double refundAmount ,Integer refundStatus ,String refundRemark ,java.util.Date refundTimeStart ,java.util.Date refundTimeEnd ,String createUser ,java.util.Date createTimeStart ,java.util.Date createTimeEnd ,String remark ) {
    this.id = id;
    this.env = env;
    this.orderNo = orderNo;
    this.refundAmount = refundAmount;
    this.refundStatus = refundStatus;
    this.refundRemark = refundRemark;
    this.refundTimeStart = refundTimeStart;
    this.refundTimeEnd = refundTimeEnd;
    this.createUser = createUser;
    this.createTimeStart = createTimeStart;
    this.createTimeEnd = createTimeEnd;
    this.remark = remark;
}

    public Long getId() {
    return id;
    }
    public void setId(Long id) {
    this.id = id;
    }
    public String getEnv() {
    return env;
    }
    public void setEnv(String env) {
    this.env = env;
    }
    public String getOrderNo() {
    return orderNo;
    }
    public void setOrderNo(String orderNo) {
    this.orderNo = orderNo;
    }
    public Double getRefundAmount() {
    return refundAmount;
    }
    public void setRefundAmount(Double refundAmount) {
    this.refundAmount = refundAmount;
    }
    public Integer getRefundStatus() {
    return refundStatus;
    }
    public void setRefundStatus(Integer refundStatus) {
    this.refundStatus = refundStatus;
    }
    public String getRefundRemark() {
    return refundRemark;
    }
    public void setRefundRemark(String refundRemark) {
    this.refundRemark = refundRemark;
    }
    public java.util.Date getRefundTimeStart() {
    return refundTimeStart;
    }
    public void setRefundTimeStart(java.util.Date refundTimeStart) {
    this.refundTimeStart = refundTimeStart;
    }
    public java.util.Date getRefundTimeEnd() {
    return refundTimeEnd;
    }
    public void setRefundTimeEnd(java.util.Date refundTimeEnd) {
    this.refundTimeEnd = refundTimeEnd;
    }
    public String getCreateUser() {
    return createUser;
    }
    public void setCreateUser(String createUser) {
    this.createUser = createUser;
    }
    public java.util.Date getCreateTimeStart() {
    return createTimeStart;
    }
    public void setCreateTimeStart(java.util.Date createTimeStart) {
    this.createTimeStart = createTimeStart;
    }
    public java.util.Date getCreateTimeEnd() {
    return createTimeEnd;
    }
    public void setCreateTimeEnd(java.util.Date createTimeEnd) {
    this.createTimeEnd = createTimeEnd;
    }
    public String getRemark() {
    return remark;
    }
    public void setRemark(String remark) {
    this.remark = remark;
    }

public String toString() {
return ToStringBuilder.reflectionToString(this);
}
}
