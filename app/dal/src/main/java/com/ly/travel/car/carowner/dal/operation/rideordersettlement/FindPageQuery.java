
/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.ly.travel.car.carowner.dal.operation.rideordersettlement;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;


import java.io.*;
import java.net.*;
import java.util.*;

import java.math.BigInteger;

import com.ly.sof.utils.page.PageQuery;
import com.ly.sof.utils.page.PageList;
import com.ly.sof.utils.page.PageQueryUtils;
/**
 * database table: ride_order_settlement
 * database table comments: RideOrderSettlement
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to 
 * directory <tt>(project-home)/dalgen</tt>
 * 
 * <AUTHOR>
 **/
public class FindPageQuery  extends PageQuery implements java.io.Serializable {
private static final long serialVersionUID = -5216457518046898601L;

    /** 主键 */
    private Long id;
    /** 订单号 */
    private String orderNo;
    /** 司机ID */
    private Long driverId;
    /** 供应商结算价 */
    private Double supplierSettlementPrice;
    /** 平台结算收益 */
    private Double platformSettlementPrice;
    /** 供应商分佣比例 */
    private Double supplierMaidRate;
    /** 平台分佣比例 */
    private Double platformMaidRate;
    /** 司机结算价 */
    private Double driverSettlementPrice;
    /** 创建人 */
    private String createUser;
    /** 创建时间 */
    private java.util.Date createTimeStart;
    /** 创建时间 */
    private java.util.Date createTimeEnd;
    /** 修改人 */
    private String updateUser;
    /** 修改时间 */
    private java.util.Date updateTimeStart;
    /** 修改时间 */
    private java.util.Date updateTimeEnd;
    /** 状态 */
    private Integer status;

public FindPageQuery() {
}

public FindPageQuery(Long id ,String orderNo ,Long driverId ,Double supplierSettlementPrice ,Double platformSettlementPrice ,Double supplierMaidRate ,Double platformMaidRate ,Double driverSettlementPrice ,String createUser ,java.util.Date createTimeStart ,java.util.Date createTimeEnd ,String updateUser ,java.util.Date updateTimeStart ,java.util.Date updateTimeEnd ,Integer status ) {
    this.id = id;
    this.orderNo = orderNo;
    this.driverId = driverId;
    this.supplierSettlementPrice = supplierSettlementPrice;
    this.platformSettlementPrice = platformSettlementPrice;
    this.supplierMaidRate = supplierMaidRate;
    this.platformMaidRate = platformMaidRate;
    this.driverSettlementPrice = driverSettlementPrice;
    this.createUser = createUser;
    this.createTimeStart = createTimeStart;
    this.createTimeEnd = createTimeEnd;
    this.updateUser = updateUser;
    this.updateTimeStart = updateTimeStart;
    this.updateTimeEnd = updateTimeEnd;
    this.status = status;
}

    public Long getId() {
    return id;
    }
    public void setId(Long id) {
    this.id = id;
    }
    public String getOrderNo() {
    return orderNo;
    }
    public void setOrderNo(String orderNo) {
    this.orderNo = orderNo;
    }
    public Long getDriverId() {
    return driverId;
    }
    public void setDriverId(Long driverId) {
    this.driverId = driverId;
    }
    public Double getSupplierSettlementPrice() {
    return supplierSettlementPrice;
    }
    public void setSupplierSettlementPrice(Double supplierSettlementPrice) {
    this.supplierSettlementPrice = supplierSettlementPrice;
    }
    public Double getPlatformSettlementPrice() {
    return platformSettlementPrice;
    }
    public void setPlatformSettlementPrice(Double platformSettlementPrice) {
    this.platformSettlementPrice = platformSettlementPrice;
    }
    public Double getSupplierMaidRate() {
    return supplierMaidRate;
    }
    public void setSupplierMaidRate(Double supplierMaidRate) {
    this.supplierMaidRate = supplierMaidRate;
    }
    public Double getPlatformMaidRate() {
    return platformMaidRate;
    }
    public void setPlatformMaidRate(Double platformMaidRate) {
    this.platformMaidRate = platformMaidRate;
    }
    public Double getDriverSettlementPrice() {
    return driverSettlementPrice;
    }
    public void setDriverSettlementPrice(Double driverSettlementPrice) {
    this.driverSettlementPrice = driverSettlementPrice;
    }
    public String getCreateUser() {
    return createUser;
    }
    public void setCreateUser(String createUser) {
    this.createUser = createUser;
    }
    public java.util.Date getCreateTimeStart() {
    return createTimeStart;
    }
    public void setCreateTimeStart(java.util.Date createTimeStart) {
    this.createTimeStart = createTimeStart;
    }
    public java.util.Date getCreateTimeEnd() {
    return createTimeEnd;
    }
    public void setCreateTimeEnd(java.util.Date createTimeEnd) {
    this.createTimeEnd = createTimeEnd;
    }
    public String getUpdateUser() {
    return updateUser;
    }
    public void setUpdateUser(String updateUser) {
    this.updateUser = updateUser;
    }
    public java.util.Date getUpdateTimeStart() {
    return updateTimeStart;
    }
    public void setUpdateTimeStart(java.util.Date updateTimeStart) {
    this.updateTimeStart = updateTimeStart;
    }
    public java.util.Date getUpdateTimeEnd() {
    return updateTimeEnd;
    }
    public void setUpdateTimeEnd(java.util.Date updateTimeEnd) {
    this.updateTimeEnd = updateTimeEnd;
    }
    public Integer getStatus() {
    return status;
    }
    public void setStatus(Integer status) {
    this.status = status;
    }

public String toString() {
return ToStringBuilder.reflectionToString(this);
}
}
