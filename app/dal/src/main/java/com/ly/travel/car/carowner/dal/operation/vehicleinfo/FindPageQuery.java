
/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.ly.travel.car.carowner.dal.operation.vehicleinfo;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;


import java.io.*;
import java.net.*;
import java.util.*;

import java.math.BigInteger;

import com.ly.sof.utils.page.PageQuery;
import com.ly.sof.utils.page.PageList;
import com.ly.sof.utils.page.PageQueryUtils;
/**
 * database table: vehicle_info
 * database table comments: VehicleInfo
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to 
 * directory <tt>(project-home)/dalgen</tt>
 * 
 * <AUTHOR>
 **/
public class FindPageQuery  extends Page<PERSON><PERSON>y implements java.io.Serializable {
private static final long serialVersionUID = -5216457518046898601L;

    /** 自增id */
    private Long id;
    /** 环境 */
    private String env;
    /** 车牌 */
    private String vehicleNo;
    /** 司机，默认为0 */
    private Long driverId;
    /** 车牌号城市 */
    private String vehicleNoCity;
    /** 车辆性质 02小型汽车、52新能源小型车 */
    private String vehicleType;
    /** 车辆所有人 */
    private String vehicleOwner;
    /** 车辆使用性质 非营运、营转非、出租转非、预约出租转非 */
    private String useCharacter;
    /** 品牌编号 */
    private String carBrand;
    /** 车型编号 */
    private String carModel;
    /** 车辆识别代号 */
    private String vin;
    /** 颜色 */
    private String color;
    /** 注册日期 */
    private String registerDate;
    /** 发动机号码 */
    private String engineNo;
    /** 发证日期 */
    private String issueDate;
    /** 核定座数 */
    private Integer authSeatNum;
    /** 乘客座数 */
    private Integer passSeatNum;
    /** 行驶证图片 */
    private String drivingLicensePicture;
    /** 燃料类型 柴油 汽油 电动 混动 */
    private String fuelType;
    /** 品牌型号 行驶证识别 */
    private String brandModel;
    /** 车辆状态 */
    private String vehicleStatus;
    /** 检验有效期止 */
    private String inspectEndDate;
    /** 年检日期 */
    private String checkDate;
    /** 状态 1.未认证 2.待系统认证 3.待人工认证 4.认证通过 5.认证不通过 */
    private Integer status;
    /** 风控ocr不通过原因 */
    private String verifyRemark;
    /** 审核不通过原因 1.证件照片不清晰 2.证件照片作假 3.驾驶证已过期 4.准驾车型不符 5.驾龄不满足要求 6.驾驶证状态异常 7.其他 */
    private Integer unauditReason;
    /** 审核不通过其他原因 */
    private String unauditRemark;
    /** 车辆照片识别时间 */
    private java.util.Date vehiclePictureStartDateStart;
    /** 车辆照片识别时间 */
    private java.util.Date vehiclePictureStartDateEnd;
    /** 车辆照片识别有效时长 */
    private Integer vehicleValidPeriod;
    /** 车辆照片识别时间 */
    private java.util.Date vehiclePicture;
    /** 创建人 */
    private String createUser;
    /** 创建时间 */
    private java.util.Date createTimeStart;
    /** 创建时间 */
    private java.util.Date createTimeEnd;
    /** 修改人 */
    private String updateUser;
    /** 修改时间 */
    private java.util.Date updateTimeStart;
    /** 修改时间 */
    private java.util.Date updateTimeEnd;

public FindPageQuery() {
}

public FindPageQuery(Long id ,String env ,String vehicleNo ,Long driverId ,String vehicleNoCity ,String vehicleType ,String vehicleOwner ,String useCharacter ,String carBrand ,String carModel ,String vin ,String color ,String registerDate ,String engineNo ,String issueDate ,Integer authSeatNum ,Integer passSeatNum ,String drivingLicensePicture ,String fuelType ,String brandModel ,String vehicleStatus ,String inspectEndDate ,String checkDate ,Integer status ,String verifyRemark ,Integer unauditReason ,String unauditRemark ,java.util.Date vehiclePictureStartDateStart ,java.util.Date vehiclePictureStartDateEnd ,Integer vehicleValidPeriod ,java.util.Date vehiclePicture ,String createUser ,java.util.Date createTimeStart ,java.util.Date createTimeEnd ,String updateUser ,java.util.Date updateTimeStart ,java.util.Date updateTimeEnd ) {
    this.id = id;
    this.env = env;
    this.vehicleNo = vehicleNo;
    this.driverId = driverId;
    this.vehicleNoCity = vehicleNoCity;
    this.vehicleType = vehicleType;
    this.vehicleOwner = vehicleOwner;
    this.useCharacter = useCharacter;
    this.carBrand = carBrand;
    this.carModel = carModel;
    this.vin = vin;
    this.color = color;
    this.registerDate = registerDate;
    this.engineNo = engineNo;
    this.issueDate = issueDate;
    this.authSeatNum = authSeatNum;
    this.passSeatNum = passSeatNum;
    this.drivingLicensePicture = drivingLicensePicture;
    this.fuelType = fuelType;
    this.brandModel = brandModel;
    this.vehicleStatus = vehicleStatus;
    this.inspectEndDate = inspectEndDate;
    this.checkDate = checkDate;
    this.status = status;
    this.verifyRemark = verifyRemark;
    this.unauditReason = unauditReason;
    this.unauditRemark = unauditRemark;
    this.vehiclePictureStartDateStart = vehiclePictureStartDateStart;
    this.vehiclePictureStartDateEnd = vehiclePictureStartDateEnd;
    this.vehicleValidPeriod = vehicleValidPeriod;
    this.vehiclePicture = vehiclePicture;
    this.createUser = createUser;
    this.createTimeStart = createTimeStart;
    this.createTimeEnd = createTimeEnd;
    this.updateUser = updateUser;
    this.updateTimeStart = updateTimeStart;
    this.updateTimeEnd = updateTimeEnd;
}

    public Long getId() {
    return id;
    }
    public void setId(Long id) {
    this.id = id;
    }
    public String getEnv() {
    return env;
    }
    public void setEnv(String env) {
    this.env = env;
    }
    public String getVehicleNo() {
    return vehicleNo;
    }
    public void setVehicleNo(String vehicleNo) {
    this.vehicleNo = vehicleNo;
    }
    public Long getDriverId() {
    return driverId;
    }
    public void setDriverId(Long driverId) {
    this.driverId = driverId;
    }
    public String getVehicleNoCity() {
    return vehicleNoCity;
    }
    public void setVehicleNoCity(String vehicleNoCity) {
    this.vehicleNoCity = vehicleNoCity;
    }
    public String getVehicleType() {
    return vehicleType;
    }
    public void setVehicleType(String vehicleType) {
    this.vehicleType = vehicleType;
    }
    public String getVehicleOwner() {
    return vehicleOwner;
    }
    public void setVehicleOwner(String vehicleOwner) {
    this.vehicleOwner = vehicleOwner;
    }
    public String getUseCharacter() {
    return useCharacter;
    }
    public void setUseCharacter(String useCharacter) {
    this.useCharacter = useCharacter;
    }
    public String getCarBrand() {
    return carBrand;
    }
    public void setCarBrand(String carBrand) {
    this.carBrand = carBrand;
    }
    public String getCarModel() {
    return carModel;
    }
    public void setCarModel(String carModel) {
    this.carModel = carModel;
    }
    public String getVin() {
    return vin;
    }
    public void setVin(String vin) {
    this.vin = vin;
    }
    public String getColor() {
    return color;
    }
    public void setColor(String color) {
    this.color = color;
    }
    public String getRegisterDate() {
    return registerDate;
    }
    public void setRegisterDate(String registerDate) {
    this.registerDate = registerDate;
    }
    public String getEngineNo() {
    return engineNo;
    }
    public void setEngineNo(String engineNo) {
    this.engineNo = engineNo;
    }
    public String getIssueDate() {
    return issueDate;
    }
    public void setIssueDate(String issueDate) {
    this.issueDate = issueDate;
    }
    public Integer getAuthSeatNum() {
    return authSeatNum;
    }
    public void setAuthSeatNum(Integer authSeatNum) {
    this.authSeatNum = authSeatNum;
    }
    public Integer getPassSeatNum() {
    return passSeatNum;
    }
    public void setPassSeatNum(Integer passSeatNum) {
    this.passSeatNum = passSeatNum;
    }
    public String getDrivingLicensePicture() {
    return drivingLicensePicture;
    }
    public void setDrivingLicensePicture(String drivingLicensePicture) {
    this.drivingLicensePicture = drivingLicensePicture;
    }
    public String getFuelType() {
    return fuelType;
    }
    public void setFuelType(String fuelType) {
    this.fuelType = fuelType;
    }
    public String getBrandModel() {
    return brandModel;
    }
    public void setBrandModel(String brandModel) {
    this.brandModel = brandModel;
    }
    public String getVehicleStatus() {
    return vehicleStatus;
    }
    public void setVehicleStatus(String vehicleStatus) {
    this.vehicleStatus = vehicleStatus;
    }
    public String getInspectEndDate() {
    return inspectEndDate;
    }
    public void setInspectEndDate(String inspectEndDate) {
    this.inspectEndDate = inspectEndDate;
    }
    public String getCheckDate() {
    return checkDate;
    }
    public void setCheckDate(String checkDate) {
    this.checkDate = checkDate;
    }
    public Integer getStatus() {
    return status;
    }
    public void setStatus(Integer status) {
    this.status = status;
    }
    public String getVerifyRemark() {
    return verifyRemark;
    }
    public void setVerifyRemark(String verifyRemark) {
    this.verifyRemark = verifyRemark;
    }
    public Integer getUnauditReason() {
    return unauditReason;
    }
    public void setUnauditReason(Integer unauditReason) {
    this.unauditReason = unauditReason;
    }
    public String getUnauditRemark() {
    return unauditRemark;
    }
    public void setUnauditRemark(String unauditRemark) {
    this.unauditRemark = unauditRemark;
    }
    public java.util.Date getVehiclePictureStartDateStart() {
    return vehiclePictureStartDateStart;
    }
    public void setVehiclePictureStartDateStart(java.util.Date vehiclePictureStartDateStart) {
    this.vehiclePictureStartDateStart = vehiclePictureStartDateStart;
    }
    public java.util.Date getVehiclePictureStartDateEnd() {
    return vehiclePictureStartDateEnd;
    }
    public void setVehiclePictureStartDateEnd(java.util.Date vehiclePictureStartDateEnd) {
    this.vehiclePictureStartDateEnd = vehiclePictureStartDateEnd;
    }
    public Integer getVehicleValidPeriod() {
    return vehicleValidPeriod;
    }
    public void setVehicleValidPeriod(Integer vehicleValidPeriod) {
    this.vehicleValidPeriod = vehicleValidPeriod;
    }
    public java.util.Date getVehiclePicture() {
    return vehiclePicture;
    }
    public void setVehiclePicture(java.util.Date vehiclePicture) {
    this.vehiclePicture = vehiclePicture;
    }
    public String getCreateUser() {
    return createUser;
    }
    public void setCreateUser(String createUser) {
    this.createUser = createUser;
    }
    public java.util.Date getCreateTimeStart() {
    return createTimeStart;
    }
    public void setCreateTimeStart(java.util.Date createTimeStart) {
    this.createTimeStart = createTimeStart;
    }
    public java.util.Date getCreateTimeEnd() {
    return createTimeEnd;
    }
    public void setCreateTimeEnd(java.util.Date createTimeEnd) {
    this.createTimeEnd = createTimeEnd;
    }
    public String getUpdateUser() {
    return updateUser;
    }
    public void setUpdateUser(String updateUser) {
    this.updateUser = updateUser;
    }
    public java.util.Date getUpdateTimeStart() {
    return updateTimeStart;
    }
    public void setUpdateTimeStart(java.util.Date updateTimeStart) {
    this.updateTimeStart = updateTimeStart;
    }
    public java.util.Date getUpdateTimeEnd() {
    return updateTimeEnd;
    }
    public void setUpdateTimeEnd(java.util.Date updateTimeEnd) {
    this.updateTimeEnd = updateTimeEnd;
    }

public String toString() {
return ToStringBuilder.reflectionToString(this);
}
}
