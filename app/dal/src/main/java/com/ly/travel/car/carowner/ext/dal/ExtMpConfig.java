package com.ly.travel.car.carowner.ext.dal;


import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Configuration;

@Configuration
@MapperScan(basePackages = "com.ly.travel.car.carowner.ext.dal.mapper", sqlSessionFactoryRef = "extSqlSessionFactory")
public class ExtMpConfig {
//    @Bean
//    public GlobalConfig globalConfiguration() {
//        GlobalConfig conf = new GlobalConfig();
//        conf.setDbConfig(new GlobalConfig.DbConfig());
//        return conf;
//    }

//    @Bean
//    public MybatisSqlSessionFactoryBean sqlSessionFactory(DataSource dataSource){
//        MybatisSqlSessionFactoryBean sqlSessionFactory=new MybatisSqlSessionFactoryBean();
//        sqlSessionFactory.setDataSource(dataSource);
//
//        return sqlSessionFactory;
//    }
}
