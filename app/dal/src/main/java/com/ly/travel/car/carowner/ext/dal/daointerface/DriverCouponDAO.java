/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.ly.travel.car.carowner.ext.dal.daointerface;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.travel.car.carowner.ext.dal.dataobject.DriverCouponDO;

import java.util.List;

/**
* DriverCouponDAO
 * database table: driver_coupon
 * database table comments: DriverCoupon
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to 
 * directory <tt>(project-home)/dalgen</tt>
 * 
 * <AUTHOR> <PERSON>)
 **/
public interface DriverCouponDAO extends IService<DriverCouponDO> {

    DriverCouponDO queryByOrderNo(Long driverId, String orderNo, String env);

    List<DriverCouponDO> queryByDriverId(Long driverId, String env, Integer status);

    boolean updateFreezeState(Long id, String orderNo, Integer status);

    boolean updateUsedState(Long id, Integer status);

    boolean updateUnfreezeState(Long id, Integer status);

    /**
     * @param entity
     */
    boolean saveDriverCoupon(DriverCouponDO entity);

    /**
     * @param driverId
     * @param batchNo
     * @param couponType
     * @param scene
     * @return
     */
    long queryDriverCouponCount(Long driverId, String batchNo, Integer couponType, String scene);
}
