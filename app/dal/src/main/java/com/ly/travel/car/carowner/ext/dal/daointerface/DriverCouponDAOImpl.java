package com.ly.travel.car.carowner.ext.dal.daointerface;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ly.travel.car.carowner.ext.dal.dataobject.DriverCouponDO;
import com.ly.travel.car.carowner.ext.dal.mapper.DriverCouponMapper;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DriverCouponDAOImpl extends ServiceImpl<DriverCouponMapper, DriverCouponDO> implements DriverCouponDAO {
    @Override
    public DriverCouponDO queryByOrderNo(Long driverId, String orderNo, String env) {
        return baseMapper.selectOne(new LambdaQueryWrapper<DriverCouponDO>().eq(DriverCouponDO::getDriverId, driverId)
            .eq(DriverCouponDO::getOrderNo, orderNo).eq(DriverCouponDO::getEnv, env), false);
    }

    @Override
    public List<DriverCouponDO> queryByDriverId(Long driverId, String env, Integer status) {
        return lambdaQuery().eq(DriverCouponDO::getDriverId, driverId).eq(DriverCouponDO::getEnv, env).eq(DriverCouponDO::getStatus, status)
            .list();
    }

    @Override
    public boolean updateFreezeState(Long id, String orderNo, Integer status) {
        DriverCouponDO aDo = new DriverCouponDO();
        aDo.setId(id);
        aDo.setStatus(status);
        aDo.setOrderNo(orderNo);
        return updateById(aDo);
    }

    @Override
    public boolean updateUsedState(Long id, Integer status) {
        DriverCouponDO aDo = new DriverCouponDO();
        aDo.setId(id);
        aDo.setStatus(status);
        aDo.setGmtUsed(new java.util.Date());
        return updateById(aDo);
    }

    @Override
    public boolean updateUnfreezeState(Long id, Integer status) {
        DriverCouponDO aDo = new DriverCouponDO();
        aDo.setId(id);
        aDo.setStatus(status);
        aDo.setOrderNo("");
        aDo.setGmtRefund(new java.util.Date());
        return updateById(aDo);
    }

    @Override
    public boolean saveDriverCoupon(DriverCouponDO entity) {
        return save(entity);
    }

    @Override
    public long queryDriverCouponCount(Long driverId, String batchNo, Integer couponType, String scene) {
        return lambdaQuery()
            // 司机
            .eq(DriverCouponDO::getDriverId, driverId)
            // 批次号
            .eq(DriverCouponDO::getBatchNo, batchNo)
            // 券类型
            .eq(DriverCouponDO::getCouponType, couponType)
            // 券场景
            .eq(DriverCouponDO::getScene, scene).count();
    }
}
