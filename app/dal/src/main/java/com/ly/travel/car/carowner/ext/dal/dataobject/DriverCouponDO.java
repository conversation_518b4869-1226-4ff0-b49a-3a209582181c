/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.ly.travel.car.carowner.ext.dal.dataobject;

import lombok.Data;
import lombok.Getter;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.util.*;

/**
* DriverCouponDO
 * database table: driver_coupon
 * database table comments: DriverCoupon
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to 
 * directory <tt>(project-home)/dalgen</tt>
 * 
 * <AUTHOR> Junxiang)
 **/
@Data
@TableName("driver_coupon")
public class DriverCouponDO implements java.io.Serializable {

    private static final long serialVersionUID = -5216457518046898601L;

    /**
     * 自增id        db_column: id
     */

    @TableId(value = "id", type = IdType.AUTO)
    private Long              id;
    /**
     * 司机账户ID        db_column: driver_id
     */

    @TableField(value = "driver_id")
    private Long              driverId;
    /**
     * 批次号        db_column: batch_no
     */

    @Getter
    @TableField(value = "batch_no")
    private String            batchNo;
    /**
     * 券号        db_column: coupon_no
     */

    @TableField(value = "coupon_no")
    private String            couponNo;
    /**
     * 券名称        db_column: name
     */

    @TableField(value = "name")
    private String            name;
    /**
     * 券类型:1-免佣卡        db_column: coupon_type
     */

    @TableField(value = "coupon_type")
    private Integer           couponType;
    /**
     * 门槛        db_column: sill
     */

    @TableField(value = "sill")
    private BigDecimal        sill;
    /**
     * 券有效开始时间        db_column: gmt_coupon_begin
     */

    @TableField(value = "gmt_coupon_begin")
    private Date              gmtCouponBegin;
    /**
     * 券有效截止时间        db_column: gmt_coupon_end
     */

    @TableField(value = "gmt_coupon_end")
    private Date              gmtCouponEnd;
    /**
     * 券领取时间        db_column: gmt_received
     */

    @TableField(value = "gmt_received")
    private Date              gmtReceived;
    /**
     * 券退还时间        db_column: gmt_refund
     */

    @TableField(value = "gmt_refund")
    private Date              gmtRefund;
    /**
     * 券核销时间        db_column: gmt_used
     */

    @TableField(value = "gmt_used")
    private Date              gmtUsed;
    /**
     * 使用的订单号        db_column: order_no
     */

    @TableField(value = "order_no")
    private String            orderNo;
    /**
     * 发放场景:司机认证、完单        db_column: scene
     */

    @TableField(value = "scene")
    private String            scene;
    /**
     * 券拓展        db_column: ext
     */

    @TableField(value = "ext")
    private String            ext;

    /**
     * 券状态:0-待使用,1-已占用,2-已核销,3-已过期        db_column: status
     */

    @TableField(value = "status")
    private Integer           status;
    /**
     * 环境        db_column: env
     */

    @TableField(value = "env")
    private String            env;
    /**
     * 创建人        db_column: create_user
     */

    @TableField(value = "create_user")
    private String            createUser;
    /**
     * 券领取时间        db_column: create_time
     */

    @TableField(value = "create_time")
    private Date              createTime;
    /**
     * 修改人        db_column: update_user
     */

    @TableField(value = "update_user")
    private String            updateUser;
    /**
     * 修改时间        db_column: update_time
     */

    @TableField(value = "update_time")
    private Date              updateTime;

}
