/**
 * LY.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */package com.ly.travel.car.carowner.ext.dal.mapper;
import com.ly.travel.car.carowner.ext.dal.dataobject.DriverCouponDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
* DriverCouponMapper
 * database table: driver_coupon
 * database table comments: DriverCoupon
 * This file is generated by <tt>dalgen</tt>, a DAL (Data Access Layer)
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to 
 * directory <tt>(project-home)/dalgen</tt>
 * 
 * <AUTHOR> <PERSON>)
 **/

@Mapper
public interface DriverCouponMapper extends BaseMapper
<DriverCouponDO> {
    }


