<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="
		http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/tx
		http://www.springframework.org/schema/tx/spring-tx.xsd
		http://www.springframework.org/schema/aop
		http://www.springframework.org/schema/aop/spring-aop.xsd
		http://www.springframework.org/schema/context
		http://www.springframework.org/schema/context/spring-context.xsd"
       default-autowire="byName">

    <context:annotation-config/>
    <aop:aspectj-autoproxy proxy-target-class="true"/>

    <!-- Only one tx:annotation-driven can be marked in the Spring 4 Container.
         If you have configured more than one TransactionManagers, please use "qualifier" to mark your TransactionManager. -->
    <!--<tx:annotation-driven/>-->

    <!-- 	<bean id="datasource" class="org.apache.commons.dbcp.BasicDataSource" destroy-method="close" lazy-init="default" autowire="default">-->
    <!-- 		<property name="driverClassName" value="${jdbc.driverClassName}" />-->
    <!-- 		<property name="url" value="${jdbc.url}" />-->
    <!-- 		<property name="username" value="${jdbc.username}" />-->
    <!-- 		<property name="password" value="${jdbc.pwd}" />-->
    <!-- 		<property name="initialSize" value="${initialSize}" />-->
    <!-- 		<property name="maxIdle" value="${maxIdle}" />-->
    <!-- 		<property name="maxActive" value="${maxActive}" />-->
    <!-- 		<property name="maxWait" value="${maxWait}" />-->
    <!-- 		<property name="logAbandoned" value="${logAbandoned}" />-->
    <!-- 		<property name="removeAbandoned" value="${removeAbandoned}" />-->
    <!-- 		<property name="removeAbandonedTimeout" value="${removeAbandonedTimeout}" />-->
    <!-- 		<property name="validationQuery" value="${validationQuery}" />-->
    <!-- 		<property name="testWhileIdle" value="${testWhileIdle}" />-->
    <!-- 		<property name="testOnBorrow" value="${testOnBorrow}" />-->
    <!-- 		<property name="testOnReturn" value="${testOnReturn}" />-->
    <!-- 		<property name="timeBetweenEvictionRunsMillis" value="${timeBetweenEvictionRunsMillis}" />-->
    <!-- 		<property name="numTestsPerEvictionRun" value="${numTestsPerEvictionRun}" />-->
    <!-- 	</bean>-->

    <!-- 填写数据库配置，解开以下注释 设置数据库环境 test：线下测试、stage：预发、product：线上正式 -->
    <bean id="extDataSource" class="com.ly.dal.datasource.RoutableDataSource" init-method="init" destroy-method="close">
        <property name="dbName" value="TECarOwnerExt"/>
    </bean>

    <bean id="extDatasourceProxy" class="org.springframework.jdbc.datasource.LazyConnectionDataSourceProxy">
        <property name="targetDataSource" ref="extDataSource"/>
    </bean>

    <bean id="extTransactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="extDatasourceProxy"/>
        <qualifier value="extTmQualifierName"/>
    </bean>

    <bean id="extTransactionTemplate" class="org.springframework.transaction.support.TransactionTemplate">
        <property name="transactionManager">
            <ref bean="extTransactionManager"/>
        </property>
    </bean>

    <bean id="extSqlMapClientDAO" abstract="true">
        <property name="dataSource">
            <ref bean="extDatasourceProxy"/>
        </property>
        <property name="sqlMapClient">
            <ref bean="extSqlMapClient"/>
        </property>
    </bean>
    <bean id="extSqlMapClient" class="org.springframework.orm.ibatis.SqlMapClientFactoryBean">
        <property name="dataSource" ref="extDatasourceProxy"/>
        <property name="configLocations">
            <list>
                <value>classpath:sqlmap/ext/ext-sqlmap-config.xml</value>
            </list>
        </property>
        <property name="lobHandler" ref="extLobHandler"/>
    </bean>

    <bean id="extSqlSessionFactory" class="com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean">
        <property name="dataSource" ref="extDatasourceProxy"/>
        <!--        <property name="configuration" ref="configuration"/>-->
        <property name="mapperLocations" value="classpath:mappers/ext/*.xml"/>
        <property name="globalConfig" ref="extGlobalConfig"/>
        <property name="plugins">
            <array>
                <ref bean="extMybatisPlusInterceptor"/>
            </array>
        </property>
    </bean>

    <!--    <bean id="configuration" class="com.baomidou.mybatisplus.core.MybatisConfiguration">-->
    <!--        <property name="useDeprecatedExecutor" value="false"/>-->
    <!--    </bean>-->

    <bean id="extMybatisPlusInterceptor" class="com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor">
        <property name="interceptors">
            <list>
                <ref bean="extPaginationInnerInterceptor"/>
            </list>
        </property>
    </bean>

    <bean id="extPaginationInnerInterceptor"
          class="com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor">
        <constructor-arg name="dbType" value="MYSQL"/>
        <property name="optimizeJoin" value="true"/>
    </bean>

    <!--    <bean id="countSqlParser"-->
    <!--          class="com.baomidou.mybatisplus.extension.plugins.pagination.optimize.JsqlParserCountOptimize">-->
    <!--        <property name="optimizeJoin" value="true"/>-->
    <!--    </bean>-->

    <bean id="extGlobalConfig" class="com.baomidou.mybatisplus.core.config.GlobalConfig" lazy-init="true">
        <property name="dbConfig" ref="extDbConfig"/>
        <property name="sqlSessionFactory">
            <null></null>
        </property>
    </bean>

    <bean id="extDbConfig" class="com.baomidou.mybatisplus.core.config.GlobalConfig.DbConfig">
    </bean>


    <bean id="extLobHandler" class="org.springframework.jdbc.support.lob.DefaultLobHandler"/>

</beans>
