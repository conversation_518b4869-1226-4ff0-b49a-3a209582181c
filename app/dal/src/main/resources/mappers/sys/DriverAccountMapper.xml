<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ly.travel.car.carowner.dal.mapper.DriverAccountMapper">
    <update id="updateAmountByDriverId" parameterType="com.ly.travel.car.carowner.dal.dataobject.DriverAccountDO">
        UPDATE driver_account
        SET
        <if test="totalAmount != null">
            total_amount = total_amount + #{totalAmount,jdbcType=DECIMAL},
        </if>
        <if test="freezeAmount != null">
            freeze_amount = freeze_amount + #{freezeAmount,jdbcType=DECIMAL},
        </if>
        <if test="withdrawingAmount != null">
            withdrawing_amount = withdrawing_amount + #{withdrawingAmount,jdbcType=DECIMAL},
        </if>
        <if test="withdrawnAmount != null">
            withdrawn_amount = withdrawn_amount + #{withdrawnAmount,jdbcType=DECIMAL},
        </if>
        <if test="availableAmount != null">
            available_amount = available_amount + #{availableAmount,jdbcType=DECIMAL},
        </if>
        <if test="rewardAmount != null">
            reward_amount = reward_amount + #{rewardAmount},
        </if>
        update_user = #{updateUser,jdbcType=VARCHAR},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        WHERE
        driver_id = #{driverId,jdbcType=BIGINT}
    </update>
</mapper>
