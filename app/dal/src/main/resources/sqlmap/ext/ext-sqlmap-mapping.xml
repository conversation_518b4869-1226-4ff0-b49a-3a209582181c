<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"
        "http://ibatis.apache.org/dtd/sql-map-2.dtd">

<!-- ==========================================================  -->
<!-- Configuration for ibatis sqlmap mapping.                    -->
<!-- ==========================================================  -->

<sqlMap namespace="sys">

    <!-- ============================================= -->
    <!-- mapped statements for SEQUENCE                -->
    <!-- ============================================= -->

    <!-- result map for Money class -->
    <resultMap id="RM.Money" class="com.ly.sof.utils.common.Money">
        <result property="cent" columnIndex="1" jdbcType="NUMBER" nullValue="0"/>
    </resultMap>
    <!-- result map for Money class -->
    <resultMap id="paygw.RM-MONEY" class="com.ly.sof.utils.common.Money">
        <result property="cent" columnIndex="1" jdbcType="NUMBER" nullValue="0"/>
    </resultMap>


</sqlMap>
