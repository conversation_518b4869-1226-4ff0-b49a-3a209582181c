<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
        <groupId>com.ly.travel.car.carowner</groupId>
        <artifactId>shared-mobility-carowner-core</artifactId>
        <version>1.0-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>shared-mobility-carowner-core-facade-impl</artifactId>
    <packaging>jar</packaging>

    <name>LY shared-mobility-carowner-core-facade-impl</name>
    <description>LY shared-mobility-carowner-core-facade-impl</description>

    <dependencies>
        <!-- project depends -->
        <dependency>
            <groupId>com.ly.travel.car.carowner</groupId>
            <artifactId>shared-mobility-carowner-core-biz</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ly.sof</groupId>
            <artifactId>sof-core</artifactId>
        </dependency>

        <!-- Test dependecies -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-all</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.easymock</groupId>
            <artifactId>easymock</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-easymock</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
