package com.ly.travel.car.carowner.facade;

import com.ly.sof.utils.log.LogContextUtils;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.common.exception.BusinessException;
import com.ly.travel.car.carowner.common.utils.LogUtil;
import com.ly.travel.car.carowner.facade.response.CarOwnerResponseDTO;
import com.tcelong.hubble.probe.util.threadlocal.ApmTraceUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * controller切面，参数放到request中，然后在filter中chain.doFilter后获取
 */
@Component
@Aspect
public class ApiControllerAspect {
    Logger log= LoggerFactory.getLogger(LogUtil.class);


    /**
     * 获取到参数放到request中，然后在filter中chain.doFilter后获取
     *
     * @param pjp
     * @return
     * @throws Throwable
     */
    @Around("execution(* com.ly.travel.car.carowner.facade.impl.*.*(..))")
    public Object doAfterController(ProceedingJoinPoint pjp) throws Throwable {
        Object result = null;

        String methodName = pjp.getSignature().getName();
        String className = pjp.getTarget().getClass().getSimpleName();
        boolean shouldLog = !"MonitorController".equals(className);

        if (shouldLog) {
            LogContextUtils.setModule("ControllerAspect");
            LogContextUtils.setCategory(className);
            LogContextUtils.setSubCategory(methodName);
            LoggerUtils.info(log, "请求：{}", FastJsonUtils.toJSONString(getParams(pjp)));
        }

        try {
            result = pjp.proceed();
        } catch (BusinessException e) {
            LogContextUtils.setModule("ControllerAspect");
            LogContextUtils.setCategory(className);
            LogContextUtils.setSubCategory(methodName);
            LoggerUtils.warn(log, e.getMessage(), e);
            return CarOwnerResponseDTO.fail(e.getMessage()).setTraceId(ApmTraceUtils.getTraceId());
        } catch (Throwable e) {
            if (shouldLog) {
                LogContextUtils.setModule("ControllerAspect");
                LogContextUtils.setCategory(className);
                LogContextUtils.setSubCategory(methodName);
                LoggerUtils.error(log,e.getMessage(), e);
            }
            return CarOwnerResponseDTO.fail(e.getMessage()).setTraceId(ApmTraceUtils.getTraceId());
        } finally {
            if (shouldLog) {
                LogContextUtils.setModule("ControllerAspect");
                LogContextUtils.setCategory(className);
                LogContextUtils.setSubCategory(methodName);
                LoggerUtils.info(log, "返回:{}", FastJsonUtils.toJSONString((result)));
            }
        }

        if(result instanceof  CarOwnerResponseDTO){
            ((CarOwnerResponseDTO)result).setTraceId(ApmTraceUtils.getTraceId());
        }

        return result;
    }

    /**
     * 根据切面获取到当前方法
     *
     * @param thisJoinPoint
     * @return
     * @throws NoSuchMethodException
     * @throws SecurityException
     */
    private Method getMethodFromJoinPoint(ProceedingJoinPoint thisJoinPoint)
            throws NoSuchMethodException, SecurityException {
        Signature sig = thisJoinPoint.getSignature();
        MethodSignature msig = (MethodSignature) sig;
        Object target = thisJoinPoint.getTarget();
        return target.getClass().getMethod(msig.getName(), msig.getParameterTypes());
    }

    /**
     * 获取当前方法的参数
     *
     * @param thisJoinPoint
     * @return
     */
    private Map<String, Object> getParams(ProceedingJoinPoint thisJoinPoint) {
        Map<String, Object> paramMaps = new HashMap<>();
        try {
            Method currentMethod = getMethodFromJoinPoint(thisJoinPoint);
            LocalVariableTableParameterNameDiscoverer u = new LocalVariableTableParameterNameDiscoverer();
            Class<?>[] parameterTypes = currentMethod.getParameterTypes();
            String[] parameterNames = u.getParameterNames(currentMethod);
            Object[] args = thisJoinPoint.getArgs();
            for (int i = 0; i < args.length; i++) {
                String fullName = parameterTypes[i].getName();
                paramMaps.put(parameterNames[i], FastJsonUtils.toJSONString(args[i]));
            }
        } catch (Exception e) {
            LoggerUtils.warn(log, "[Controller][{}.{}] >>> {}", "ApiControllerAspect", "getParams", e.getMessage(), e);
        }
        return paramMaps;
    }
}
