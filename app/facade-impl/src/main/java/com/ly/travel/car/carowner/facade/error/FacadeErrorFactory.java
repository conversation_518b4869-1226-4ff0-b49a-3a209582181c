package com.ly.travel.car.carowner.facade.error;

import com.ly.sof.api.error.AbstractErrorFactory;
import com.ly.sof.api.error.LYError;

public class FacadeErrorFactory extends AbstractErrorFactory {
    /** 分隔符 */
    private static final String SPLIT = ",";

    /**
     * 获取异常工厂单例
     *
     * @return 异常工厂单例
     */
    public static FacadeErrorFactory getInstance() {
        return FacadeErrorFactoryHolder.INSTANCE;
    }

    private static final class FacadeErrorFactoryHolder {
        /** 单例 */
        private static final FacadeErrorFactory INSTANCE = new FacadeErrorFactory();
    }

    /**
     * 获取资源文件名称
     *
     * @return 资源文件名称
     */
    @Override
    protected String provideErrorBundleName() {
        return "facade";
    }

    /**
     * LY0510010000=系统异常,详情:{0}
     *
     * @param msg 参数
     * @return 同程异常
     */
    public LYError sysErr(String msg) {
        return createError("LY0510010000", msg);
    }

    /**
     * LY0510030000=司机确认行程异常
     * @param message
     * @return
     */
    public LYError confirmAcceptError(String message) {
        return createError("LY0510030000", message);
    }

    /**
     * LY0510000006=抢单失败，订单已无效
     * @param message
     * @return
     */
    public LYError confirmAcceptFailError(String message) {
        return createError("LY0510000006", message);
    }

    /**
     * LY0510030002=乘客订单已经被取消
     * @param message
     * @return
     */
    public LYError confirmAcceptCanceledError(String message) {
        return createError("LY0510030002", message);
    }
}
