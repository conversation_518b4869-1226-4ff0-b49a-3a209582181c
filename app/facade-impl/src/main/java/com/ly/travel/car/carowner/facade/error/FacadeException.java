package com.ly.travel.car.carowner.facade.error;

import com.ly.sof.api.error.LYError;

public class FacadeException extends OrderBaseException{

    /**
     * 构造器
     *
     * @param error 同程异常
     */
    public FacadeException(LYError error) {
        super(error);
    }

    /**
     * 构造器
     *
     * @param traceId 全链路id
     * @param e       异常
     */
    public FacadeException(String traceId, Exception e) {
        super(traceId, e);
    }

}
