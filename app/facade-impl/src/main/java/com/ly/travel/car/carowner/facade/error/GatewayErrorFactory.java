package com.ly.travel.car.carowner.facade.error;

import com.ly.sof.api.error.AbstractErrorFactory;
import com.ly.sof.api.error.LYError;

/**
 * 网关异常工厂
 *
 * <AUTHOR>
 * @version Id: GatewayErrorFactory, v 0.1 2023/12/13 14:45 icanci Exp $
 */
public class GatewayErrorFactory extends AbstractErrorFactory {

    /**
     * 获取ErrorFactory单例
     *
     * @return instance
     */
    public static GatewayErrorFactory getInstance() {
        return GatewayErrorFactoryHolder.INSTANCE;
    }

    /**
     * Provide error bundle name string.
     *
     * @return the string
     */
    @Override
    protected String provideErrorBundleName() {
        return "trade-gateway";
    }

    /**
     * 系统异常
     *
     * @param error 错误消息
     * @return 异常
     */
    public LYError sysError(String error) {
        return createError("LY0510000001", error);
    }

    /**
     * 参数名:{0}, 内容:{1}不合法
     *
     * @param type    类型
     * @param message 错误消息
     * @param right   正确的格式
     * @return 异常
     */
    public LYError illegalParam(String type, String message, String right) {
        return createError("LY0510000002", type, message, right);
    }

    /**
     * 参数{0}不能为空
     *
     * @param param 参数
     * @return 异常
     */
    public LYError paramNull(String param) {
        return createError("LY0510000003", param);
    }

    /**
     * {0}格式错误，格式应该为{1}
     *
     * @return 异常
     */
    public LYError paramFormatError(Object obj, String format) {
        return createError("LY0510000004", obj, format);
    }

    /**
     * {0}参数，值:{1}不合法
     *
     * @return 异常
     */
    public LYError illegalParam(String param, Object value) {
        return createError("LY0510000005", param, value);
    }


    /**
     * 单例实现
     * <p>
     * GatewayErrorFactoryHolder instance keeper
     */
    private static final class GatewayErrorFactoryHolder {
        /**
         * 单例
         */
        private static final GatewayErrorFactory INSTANCE = new GatewayErrorFactory();
    }
}
