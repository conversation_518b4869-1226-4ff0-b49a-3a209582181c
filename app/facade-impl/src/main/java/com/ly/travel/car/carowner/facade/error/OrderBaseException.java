package com.ly.travel.car.carowner.facade.error;

import com.ly.sof.api.error.LYError;
import com.ly.sof.api.exception.LYException;

/**
 * 订单领域异常
 */
public class OrderBaseException extends LYException {

    /** 序列化版本 */
    private static final long serialVersionUID = -2199134784883522913L;

    /** 全链路id */
    private String            traceId;

    /**
     * 构造器
     *
     * @param cause
     */
    public OrderBaseException(Throwable cause) {
        super(cause);
    }

    /**
     * 构造器
     *
     * @param error 同程异常
     */
    public OrderBaseException(LYError error) {
        super(error);
    }

    /**
     * 构造器
     *
     * @param error 同程异常
     * @param cause 错误
     */
    public OrderBaseException(LYError error, Throwable cause) {
        super(error, cause);
    }

    /**
     * 构造器
     *
     * @param traceId 全链路id
     * @param cause   错误
     */
    public OrderBaseException(String traceId, Throwable cause) {
        super(cause);
        this.traceId = traceId;
    }

    /**
     * Gets the value of traceId.
     *
     * @return the value of traceId
     */
    public String getTraceId() {
        return traceId;
    }

    /**
     * Sets the traceId. *
     * <p>You can use getTraceId() to get the value of traceId</p >
     * @param traceId traceId
     */
    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }
}
