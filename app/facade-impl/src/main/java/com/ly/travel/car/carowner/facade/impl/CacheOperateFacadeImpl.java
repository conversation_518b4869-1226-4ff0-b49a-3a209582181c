package com.ly.travel.car.carowner.facade.impl;

import com.ly.travel.car.carowner.common.cache.CacheUtils;
import com.ly.travel.car.carowner.facade.CacheOperateFacade;
import com.ly.travel.car.carowner.facade.request.CacheOperateRequest;
import com.ly.travel.car.carowner.facade.response.CarOwnerResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service("cacheOperateFacade")
public class CacheOperateFacadeImpl implements CacheOperateFacade {

    @Override
    public CarOwnerResponseDTO delete(CacheOperateRequest request) {
        if (request == null || StringUtils.isBlank(request.getCacheKey())) {
            return CarOwnerResponseDTO.fail("入参不能为空");
        }

        CacheUtils.delKey(request.getCacheKey());
        log.info("缓存清理成功 >>> cacheKey={}", request.getCacheKey());

        return null;
    }

    @Override
    public CarOwnerResponseDTO get(CacheOperateRequest request) {
        if (request == null || StringUtils.isBlank(request.getCacheKey())) {
            return CarOwnerResponseDTO.fail("入参不能为空");
        }
        return CarOwnerResponseDTO.succeed(CacheUtils.getCacheValue(request.getCacheKey()));
    }
}