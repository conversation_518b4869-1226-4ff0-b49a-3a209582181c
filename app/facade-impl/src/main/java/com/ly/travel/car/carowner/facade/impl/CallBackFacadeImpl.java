package com.ly.travel.car.carowner.facade.impl;

import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.biz.driver.coupon.enums.CouponPayloadEventType;
import com.ly.travel.car.carowner.biz.driver.coupon.service.DriverCouponService;
import com.ly.travel.car.carowner.biz.order.mapper.SupplyCallbackMapping;
import com.ly.travel.car.carowner.biz.order.model.PassengerOrderInfoVO;
import com.ly.travel.car.carowner.biz.order.model.RideOrderInfoVO;
import com.ly.travel.car.carowner.biz.order.service.*;
import com.ly.travel.car.carowner.biz.retry.BusinessRetryMqHandle;
import com.ly.travel.car.carowner.biz.retry.OrderEventPayload;
import com.ly.travel.car.carowner.biz.sys.service.SysConfigService;
import com.ly.travel.car.carowner.biz.turbomq.producer.UpdateOrderStatusProducer;
import com.ly.travel.car.carowner.common.enums.*;
import com.ly.travel.car.carowner.common.utils.CarownerConfigCenterUtils;
import com.ly.travel.car.carowner.common.utils.DateUtil;
import com.ly.travel.car.carowner.dal.dataobject.RideOrderCancelDO;
import com.ly.travel.car.carowner.dal.dataobject.RideOrderTripInfoDO;
import com.ly.travel.car.carowner.facade.CallBackFacade;
import com.ly.travel.car.carowner.facade.request.callback.*;
import com.ly.travel.car.carowner.facade.response.CarOwnerResponseDTO;
import com.ly.travel.car.carowner.integration.client.api.LbsClient;
import com.ly.travel.car.carowner.integration.client.api.lbs.DistanceReq;
import com.ly.travel.car.carowner.integration.client.api.lbs.Path;
import com.ly.travel.car.carowner.integration.client.api.supplychain.SupplyChainApi;
import com.ly.travel.car.carowner.integration.client.api.supplychain.request.CallbackRequest;
import com.ly.travel.car.carowner.integration.client.api.supplychain.response.CallbackBaseResponse;
import com.ly.travel.car.carowner.integration.sms.SmsComponent;
import com.ly.travel.car.carowner.integration.sms.SmsTemplatesDefinedList;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @desc CallBackFacade
 */
@Slf4j
@Service("callBackFacade")
public class CallBackFacadeImpl implements CallBackFacade {

    private static final String ORDER_NOT_EXIST = "1000";
    private static final String ORDER_FINISHED = "1001";
    private static final String ORDER_CANCELED = "1002";
    private static final String NO_GET_ON_ERROR = "1003";
    private static final String NO_GET_ON_NOTIFY = "1004";
    private static final String END_PHONE_CHECK_FAIL = "1005";

    @Resource
    private SupplyChainApi supplyChainApi;
    @Resource
    private PassengerOrderInfoService passengerOrderInfoService;
    @Resource
    private RideOrderInfoService rideOrderInfoService;
    @Resource
    private SupplyCallbackMapping supplyCallbackMapping;
    @Resource
    private RideOrderTripInfoService rideOrderTripInfoService;
    @Resource
    private RideOrderCancelService rideOrderCancelService;
    @Resource
    private BusinessRetryMqHandle businessRetryMqHandle;
    @Resource
    private UpdateOrderStatusProducer updateOrderStatusProducer;
    @Resource
    private SysConfigService sysConfigService;
    @Autowired
    private RideOrderLogService rideOrderLogService;
    @Resource
    private LbsClient lbsClient;
    @Resource
    private SmsComponent smsComponent;
    @Resource
    private DriverCouponService driverCouponService;
    @Autowired
    private RideOrderTagsService rideOrderTagsService;

    @Override
    public CarOwnerResponseDTO acceptOrder(AcceptOrderRequest request) {
        //接单失败
        return success(request.getTraceId());
    }

    @Override
    public CarOwnerResponseDTO cancelOrder(CancelOrderRequest request) {
        if (Objects.isNull(request) || StringUtils.isEmpty(request.getOrderNo()) || StringUtils.isEmpty(request.getCancelReason()) || Objects.isNull(request.getCancelType())) {
            return fail("缺少必要参数");
        }
        String orderNo = request.getOrderNo();
        RideOrderInfoVO rideOrderInfoVO = rideOrderInfoService.queryOrderInfo(orderNo, null);
        PassengerOrderInfoVO passengerOrderInfoVO = passengerOrderInfoService.queryByOrderNo(orderNo);
        if (Objects.isNull(rideOrderInfoVO) && Objects.isNull(passengerOrderInfoVO)) {
            log.warn("乘客订单或主订单：{}不存在", orderNo);
            return fail("订单不存在");
        }
        Integer preState = Objects.nonNull(rideOrderInfoVO) ? rideOrderInfoVO.getStatus() : passengerOrderInfoVO.getStatus();
        if (Objects.nonNull(rideOrderInfoVO)) {
            if (Objects.nonNull(request.getDriverId()) && !request.getDriverId().equals(rideOrderInfoVO.getDriverId())) {
                log.warn("订单：{}司机信息不一致，driverId：{}", orderNo, request.getDriverId());
                return fail("司机身份认证失败");
            }
            if (rideOrderInfoVO.getStatus().equals(OrderStatusEnum.COMPLETED.getStatus())) {
                log.warn("订单：{}已完成", orderNo);
                return fail("订单已完成");
            }
            if (rideOrderInfoVO.getStatus().equals(OrderStatusEnum.CANCELLED.getStatus())) {
                log.info("订单：{}已取消", orderNo);
                return success(request.getTraceId());
            }
        }
        CallbackRequest callbackRequest = supplyCallbackMapping.convert(request);
        callbackRequest.setStatus(CallbackTypeEnum.CANCELLED.getStatus());
        callbackRequest.setTcSerialNo(Objects.nonNull(passengerOrderInfoVO) ? passengerOrderInfoVO.getDistributorOrderNo(): rideOrderInfoVO.getDistributorOrderNo());
        callbackRequest.setSupplierOrderNo(orderNo);
        CallbackBaseResponse callbackResponse = supplyChainApi.callback(callbackRequest);
        if (!callbackResponse.isSuccess()) {
            callbackResponse = businessRetryMqHandle.cancelOrderRetry(orderNo, BusinessBizEnum.CANCEL_ORDER_NOTIFY_C, FastJsonUtils.toJSONString(callbackRequest), 1);
        }

        if (!callbackResponse.isSuccess()) {
            log.info("订单[{}]取消订单通知供应链接入层失败", orderNo);
            return fail(callbackResponse.getMsg());
        }
        if (Objects.nonNull(passengerOrderInfoVO)) {
            passengerOrderInfoVO.setStatus(PassengerOrderStatusEnum.CANCELED.getCode());
            passengerOrderInfoService.updatePassengerOrderStatus(orderNo,PassengerOrderStatusEnum.CANCELED.getCode(),"cancelOrder");
        }
        if (Objects.nonNull(rideOrderInfoVO)) {
            rideOrderInfoVO.setStatus(OrderStatusEnum.CANCELLED.getStatus());

            //更新
            RideOrderInfoVO updateRideOrderVO = new RideOrderInfoVO();
            updateRideOrderVO.setOrderNo(rideOrderInfoVO.getOrderNo());
            updateRideOrderVO.setStatus(OrderStatusEnum.CANCELLED.getStatus());
            rideOrderInfoService.updateRideOrder(updateRideOrderVO);
        }
        //写日志
        List<RideOrderCancelDO> rideOrderCancelDOS = rideOrderCancelService.listByOrderNoAndCancelType(orderNo, request.getCancelType());
        if (CollectionUtils.isEmpty(rideOrderCancelDOS) && Objects.nonNull(rideOrderInfoVO)) {
            RideOrderCancelDO rideOrderCancelDO = new RideOrderCancelDO();
            rideOrderCancelDO.setOrderNo(orderNo);
            rideOrderCancelDO.setCancelType(request.getCancelType());
            rideOrderCancelDO.setCancelReason(request.getCancelReason());
            rideOrderCancelDO.setStatus(0);
            rideOrderCancelService.insert(rideOrderCancelDO);
        }

        //发送MQ
        updateOrderStatusProducer.send(new OrderEventPayload(orderNo, EventTypeEnum.UPDATE_ORDER_STATE.getCode(), OrderStatusEnum.CANCELLED.getStatus(), preState));
        //记录日志
        rideOrderLogService.addLog(request.getOrderNo(), OrderLogTypeEnum.CANCEL,
                OrderLogTypeEnum.CANCEL.formatRemark("司机", request.getCancelReason()),"系统");
        return success(request.getTraceId());
    }

    @Override
    public CarOwnerResponseDTO finishOrder(FinishOrderRequest request) {
        if (Objects.isNull(request) || StringUtils.isEmpty(request.getOrderNo())) {
            return fail("缺少必要参数");
        }
        String orderNo = request.getOrderNo();
        log.info("订单完单通知供应链，{}", orderNo);
        RideOrderInfoVO rideOrderInfoVO = rideOrderInfoService.queryOrderInfo(orderNo, null);
        if (Objects.isNull(rideOrderInfoVO)) {
            log.warn("订单：{}不存在", orderNo);
            return fail("订单不存在");
        }
        //回调供应链
        CallbackRequest callbackRequest = supplyCallbackMapping.convert(request);
        callbackRequest.setStatus(CallbackTypeEnum.FINISH.getStatus());
        callbackRequest.setTcSerialNo(rideOrderInfoVO.getDistributorOrderNo());
        callbackRequest.setSupplierOrderNo(orderNo);
        CallbackBaseResponse callbackResponse = supplyChainApi.callback(callbackRequest);
        if (!callbackResponse.isSuccess()) {
            callbackResponse = businessRetryMqHandle.driverTripRetry(orderNo, BusinessBizEnum.DRIVER_TRIP_NOTIFY_C, FastJsonUtils.toJSONString(callbackRequest), 1);
        }
        if (!callbackResponse.isSuccess()) {
            log.info("订单[{}]完单通知供应链接入层失败", orderNo);
        }
        return success(request.getTraceId());
    }

    @Override
    public CarOwnerResponseDTO driverTrip(DriverTripRequest request) {
        if (Objects.isNull(request) || StringUtils.isEmpty(request.getOrderNo()) || Objects.isNull(request.getTripType())) {
            return fail("缺少必要参数");
        }
        String orderNo = request.getOrderNo();
        RideOrderInfoVO rideOrderInfoVO = rideOrderInfoService.queryOrderInfo(orderNo, null);
        if (Objects.isNull(rideOrderInfoVO)) {
            log.warn("订单：{}不存在", orderNo);
            return fail("主订单不存在", ORDER_NOT_EXIST);
        }
        if (Objects.nonNull(request.getDriverId()) && !request.getDriverId().equals(rideOrderInfoVO.getDriverId())) {
            log.warn("订单：{}司机信息不一致，driverId：{}", orderNo, request.getDriverId());
            return fail("司机身份认证失败");
        }
        Integer preState = rideOrderInfoVO.getStatus();
        if (rideOrderInfoVO.getStatus().equals(OrderStatusEnum.COMPLETED.getStatus())) {
            log.warn("订单：{}已完结", orderNo);
            return fail("订单已完结", ORDER_FINISHED);
        }
        if (rideOrderInfoVO.getStatus().equals(OrderStatusEnum.CANCELLED.getStatus())) {
            log.warn("订单：{}已取消", orderNo);
            return fail("订单已取消", ORDER_CANCELED);
        }
        PassengerOrderInfoVO passengerOrderInfoVO = passengerOrderInfoService.queryByOrderNo(orderNo);
        if (Objects.isNull(passengerOrderInfoVO)) {
            log.warn("订单：{}不存在", orderNo);
        }
        RideOrderTripInfoDO rideOrderTripInfoDO = rideOrderTripInfoService.queryByOrderNo(orderNo);
        boolean isNew = Objects.isNull(rideOrderTripInfoDO);
        if (isNew) {
            rideOrderTripInfoDO = new RideOrderTripInfoDO();
            rideOrderTripInfoDO.setOrderNo(orderNo);
            rideOrderTripInfoDO.setStatus(-1);
        } else {
            Long id = rideOrderTripInfoDO.getId();
            Integer status = rideOrderTripInfoDO.getStatus();
            rideOrderTripInfoDO = new RideOrderTripInfoDO();
            rideOrderTripInfoDO.setId(id);
            rideOrderTripInfoDO.setStatus(status);
        }

        CallbackTypeEnum callbackTypeEnum = CallbackTypeEnum.getByStatus(request.getTripType());
        // 司机点击乘客已上车，需校验乘客是否点击已上车
        // boolean forceVerify = false;
        OrderTripStateEnum orderTripStateEnum = OrderTripStateEnum.of(callbackTypeEnum);
        if (orderTripStateEnum != null && Objects.equals(rideOrderTripInfoDO.getStatus(), orderTripStateEnum.getCode())) {
            log.info("db订单状态: dbStatus={},orderTripState={}", rideOrderTripInfoDO.getStatus(), orderTripStateEnum.getCode());
            return success(request.getTraceId());
        }

        if (Objects.equals(rideOrderTripInfoDO.getStatus(), OrderTripStateEnum.SERVICE.getCode()) && (orderTripStateEnum == OrderTripStateEnum.SET_OUT || orderTripStateEnum == OrderTripStateEnum.ARRIVED)) {
            log.info("乘客滑动上车对司机滑动校验拦截 >>> db订单状态: dbStatus={},orderTripState={}", rideOrderTripInfoDO.getStatus(), orderTripStateEnum.getCode());
            return success(request.getTraceId());
        }

        if (callbackTypeEnum.equals(CallbackTypeEnum.SERVICE_ON)) {
            /*passengerGetOn = Objects.nonNull(rideOrderTripInfoDO.getStatus()) && rideOrderTripInfoDO.getStatus().equals(OrderTripStateEnum.GET_ON.getCode());
            String stringCachedCfgValue = sysConfigService.getStringCachedCfgValue(SysConfigEnum.PASSENGER_GET_ON_FORCE_VERIFY_KEY);
            forceVerify = stringCachedCfgValue.equals("1");
            if (!passengerGetOn && forceVerify) {
                return fail("乘客未点击上车", NO_GET_ON_ERROR);
            }*/

            if (StringUtils.equals(CarownerConfigCenterUtils.checkRealTelephone, "0")) {
                String realTelephone = rideOrderInfoVO.getRealTelephone();
                if (StringUtils.isNotBlank(realTelephone) && realTelephone.length() > 4 && !StringUtils.equals(realTelephone.substring(realTelephone.length() - 4), request.getEndPhoneNo())) {
                    log.warn("乘客手机尾号验证不通过: dbRealTelephone={}，endPhoneNo={}");
                    return fail("乘客手机尾号验证不通过", END_PHONE_CHECK_FAIL);
                }
            }
        }

        //通知供应链
        CallbackRequest callbackRequest = supplyCallbackMapping.convert(request);
        callbackRequest.setTcSerialNo(rideOrderInfoVO.getDistributorOrderNo());
        callbackRequest.setSupplierOrderNo(orderNo);
        CallbackBaseResponse callbackResponse = supplyChainApi.callback(callbackRequest);
        //失败走重试
        if (!callbackResponse.isSuccess()) {
            businessRetryMqHandle.driverTripRetry(orderNo, BusinessBizEnum.DRIVER_TRIP_NOTIFY_C, FastJsonUtils.toJSONString(callbackRequest), 1);
        }
        //修改订单行程状态
        log.info("订单：{}司机行程通知，写入行程通知数据，request：{}", orderNo, FastJsonUtils.toJSONString(callbackRequest));
        rideOrderTripInfoDO.setUpdateTime(new Date());
        OrderLogTypeEnum logTypeEnum = null;
        if (callbackTypeEnum.equals(CallbackTypeEnum.DRIVER_DEPARTURE)) {
            logTypeEnum = OrderLogTypeEnum.GO_PICK_UP_THE_PASSENGER_CAR;
            rideOrderTripInfoDO.setStartEmptyDrivingLon(request.getDrivingLon());
            rideOrderTripInfoDO.setStartEmptyDrivingLat(request.getDrivingLat());
            rideOrderTripInfoDO.setDriverStartTime(new Date());
            rideOrderTripInfoDO.setStatus(OrderTripStateEnum.SET_OUT.getCode());
        }
        if (callbackTypeEnum.equals(CallbackTypeEnum.ARRIVE_DEPARTURE)) {
            logTypeEnum = OrderLogTypeEnum.ARRIVE_AT_THE_PASSENGER_S_PLACE;
            rideOrderTripInfoDO.setGetStartingLon(request.getDrivingLon());
            rideOrderTripInfoDO.setGetStartingLat(request.getDrivingLat());
            rideOrderTripInfoDO.setDriverStartAddressTime(new Date());
            rideOrderTripInfoDO.setStatus(OrderTripStateEnum.ARRIVED.getCode());
        }
        if (callbackTypeEnum.equals(CallbackTypeEnum.SERVICE_ON)) {
            logTypeEnum = OrderLogTypeEnum.THE_PASSENGER_HAS_BOARDED_THE_BUS;
            rideOrderTripInfoDO.setGetOnLon(request.getDrivingLon());
            rideOrderTripInfoDO.setGetOnLat(request.getDrivingLat());
            rideOrderTripInfoDO.setDriverReceivePassengerTime(new Date());
            rideOrderTripInfoDO.setStatus(OrderTripStateEnum.SERVICE.getCode());
        }
        if (callbackTypeEnum.equals(CallbackTypeEnum.ARRIVE_DESTINATION)) {
            logTypeEnum = OrderLogTypeEnum.PASSENGERS_GET_OFF_THE_BUS;
            rideOrderTripInfoDO.setGetOffLon(request.getDrivingLon());
            rideOrderTripInfoDO.setGetOffLat(request.getDrivingLat());
            rideOrderTripInfoDO.setDriverSentPassengerTime(new Date());
            rideOrderTripInfoDO.setStatus(OrderTripStateEnum.DEST.getCode());
            if (Objects.nonNull(request.getAutoCompletionFlag()) && request.getAutoCompletionFlag() == AutoCompleteFlagEnum.TRUE.getCode()){
                logTypeEnum = OrderLogTypeEnum.AUTO_COMPLETE;
                rideOrderTripInfoDO.setAutoCompletionFlag(request.getAutoCompletionFlag());

            }
            //实际行驶距离，单位：km
            Path path = this.getPath(request, rideOrderInfoVO);
            if (Objects.nonNull(path) && Objects.nonNull(path.getDistance())) {
                rideOrderTripInfoDO.setRealMileage(new BigDecimal(path.getDistance()).divide(new BigDecimal(1000)));
            }
        }
        if (isNew) {
            rideOrderTripInfoService.saveRideOrderTripInfo(rideOrderTripInfoDO);
        } else {
            rideOrderTripInfoService.updateRideOrderTripInfo(rideOrderTripInfoDO);
        }
        //回调完单
        if (callbackTypeEnum.equals(CallbackTypeEnum.ARRIVE_DESTINATION)) {
            log.info("订单：{}司机已到达目的地，订单已完结，修改订单状态，request：{}", orderNo, FastJsonUtils.toJSONString(callbackRequest));
            if (Objects.nonNull(passengerOrderInfoVO)) {
                passengerOrderInfoVO.setStatus(PassengerOrderStatusEnum.FINISH.getCode());
                passengerOrderInfoService.updatePassengerOrderStatus(orderNo,PassengerOrderStatusEnum.FINISH.getCode(),"driverTrip");
            }
            rideOrderInfoVO.setStatus(OrderStatusEnum.COMPLETED.getStatus());

            //更新
            RideOrderInfoVO updateRideOrderVO = new RideOrderInfoVO();
            updateRideOrderVO.setOrderNo(rideOrderInfoVO.getOrderNo());
            updateRideOrderVO.setStatus(OrderStatusEnum.COMPLETED.getStatus());
            updateRideOrderVO.setFinishTime(new Date());
            rideOrderInfoService.updateRideOrder(updateRideOrderVO);

            if (Objects.nonNull(request.getAutoCompletionFlag()) && request.getAutoCompletionFlag() == AutoCompleteFlagEnum.TRUE.getCode()){
                rideOrderTagsService.addTag(request.getOrderNo(),OrderTagEnum.AUTO_COMPLETE.getCode(),rideOrderInfoVO.getId());
            }

            //回调完单
            callbackRequest.setStatus(CallbackTypeEnum.FINISH.getStatus());
            callbackResponse = supplyChainApi.callback(callbackRequest);
            if (!callbackResponse.isSuccess()) {
                callbackResponse = businessRetryMqHandle.driverTripRetry(orderNo, BusinessBizEnum.DRIVER_TRIP_NOTIFY_C, FastJsonUtils.toJSONString(callbackRequest), 1);
            }
            if (callbackResponse.isSuccess()) {
                //发送MQ
                updateOrderStatusProducer.send(new OrderEventPayload(orderNo, EventTypeEnum.UPDATE_ORDER_STATE.getCode(), OrderStatusEnum.COMPLETED.getStatus(), preState));
            }

            // 移到监听订单状态mq处理
            //driverCouponService.sendRecharge(orderNo, rideOrderInfoVO.getDriverId());
        }
        //乘客未点击已上车，没有强制校验，给个错误码提示
        /*if (callbackTypeEnum.equals(CallbackTypeEnum.SERVICE_ON) && !passengerGetOn && !forceVerify) {
            return success(request.getTraceId(), NO_GET_ON_NOTIFY);
        }*/
        //记录日志
        rideOrderLogService.addLog(request.getOrderNo(), logTypeEnum, logTypeEnum.getRemark(),"系统");
        return success(request.getTraceId());
    }

    //废弃掉
    @Override
    public CarOwnerResponseDTO sendSms(SendSmsRequest request) {
        String orderNo = request.getOrderNo();
        RideOrderInfoVO rideOrderInfoVO = rideOrderInfoService.queryOrderInfo(orderNo, null);
        if (Objects.isNull(rideOrderInfoVO)) {
            return success(request.getTraceId());
        }
        smsComponent.sendTemplateMsg(SmsTemplatesDefinedList.CANCEL_TEMPLATE
                .setTemplateParams(rideOrderInfoVO.getStartingTime(), rideOrderInfoVO.getOrderNo(), rideOrderInfoVO.getDriverTripNo())
                .setMobile(rideOrderInfoVO.getDriverMobile()));
        return success(request.getTraceId());
    }

    public CarOwnerResponseDTO fail(String errorMsg) {
        CarOwnerResponseDTO response = new CarOwnerResponseDTO<>();
        response.setSuccess(false);
        response.setErrorMessage(errorMsg);
        return response;
    }

    public CarOwnerResponseDTO fail(String errorMsg, String errorCode) {
        CarOwnerResponseDTO response = new CarOwnerResponseDTO<>();
        response.setSuccess(false);
        response.setErrorMessage(errorMsg);
        response.setErrorCode(errorCode);
        return response;
    }

    public CarOwnerResponseDTO success(String traceId) {
        CarOwnerResponseDTO response = new CarOwnerResponseDTO<>();
        response.setSuccess(true);
        response.setTraceId(traceId);
        return response;
    }

    public CarOwnerResponseDTO success(String traceId, String errorCode) {
        CarOwnerResponseDTO response = new CarOwnerResponseDTO<>();
        response.setSuccess(true);
        response.setTraceId(traceId);
        response.setErrorCode(errorCode);
        return response;
    }

    private Path getPath(DriverTripRequest request, RideOrderInfoVO rideOrderInfoVO) {
        try {
            if (Objects.isNull(request.getDrivingLon()) || Objects.isNull(request.getDrivingLat())) {
                log.warn("司机已到达时未传位置");
                return null;
            }
            DistanceReq distanceReq = new DistanceReq();
            distanceReq.setOrigin_latitude(rideOrderInfoVO.getStartingLat().setScale(6, BigDecimal.ROUND_HALF_UP));
            distanceReq.setOrigin_longitude(rideOrderInfoVO.getStartingLon().setScale(6, BigDecimal.ROUND_HALF_UP));
            distanceReq.setDestination_latitude(request.getDrivingLat().setScale(6, BigDecimal.ROUND_HALF_UP));
            distanceReq.setDestination_longitude(request.getDrivingLon().setScale(6, BigDecimal.ROUND_HALF_UP));
            distanceReq.setTrace_id(StringUtils.isNotEmpty(request.getTraceId()) ? request.getTraceId() : request.getOrderNo());
            distanceReq.setGeo_type("GCJ02");
            distanceReq.setPlatform(9999);
            distanceReq.setUser_identity("0");
            return lbsClient.getPath(distanceReq);
        } catch (Exception e) {
            log.error("LBS getPath error:{}", e);
        }
        return null;
    }
}
