package com.ly.travel.car.carowner.facade.impl;

import com.alibaba.fastjson.JSON;
import com.ly.travel.car.carowner.biz.driver.account.service.DriverAccountService;
import com.ly.travel.car.carowner.common.utils.CarownerConfigCenterUtils;
import com.ly.travel.car.carowner.dal.dataobject.DriverAccountDO;
import com.ly.travel.car.carowner.facade.DriverAccountFacade;
import com.ly.travel.car.carowner.facade.request.FreezeDriverAccountRequest;
import com.ly.travel.car.carowner.facade.request.UnfreezeDriverAccountRequest;
import com.ly.travel.car.carowner.facade.request.UpdateDriverAccountAmountRequest;
import com.ly.travel.car.carowner.facade.response.CarOwnerResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
@Slf4j
@Service("driverAccountFacade")
public class DriverAccountFacadeImpl implements DriverAccountFacade {
    @Resource
    private DriverAccountService driverAccountService;

    @Override
    public CarOwnerResponseDTO freezeDriverAccount(FreezeDriverAccountRequest request) {
        return driverAccountService.freezeDriverAccount(request);
    }

    @Override
    public CarOwnerResponseDTO unfreezeDriverAccount(UnfreezeDriverAccountRequest request) {
        return driverAccountService.unfreezeDriverAccount(request);
    }

    @Override
    public CarOwnerResponseDTO updateDriverAmount(UpdateDriverAccountAmountRequest request) {
        CarOwnerResponseDTO carOwnerResponseDTO=new CarOwnerResponseDTO();

        if(!"1".equals(CarownerConfigCenterUtils.UPDATE_DRIVER_ACCOUNT_AMOUNT_SWITCH)){
            carOwnerResponseDTO.setSuccess(false);
            carOwnerResponseDTO.setErrorMessage("找管理员开权限");
            return  carOwnerResponseDTO;
        }
        DriverAccountDO driverAccountDO=new DriverAccountDO();
        driverAccountDO.setDriverId(request.getDriverId());
        driverAccountDO.setTotalAmount(request.getTotalAmount());
        driverAccountDO.setFreezeAmount(request.getFreezeAmount());
        driverAccountDO.setWithdrawingAmount(request.getWithdrawingAmount());
        driverAccountDO.setWithdrawnAmount(request.getWithdrawnAmount());
        driverAccountDO.setAvailableAmount(request.getAvailableAmount());
        driverAccountDO.setUpdateUser("updateDriverAmount");
        driverAccountDO.setUpdateTime(new Date());
        log.warn("手动调用修改司机账户余额,{}", JSON.toJSONString(request));

        carOwnerResponseDTO.setSuccess(driverAccountService.updateDriverAccount(driverAccountDO)>0);
        return carOwnerResponseDTO;

    }
}
