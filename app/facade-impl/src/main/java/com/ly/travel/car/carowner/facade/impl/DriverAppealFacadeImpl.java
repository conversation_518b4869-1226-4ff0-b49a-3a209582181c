package com.ly.travel.car.carowner.facade.impl;

import com.ly.travel.car.carowner.biz.driver.appeal.service.DriverAppealService;
import com.ly.travel.car.carowner.facade.DriverAppealFacade;
import com.ly.travel.car.carowner.facade.request.DriverAdjustsBillRequest;
import com.ly.travel.car.carowner.facade.response.CarOwnerResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service("driverAppealFacade")
public class DriverAppealFacadeImpl implements DriverAppealFacade {

    @Resource
    private DriverAppealService driverAppealService;

    @Override
    public CarOwnerResponseDTO accountAdjustment(DriverAdjustsBillRequest request) {
        return driverAppealService.accountAdjustment(request);
    }
}