package com.ly.travel.car.carowner.facade.impl;

import com.ly.travel.car.carowner.biz.driver.bill.service.DriverBillService;
import com.ly.travel.car.carowner.facade.DriverBillFacade;
import com.ly.travel.car.carowner.facade.request.FreezeDriverBillRequest;
import com.ly.travel.car.carowner.facade.request.UnfreezeDriverBillRequest;
import com.ly.travel.car.carowner.facade.response.CarOwnerResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service("driverBillFacade")
public class DriverBiliFacadeImpl implements DriverBillFacade {
    @Resource
    private DriverBillService driverBillService;

    @Override
    public CarOwnerResponseDTO freezeDriverBill(FreezeDriverBillRequest request) {
        return driverBillService.freezeDriverBill(request);
    }

    @Override
    public CarOwnerResponseDTO unfreezeDriverBill(UnfreezeDriverBillRequest request) {
        return driverBillService.unfreezeDriverBill(request);
    }
}