package com.ly.travel.car.carowner.facade.impl;

import com.ly.travel.car.carowner.biz.driver.bankcard.service.DriverBankCardService;
import com.ly.travel.car.carowner.biz.driver.withdrawal.service.DriverWithdrawalService;
import com.ly.travel.car.carowner.biz.driver.withdrawal.service.WithdrawalApplyAuditService;
import com.ly.travel.car.carowner.biz.driver.withdrawal.service.YeePayCallbackService;
import com.ly.travel.car.carowner.facade.DriverWithdrawalFacade;
import com.ly.travel.car.carowner.facade.request.*;
import com.ly.travel.car.carowner.facade.response.CarOwnerResponseDTO;
import com.ly.travel.car.carowner.facade.response.RecognizeBankCardResponseDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service("withdrawalFacade")
public class DriverWithdrawalFacadeImpl implements DriverWithdrawalFacade {
    @Resource
    private DriverWithdrawalService driverWithdrawalService;
    @Resource
    private WithdrawalApplyAuditService withdrawalApplyAuditService;
    @Resource
    private YeePayCallbackService yeePayCallbackService;

    @Resource
    private DriverBankCardService driverBankCardService;


    @Override
    public CarOwnerResponseDTO driverWithdrawal(DriverWithdrawalRequest request) {
        return driverWithdrawalService.driverWithdrawal(request);
    }

    @Override
    public CarOwnerResponseDTO sendSmsVerify(SmsVerifyRequest request) {
        return driverWithdrawalService.sendSmsVerify(request);
    }

    @Override
    public CarOwnerResponseDTO withdrawalApplyAudit(WithdrawalApplyAuditRequest request) {
        return withdrawalApplyAuditService.withdrawalApplyAudit(request);
    }

    @Override
    public CarOwnerResponseDTO yeePayCallback(YeePayCallbackRequest request) {
        return yeePayCallbackService.yeePayCallback(request);
    }

    @Override
    public CarOwnerResponseDTO<RecognizeBankCardResponseDTO> recognizeBankCard(RecognizeBankCardRequest request) {
        return driverWithdrawalService.recognizeBankCard( request);
    }

    @Override
    public CarOwnerResponseDTO batchDeleteBankCard(DeleteBankCardRequest request) {
        driverBankCardService.batchDeleteBankCard( request);
        return CarOwnerResponseDTO.succeed( null);
    }

    @Override
    public CarOwnerResponseDTO queryOrderIsWithdrawal(OrderIsWithdrawalRequest request) {
        return driverWithdrawalService.queryOrderIsWithdrawal(request);
    }
}