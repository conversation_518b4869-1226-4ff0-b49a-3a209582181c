package com.ly.travel.car.carowner.facade.impl;

import com.ly.travel.car.carowner.biz.order.model.PassengerOrderInfoVO;
import com.ly.travel.car.carowner.biz.order.model.RideOrderRefundVO;
import com.ly.travel.car.carowner.biz.order.model.RideOrderSettlementVO;
import com.ly.travel.car.carowner.biz.order.service.PassengerOrderInfoService;
import com.ly.travel.car.carowner.biz.order.service.RideOrderRefundService;
import com.ly.travel.car.carowner.biz.settlement.enums.SettlementTypeEnum;
import com.ly.travel.car.carowner.biz.settlement.service.RideOrderSettlementService;
import com.ly.travel.car.carowner.biz.settlement.service.SettlementPolicyProcess;
import com.ly.travel.car.carowner.common.exception.OrderSettlementException;
import com.ly.travel.car.carowner.common.exception.TakeOrderException;
import com.ly.travel.car.carowner.common.utils.DateUtil;
import com.ly.travel.car.carowner.facade.OrderSettlementFacade;
import com.ly.travel.car.carowner.facade.request.QuerySettlementDetailRequest;
import com.ly.travel.car.carowner.facade.request.QuerySettlementRequest;
import com.ly.travel.car.carowner.facade.request.SaveOrderSettlementRequest;
import com.ly.travel.car.carowner.facade.response.CarOwnerListResponseDTO;
import com.ly.travel.car.carowner.facade.response.CarOwnerResponseDTO;
import com.ly.travel.car.carowner.facade.response.QuerySettlementDetailRspDTO;
import com.ly.travel.car.carowner.facade.response.QuerySettlementRspDTO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service("orderSettlementFacade")
public class OrderSettlementFacadeImpl implements OrderSettlementFacade {
    @Autowired
    private RideOrderSettlementService rideOrderSettlementService;

    @Autowired
    private PassengerOrderInfoService passengerOrderInfoService;

    @Autowired
    private RideOrderRefundService rideOrderRefundService;


    @Override
    public CarOwnerListResponseDTO<QuerySettlementRspDTO> querySettlement(QuerySettlementRequest req) {
        CarOwnerListResponseDTO<QuerySettlementRspDTO> listResult = new CarOwnerListResponseDTO<>();
        List<QuerySettlementRspDTO> listDto = new ArrayList<>();
        if (req.getQueryDBflag() == 1) {
            List<RideOrderSettlementVO> rideOrderSettlementVOList = rideOrderSettlementService.queryByOrderNos(req.getOrderNos());
            if (!CollectionUtils.isEmpty(rideOrderSettlementVOList)) {
                listDto = rideOrderSettlementVOList.stream().map(this::convertQuerySettlementRspDTO).collect(Collectors.toList());
                listResult.setData(listDto);
            }
        }
        if (CollectionUtils.isEmpty(listDto)) {
            List<PassengerOrderInfoVO> passengerOrderInfoVOList = passengerOrderInfoService.queryByOrderNoList(req.getOrderNos());
            listDto = passengerOrderInfoVOList.stream().map(passengerOrderInfoVO -> {
                RideOrderSettlementVO rideOrderSettlementVO = rideOrderSettlementService.calculationSettlement(passengerOrderInfoVO);
                return convertQuerySettlementRspDTO(rideOrderSettlementVO);
            }).collect(Collectors.toList());
        }

        listResult.setData(listDto);
        return listResult;
    }

    @Override
    public CarOwnerListResponseDTO saveSettlement(SaveOrderSettlementRequest req) throws Exception {
        SettlementPolicyProcess.getInstance(SettlementTypeEnum.getByCode(req.getType())).processing(req.getOrderNo());
        return new CarOwnerListResponseDTO();
    }

    private QuerySettlementRspDTO convertQuerySettlementRspDTO(RideOrderSettlementVO item) {
        QuerySettlementRspDTO rspDTO = new QuerySettlementRspDTO();
        BeanUtils.copyProperties(item, rspDTO);
        rspDTO.setDriverSettlementPrice(item.getOldDriverSettlementPrice());  //接单时的金额
        rspDTO.setDriverSettlementPrice_now(item.getDriverSettlementPrice());  //最后实际金额

        if(item.getOldDriverSettlementPrice().compareTo(BigDecimal.ZERO)<=0){
            //如果为0，则为历史数据， 给成最新值
            rspDTO.setDriverSettlementPrice(item.getDriverSettlementPrice());
        }
        return rspDTO;
    }

    public CarOwnerResponseDTO<QuerySettlementDetailRspDTO> querySettlementDetail(QuerySettlementDetailRequest req) throws Exception{
        CarOwnerResponseDTO<QuerySettlementDetailRspDTO> result = new CarOwnerResponseDTO<>();
        QuerySettlementDetailRspDTO settlementDetailRspDTO=null;
        RideOrderSettlementVO rideOrderSettlementVO = rideOrderSettlementService.queryByOrderNo(req.getOrderNo());
        if (rideOrderSettlementVO==null) {
            PassengerOrderInfoVO passengerOrderInfoVO = passengerOrderInfoService.queryByOrderNo(req.getOrderNo());
            if(passengerOrderInfoVO==null) {
                throw new OrderSettlementException("原始订单信息不存在，或可能已失效.");
            }
            rideOrderSettlementVO = rideOrderSettlementService.calculationSettlement(passengerOrderInfoVO);

        }
        settlementDetailRspDTO=convertQuerySettlementDetailRspDTO(rideOrderSettlementVO);
        BigDecimal realSumRefund = settlementDetailRspDTO.getDriverSettlementPrice().subtract(settlementDetailRspDTO.getDriverSettlementPrice_now());
        if(realSumRefund.compareTo(BigDecimal.ZERO)>0) {

            List<RideOrderRefundVO> refundVOList = rideOrderRefundService.queryRefundByOrderNo(req.getOrderNo());
            if (!CollectionUtils.isEmpty(refundVOList)) {
                RideOrderSettlementVO finalRideOrderSettlementVO = rideOrderSettlementVO;
                List<QuerySettlementDetailRspDTO.SettlementRefundInfo> settlementRefundInfoList = refundVOList.stream().map(rideOrderRefundVO -> {
                    if (rideOrderRefundVO.getRefundAmount() != null && rideOrderRefundVO.getRefundAmount().compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal refund_driver = rideOrderSettlementService.calculationDriverRefundSettlement(rideOrderRefundVO.getRefundAmount(), finalRideOrderSettlementVO);
                        if (refund_driver.compareTo(BigDecimal.ZERO) <= 0) {
                            return null;
                        }
                        return QuerySettlementDetailRspDTO.SettlementRefundInfo.builder().createTime(DateUtil.date2String(rideOrderRefundVO.getCreateTime()))
                                .refundAmount(refund_driver)
                                .build();
                    }
                    return null;
                }).filter(Objects::nonNull).collect(Collectors.toList());
                BigDecimal sumRefund = BigDecimal.ZERO;


                for (int i = 0; i < settlementRefundInfoList.size(); i++) {
                    QuerySettlementDetailRspDTO.SettlementRefundInfo settlementRefundInfo = settlementRefundInfoList.get(i);
                    if (i == settlementRefundInfoList.size() - 1) {
                        settlementRefundInfo.setRefundAmount(realSumRefund.subtract(sumRefund));
                        break;
                    }
                    sumRefund = sumRefund.add(settlementRefundInfo.getRefundAmount());
                }
                settlementDetailRspDTO.setRefundList(settlementRefundInfoList);
            }
        }
        result.setData(settlementDetailRspDTO);
        return result;

    }
    private QuerySettlementDetailRspDTO convertQuerySettlementDetailRspDTO(RideOrderSettlementVO item) {
        QuerySettlementDetailRspDTO rspDTO = new QuerySettlementDetailRspDTO();
        BeanUtils.copyProperties(item, rspDTO);
        rspDTO.setDriverSettlementPrice(item.getOldDriverSettlementPrice());  //接单时的金额
        rspDTO.setDriverSettlementPrice_now(item.getDriverSettlementPrice());  //最后实际金额

        if(item.getOldDriverSettlementPrice().compareTo(BigDecimal.ZERO)<=0){
            //如果为0，则为历史数据， 给成最新值
            rspDTO.setDriverSettlementPrice(item.getDriverSettlementPrice());
        }

        return rspDTO;
    }

}
