package com.ly.travel.car.carowner.facade.impl;

import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.biz.order.mapper.SupplyCallbackMapping;
import com.ly.travel.car.carowner.biz.order.model.PassengerOrderInfoVO;
import com.ly.travel.car.carowner.biz.order.model.RideOrderInfoVO;
import com.ly.travel.car.carowner.biz.order.service.*;
import com.ly.travel.car.carowner.biz.push.service.PushMsgService;
import com.ly.travel.car.carowner.biz.retry.BusinessRetryMqHandle;
import com.ly.travel.car.carowner.biz.retry.OrderEventPayload;
import com.ly.travel.car.carowner.biz.sys.service.SysConfigService;
import com.ly.travel.car.carowner.biz.turbomq.producer.UpdateOrderStatusProducer;
import com.ly.travel.car.carowner.common.enums.*;
import com.ly.travel.car.carowner.common.utils.CarownerConfigCenterUtils;
import com.ly.travel.car.carowner.common.utils.DateUtil;
import com.ly.travel.car.carowner.dal.dataobject.RideOrderCancelDO;
import com.ly.travel.car.carowner.dal.dataobject.RideOrderTripInfoDO;
import com.ly.travel.car.carowner.facade.CallBackFacade;
import com.ly.travel.car.carowner.facade.PushFacade;
import com.ly.travel.car.carowner.facade.request.PushMsgRequest;
import com.ly.travel.car.carowner.facade.request.callback.*;
import com.ly.travel.car.carowner.facade.response.CarOwnerListResponseDTO;
import com.ly.travel.car.carowner.facade.response.CarOwnerPushMsgResponseDTO;
import com.ly.travel.car.carowner.facade.response.CarOwnerResponseDTO;
import com.ly.travel.car.carowner.integration.client.api.LbsClient;
import com.ly.travel.car.carowner.integration.client.api.lbs.DistanceReq;
import com.ly.travel.car.carowner.integration.client.api.lbs.Path;
import com.ly.travel.car.carowner.integration.client.api.supplychain.SupplyChainApi;
import com.ly.travel.car.carowner.integration.client.api.supplychain.request.CallbackRequest;
import com.ly.travel.car.carowner.integration.client.api.supplychain.response.CallbackBaseResponse;
import com.ly.travel.car.carowner.integration.sms.SmsComponent;
import com.ly.travel.car.carowner.integration.sms.SmsTemplatesDefinedList;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;


@Slf4j
@Service("pushFacade")
public class PushFacadeImpl implements PushFacade {

    @Autowired
    private PushMsgService pushMsgService;

    @Override
    public CarOwnerPushMsgResponseDTO pushMsg(PushMsgRequest request) throws Exception {

        boolean result = pushMsgService.cxPush(request);
        CarOwnerPushMsgResponseDTO carOwnerPushMsgResponseDTO =  new CarOwnerPushMsgResponseDTO();
        carOwnerPushMsgResponseDTO.setSuccess(result);
        return carOwnerPushMsgResponseDTO;

    }
}
