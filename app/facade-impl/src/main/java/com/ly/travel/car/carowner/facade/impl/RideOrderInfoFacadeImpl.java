package com.ly.travel.car.carowner.facade.impl;

import com.alibaba.fastjson.JSON;
import com.ly.travel.car.carowner.biz.order.model.RideOrderInfoVO;
import com.ly.travel.car.carowner.biz.order.service.RideOrderInfoService;
import com.ly.travel.car.carowner.biz.order.service.RideOrderLogService;
import com.ly.travel.car.carowner.biz.order.service.RideOrderTagsService;
import com.ly.travel.car.carowner.biz.settlement.enums.SettlementTypeEnum;
import com.ly.travel.car.carowner.biz.settlement.service.SettlementPolicyProcess;
import com.ly.travel.car.carowner.common.enums.OrderLogTypeEnum;
import com.ly.travel.car.carowner.common.enums.OrderStatusEnum;
import com.ly.travel.car.carowner.facade.RideOrderInfoFacade;
import com.ly.travel.car.carowner.facade.request.*;
import com.ly.travel.car.carowner.facade.response.CarOwnerListResponseDTO;
import com.ly.travel.car.carowner.facade.response.CarOwnerResponseDTO;
import com.ly.travel.car.carowner.facade.response.QueryOrderInfoRspDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
@Slf4j
@Service("rideOrderInfoFacade")
public class RideOrderInfoFacadeImpl implements RideOrderInfoFacade {

    @Autowired
    private RideOrderInfoService rideOrderInfoService;
    @Autowired
    private RideOrderLogService rideOrderLogService;
    @Autowired
    private RideOrderTagsService rideOrderTagsService;

    //查询单个订单信息
    @Override
    public CarOwnerResponseDTO<QueryOrderInfoRspDTO> queryOrderInfo(QueryOrderInfoRequest req) throws Exception {
        CarOwnerResponseDTO<QueryOrderInfoRspDTO> responseDTO=new CarOwnerResponseDTO<>();
        RideOrderInfoVO rideOrderInfoVO = rideOrderInfoService.queryOrderInfo(req.getOrderNo(),req.getDistributorOrderNo());
        if(rideOrderInfoVO==null){
            responseDTO.setSuccess(false);
            responseDTO.setErrorMessage("订单不存在");
            return responseDTO;
        }
        responseDTO.setData(convertDTO(rideOrderInfoVO));
        return responseDTO;
    }
    private QueryOrderInfoRspDTO convertDTO(RideOrderInfoVO rideOrderInfoVO){
        QueryOrderInfoRspDTO rspDTO=new QueryOrderInfoRspDTO();
        BeanUtils.copyProperties(rideOrderInfoVO,rspDTO);
        return rspDTO;
    }

    //查询一批订单信息
    @Override
    public CarOwnerListResponseDTO<QueryOrderInfoRspDTO> queryOrderList(QueryOrderListRequest req) throws Exception {
        CarOwnerListResponseDTO<QueryOrderInfoRspDTO> listResponseDTO=new CarOwnerListResponseDTO<>();

        if(req.getOrderNos().size()>500){
            listResponseDTO.setSuccess(false);
            listResponseDTO.setErrorMessage("orderNo数据超过500不能查");
            return listResponseDTO;
        }
        List<RideOrderInfoVO> rideOrderInfoVOList = rideOrderInfoService.queryOrderInfoByOrderNos(req.getOrderNos());
        listResponseDTO.setData(rideOrderInfoVOList.stream().map(this::convertDTO).collect(Collectors.toList()));
        return listResponseDTO;
    }

    @Override
    public CarOwnerResponseDTO updateOrderStatus(UpdateOrderStatusRequest request)throws Exception {

        CarOwnerResponseDTO responseDTO=new CarOwnerResponseDTO();

        RideOrderInfoVO sourceOrder=rideOrderInfoService.queryOrderInfo(request.getOrderNo(), null);
        log.warn("修改前后状态不能相同,{}", JSON.toJSONString(sourceOrder));

        if(sourceOrder==null){
            responseDTO.setSuccess(false);
            responseDTO.setErrorMessage("查无此单");
            return responseDTO;
        }
        if(sourceOrder.getStatus()==request.getOrderStatus()){
            log.warn("修改前后状态不能相同,{}",request.getOrderNo());
            responseDTO.setSuccess(false);
            responseDTO.setErrorMessage("修改前后状态不能相同");
            return responseDTO;
        }
        if(sourceOrder.getStatus()== OrderStatusEnum.COMPLETED.getStatus()||sourceOrder.getStatus()==OrderStatusEnum.CANCELLED.getStatus()){
            log.warn("已完成或取消订单不能修改,{}",request.getOrderNo());
            responseDTO.setSuccess(false);
            responseDTO.setErrorMessage("已完成或取消订单不能修改");
            return responseDTO;
        }
        OrderStatusEnum orderStatusEnum=OrderStatusEnum.getByValue(request.getOrderStatus());
        if(orderStatusEnum==null){
            log.warn("传入订单状态不正确,{}",request.getOrderNo());
            responseDTO.setSuccess(false);
            responseDTO.setErrorMessage("传入订单状态不正确");
            return responseDTO;
        }

        RideOrderInfoVO rideOrderInfoVO=new RideOrderInfoVO();
        rideOrderInfoVO.setOrderNo(request.getOrderNo());
        rideOrderInfoVO.setStatus(request.getOrderStatus());
        rideOrderInfoVO.setUpdateTime(new Date());
        rideOrderInfoVO.setCreateUser("updateOrderStatus");
        if(request.getOrderStatus()==OrderStatusEnum.COMPLETED.getStatus()){
            rideOrderInfoVO.setFinishTime(new Date());
        }

        if(rideOrderInfoService.updateRideOrder(rideOrderInfoVO)){
            responseDTO.setSuccess(true);
            return responseDTO;
        }
        responseDTO.setSuccess(false);
        responseDTO.setErrorMessage("订单没有修改成功");
        return responseDTO;
    }

    @Override
    public CarOwnerResponseDTO addLog(AddOrderLogRequest request)  {
        rideOrderLogService.addLog(request.getOrderNo(), request.getLogType(),request.getLogTypeName(),request.getRemark(),request.getCreateUser());
        return new CarOwnerResponseDTO();
    }

    @Override
    public CarOwnerResponseDTO addTag(AddOrderTagRequest request)  {
        rideOrderTagsService.addTag(request.getOrderNo(),request.getTags(),request.getOrderId());
        return new CarOwnerResponseDTO();
    }
    @Override
    public CarOwnerResponseDTO removeOrderTag(AddOrderTagRequest request)  {
        for(String tag : request.getTags()){
            rideOrderTagsService.removeTag(request.getOrderNo(),tag,request.getOrderId());
        }
        return new CarOwnerResponseDTO();
    }
    @Override
    public CarOwnerListResponseDTO<String> queryOrderTag(QueryOrderTagRequest request)  {
        CarOwnerListResponseDTO<String> listResponseDTO=new CarOwnerListResponseDTO<>();
        listResponseDTO.setData(rideOrderTagsService.queryOrderTag(request.getOrderNo()));
        return listResponseDTO;
    }
    @Override
    public CarOwnerResponseDTO<Boolean> existsOrderTag(AddOrderTagRequest request)  {
        CarOwnerResponseDTO<Boolean> carOwnerResponseDTO=new CarOwnerResponseDTO<>();
        boolean r=true;
        for(String tag : request.getTags()){
            r&=rideOrderTagsService.existsOrderTag(request.getOrderNo(),tag);
        }
        carOwnerResponseDTO.setSuccess(r);
        return carOwnerResponseDTO;
    }
}
