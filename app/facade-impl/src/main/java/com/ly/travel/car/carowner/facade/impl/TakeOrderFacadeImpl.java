package com.ly.travel.car.carowner.facade.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.biz.driver.coupon.service.DriverCouponService;
import com.ly.travel.car.carowner.biz.order.mapper.SupplyCallbackMapping;
import com.ly.travel.car.carowner.biz.order.model.PassengerOrderInfoVO;
import com.ly.travel.car.carowner.biz.order.model.RideOrderInfoVO;
import com.ly.travel.car.carowner.biz.order.service.PassengerOrderInfoService;
import com.ly.travel.car.carowner.biz.order.service.RideOrderLogService;
import com.ly.travel.car.carowner.biz.order.service.RideOrderInfoService;
import com.ly.travel.car.carowner.biz.retry.BusinessRetryMqHandle;
import com.ly.travel.car.carowner.biz.retry.OrderEventPayload;
import com.ly.travel.car.carowner.biz.settlement.enums.SettlementTypeEnum;
import com.ly.travel.car.carowner.biz.settlement.service.SettlementPolicyProcess;
import com.ly.travel.car.carowner.biz.takeorder.model.GrabOrderStoreModel;
import com.ly.travel.car.carowner.biz.takeorder.service.AcceptOrderConfigService;
import com.ly.travel.car.carowner.biz.takeorder.service.RideDispatchInfoService;
import com.ly.travel.car.carowner.biz.takeorder.service.TakeOrderService;
import com.ly.travel.car.carowner.biz.turbomq.producer.UpdateOrderStatusProducer;
import com.ly.travel.car.carowner.common.cache.CacheUtils;
import com.ly.travel.car.carowner.common.enums.*;
import com.ly.travel.car.carowner.common.utils.CarownerConfigCenterUtils;
import com.ly.travel.car.carowner.dal.dataobject.DriverInfoDO;
import com.ly.travel.car.carowner.dal.mapper.DriverInfoMapper;
import com.ly.travel.car.carowner.facade.TakeOrderFacade;
import com.ly.travel.car.carowner.facade.error.FacadeErrorFactory;
import com.ly.travel.car.carowner.facade.error.FacadeException;
import com.ly.travel.car.carowner.facade.request.ConfirmTakeOrderRequest;
import com.ly.travel.car.carowner.facade.request.ValidateAcceptOrderRequest;
import com.ly.travel.car.carowner.facade.request.callback.AcceptOrderRequest;
import com.ly.travel.car.carowner.facade.response.CarOwnerResponseDTO;
import com.ly.travel.car.carowner.integration.client.api.supplychain.SupplyChainApi;
import com.ly.travel.car.carowner.integration.client.api.supplychain.request.CallbackRequest;
import com.ly.travel.car.carowner.integration.client.api.supplychain.response.CallbackBaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

@Slf4j
@Service("takeOrderFacade")
public class TakeOrderFacadeImpl implements TakeOrderFacade {

    private static final FacadeErrorFactory FACADE_ERROR_FACTORY = FacadeErrorFactory.getInstance();

    @Autowired
    private TakeOrderService takeOrderService;
    @Resource
    private SupplyChainApi supplyChainApi;
    @Resource
    private PassengerOrderInfoService passengerOrderInfoService;
    @Resource
    private RideOrderInfoService rideOrderInfoService;
    @Resource
    private SupplyCallbackMapping supplyCallbackMapping;
    @Autowired
    private RideOrderLogService rideOrderLogService;
    @Autowired
    private RideDispatchInfoService rideDispatchInfoService;
    @Resource
    private BusinessRetryMqHandle businessRetryMqHandle;
    @Resource
    private UpdateOrderStatusProducer updateOrderStatusProducer;
    @Resource
    private DriverInfoMapper driverInfoMapper;
    @Resource
    private AcceptOrderConfigService acceptOrderConfigService;
    @Resource
    private DriverCouponService driverCouponService;

    @Override
    public CarOwnerResponseDTO updateOrderstatus(ConfirmTakeOrderRequest request) throws Exception {
        log.info("[REQUEST] >>> url= req={}", FastJsonUtils.toJSONString(request));
        return confirmTakeOrder(request);
    }

    @Override
    public CarOwnerResponseDTO confirmTakeOrder(ConfirmTakeOrderRequest request) throws Exception {
        CarOwnerResponseDTO responseDTO = new CarOwnerResponseDTO();
        GrabOrderStoreModel grabOrderStoreModel=takeOrderService.grabOrderSuccess(request.getOrderNo());
        if (grabOrderStoreModel!=null) {
            SettlementPolicyProcess.getInstance(SettlementTypeEnum.RECEIVED_ORDER).processing(request.getOrderNo());

            updateOrderStatusProducer.send(new OrderEventPayload(request.getOrderNo(), EventTypeEnum.UPDATE_ORDER_STATE.getCode(), OrderStatusEnum.DISPATCHED.getStatus(),OrderStatusEnum.DISPATCHED.getStatus()));

            rideOrderLogService.addLog(request.getOrderNo(), OrderLogTypeEnum.ORDER_SUCCESSFULLY_RECEIVED,
                    OrderLogTypeEnum.ORDER_SUCCESSFULLY_RECEIVED.formatRemark(
                            grabOrderStoreModel.getDriverName(),
                            grabOrderStoreModel.getDriverMobile(),
                            grabOrderStoreModel.getVehicleNo()
                            ),"系统");
            return responseDTO;
        }
        // todo 接单冻结卡券
        responseDTO.setErrorMessage("失败");
        responseDTO.setSuccess(false);
        responseDTO.setErrorCode("500");
        return responseDTO;
    }

    @Override
    public CarOwnerResponseDTO grabOrder(AcceptOrderRequest request) {
        try {
            if (Objects.isNull(request) || StringUtils.isEmpty(request.getOrderNo()) || Objects.isNull(request.getDriverId()) || StringUtils.isEmpty(request.getDriverName())
                    || StringUtils.isEmpty(request.getDriverPhone()) || StringUtils.isEmpty(request.getVehiclePlateNum()) || StringUtils.isEmpty(request.getDriverTripNo())
            ) {
                throw new FacadeException(FACADE_ERROR_FACTORY.confirmAcceptError("缺少必要参数"));
            }
            String orderNo = request.getOrderNo();
            String cacheKey = CacheKeyEnum.GRAB_ORDER_STORE.format(orderNo);
            String cacheValue = CacheUtils.getCacheValue(cacheKey);
            log.info("订单[{}]司机抢单缓存：{}", orderNo, cacheValue);
      /*  if (StringUtils.isNotEmpty(cacheValue)) {
            return fail("重复抢单");
        }*/
            RideOrderInfoVO rideOrderInfoVO = rideOrderInfoService.queryOrderInfo(orderNo, null);
            if (Objects.nonNull(rideOrderInfoVO)) {
                log.warn("订单：{}已接单", orderNo);
                throw new FacadeException(FACADE_ERROR_FACTORY.confirmAcceptFailError("订单被已接单"));
            }
            PassengerOrderInfoVO passengerOrderInfoVO = passengerOrderInfoService.queryByOrderNo(orderNo);
            if (Objects.isNull(passengerOrderInfoVO)) {
                log.warn("乘客订单：{}不存在", orderNo);
                throw new FacadeException(FACADE_ERROR_FACTORY.confirmAcceptCanceledError("乘客订单已经被取消"));
            }

            // 接单配置校验
            ValidateAcceptOrderRequest acceptOrderRequest = buildValidateAcceptOrderRequest(request.getDriverId(), passengerOrderInfoVO);
            CarOwnerResponseDTO carOwnerResponseDTO = validateAcceptOrder(acceptOrderRequest);
            if (!carOwnerResponseDTO.isSuccess()) {
                log.warn("接单配置校验不通过: request={}", FastJsonUtils.toJSONString(request));
                return carOwnerResponseDTO;
            }

            //设置抢单缓存
            GrabOrderStoreModel grabOrderStoreModel = supplyCallbackMapping.convertGrabOrder(request);
            grabOrderStoreModel.setDriverMobile(request.getDriverPhone());
            grabOrderStoreModel.setVehicleNo(request.getVehiclePlateNum());
            Long dispatchId=rideDispatchInfoService.addDispatchInfo(grabOrderStoreModel);

            CallbackRequest callbackRequest = supplyCallbackMapping.convert(request);
            callbackRequest.setDriverName(this.getPrivateName(request.getDriverId(), request.getDriverName()));
            callbackRequest.setTcSerialNo(passengerOrderInfoVO.getDistributorOrderNo());
            callbackRequest.setSupplierOrderNo(orderNo);
            callbackRequest.setStatus(CallbackTypeEnum.ACCEPT_ORDER.getStatus());
            CallbackBaseResponse callbackResponse = supplyChainApi.callback(callbackRequest);
            if("1".equals(CarownerConfigCenterUtils.TestGrabOrder_AutoSuccess)){
                log.error("用于测试的调用供应链成功,{}",request.getOrderNo());
                callbackResponse.setCode("200");
            }
            if (!callbackResponse.isSuccess()) {
                callbackResponse = businessRetryMqHandle.grabOrderRetry(orderNo, BusinessBizEnum.ACCEPT_ORDER_NOTIFY_C, FastJsonUtils.toJSONString(callbackRequest), 0);
            }
            if (callbackResponse.isSuccess()) {
                rideDispatchInfoService.updateDispatch(dispatchId,RideDispatchStatusTypeEnum.SUCCESSFUL.getType());
                //修改乘客订单状态

                passengerOrderInfoService.updatePassengerOrderStatus(passengerOrderInfoVO.getOrderNo(),PassengerOrderStatusEnum.ACCEPT_ORDER.getCode(),"grabOrder");
                //记录日志
                rideOrderLogService.addLog(request.getOrderNo(), OrderLogTypeEnum.CONFIRM_YOUR_PEERS,
                        OrderLogTypeEnum.CONFIRM_YOUR_PEERS.formatRemark(
                                grabOrderStoreModel.getDriverName(),
                                grabOrderStoreModel.getDriverMobile(),
                                grabOrderStoreModel.getVehicleNo()
                        ),"系统");
                return success(request.getTraceId());
            }
            rideDispatchInfoService.updateDispatch(dispatchId, RideDispatchStatusTypeEnum.FAILED.getType());
            // 兼容乘客取消和司机确认同行在用一时间
            if (StringUtils.isNotBlank(callbackResponse.getMsg()) && callbackResponse.getMsg().contains("LY000000006")) {
                throw new FacadeException(FACADE_ERROR_FACTORY.confirmAcceptCanceledError("乘客订单已经被取消"));
            }
            return CarOwnerResponseDTO.fail("LY0510030000", callbackResponse.getMsg());
        } catch (FacadeException e) {
            String message = e.getError().getMessage();
            if (StringUtils.isNotBlank(message) && message.contains(":")) {
                message = message.split(":")[1];
            }
            return CarOwnerResponseDTO.fail(e.getError().getCode(), message);
        } catch (Exception e) {
            log.error("确认同行派单失败: request={}", FastJsonUtils.toJSONString(request), e);
            return CarOwnerResponseDTO.fail("LY0510030000", "派单失败");
        }
    }

    private ValidateAcceptOrderRequest buildValidateAcceptOrderRequest(Long driverId, PassengerOrderInfoVO passengerOrderInfoVO) {
        ValidateAcceptOrderRequest request = new ValidateAcceptOrderRequest();
        request.setDriverId(driverId);
        request.setStartingTime(passengerOrderInfoVO.getStartingTime());
        request.setStartCityId(passengerOrderInfoVO.getStartCityId());
        request.setEndCityId(passengerOrderInfoVO.getEndCityId());
        return request;
    }

    private String getPrivateName(Long driverId, String driverName) {
        DriverInfoDO driverInfoDO = driverInfoMapper.selectOne(new LambdaQueryWrapper<DriverInfoDO>()
                .eq(DriverInfoDO::getId, driverId));
        if (Objects.isNull(driverInfoDO)) {
            log.error("未找到司机信息, driverId：{}", driverId);
            return driverName.substring(0, 1) + "师傅";
        }
        if (StringUtils.isEmpty(driverInfoDO.getSex())) {
            log.warn("司机未录入性别, driverId：{}", driverId);
            return driverName.substring(0, 1) + "师傅";
        }
        return driverName.substring(0, 1) + (driverInfoDO.getSex().equals("男") ? "先生" : "女士");
    }

    public CarOwnerResponseDTO fail(String errorMsg) {
        CarOwnerResponseDTO response = new CarOwnerResponseDTO<>();
        response.setSuccess(false);
        response.setErrorMessage(errorMsg);
        return response;
    }

    public CarOwnerResponseDTO success(String traceId) {
        CarOwnerResponseDTO response = new CarOwnerResponseDTO<>();
        response.setSuccess(true);
        response.setTraceId(traceId);
        return response;
    }

    @Override
    public CarOwnerResponseDTO validateAcceptOrder(ValidateAcceptOrderRequest request) {
        String validate = request.validate();
        if (StringUtils.isNotBlank(validate)) {
            log.warn(validate);
            return CarOwnerResponseDTO.fail(validate);
        }

        CarOwnerResponseDTO carOwnerResponseDTO = acceptOrderConfigService.validateAcceptOrder(request);
        if (!carOwnerResponseDTO.isSuccess()) {
            log.warn("接单配置校验不通过: request={}", FastJsonUtils.toJSONString(request));
            return carOwnerResponseDTO;
        }

        return CarOwnerResponseDTO.succeed(null);
    }
}
