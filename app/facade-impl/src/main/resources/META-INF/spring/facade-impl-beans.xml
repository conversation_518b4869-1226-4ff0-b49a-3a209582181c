<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/context
	    https://www.springframework.org/schema/context/spring-context.xsd
         http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd"
       default-autowire="byName">


    <dubbo:registry id="tcdsfRegistry" address="${dubbo.service.registry.address}" protocol="tcdsf"/>
    <dubbo:protocol name="tcdsfrest" port="${dubbo.service.port}" server="${dubbo.service.deploy.container}"
                    extension="com.ly.sof.core.support.DsfJacksonConfig"/>

    <bean id="tcdsfGroup" class="com.alibaba.dubbo.registry.tcdsf.DsfServiceGroup">
        <constructor-arg name="registry" ref="tcdsfRegistry"/>
        <constructor-arg name="gsName" value="${dubbo.service.gsname}"/>
        <constructor-arg name="version" value="${dubbo.service.version}"/>
        <constructor-arg name="cLevel" value="2"/>
        <constructor-arg name="pLevel" value="3"/>
        <constructor-arg name="services">
            <set>
                <ref bean="dsfOrderSettlementService"/>
                <ref bean="dsfOrderInfoService"/>
                <ref bean="dsfWithdrawalService"/>
                <ref bean="dsfTakeOrderService"/>
                <ref bean="dsfDriverAccountService"/>
                <ref bean="dsfCallBackService"/>
                <ref bean="dsfDriverBillService"/>
                <ref bean="dsfDriverAppealService"/>
                <ref bean="dsfCacheOperateService"/>
                <ref bean="dsfPushService"/>
            </set>
        </constructor-arg>
    </bean>

    <dubbo:service id="dsfOrderSettlementService"
                   ref="orderSettlementFacade"
                   interface="com.ly.travel.car.carowner.facade.OrderSettlementFacade"
                   protocol="tcdsfrest"/>

    <dubbo:service id="dsfOrderInfoService"
                   ref="rideOrderInfoFacade"
                   interface="com.ly.travel.car.carowner.facade.RideOrderInfoFacade"
                   protocol="tcdsfrest"/>

    <dubbo:service id="dsfWithdrawalService"
                   ref="withdrawalFacade"
                   interface="com.ly.travel.car.carowner.facade.DriverWithdrawalFacade"
                   protocol="tcdsfrest"/>

    <dubbo:service id="dsfTakeOrderService"
                   ref="takeOrderFacade"
                   interface="com.ly.travel.car.carowner.facade.TakeOrderFacade"
                   protocol="tcdsfrest"/>

    <dubbo:service id="dsfCallBackService"
                   ref="callBackFacade"
                   interface="com.ly.travel.car.carowner.facade.CallBackFacade"
                   protocol="tcdsfrest"/>

    <dubbo:service id="dsfDriverAccountService"
                   ref="driverAccountFacade"
                   interface="com.ly.travel.car.carowner.facade.DriverAccountFacade"
                   protocol="tcdsfrest"/>

    <dubbo:service id="dsfDriverBillService"
                   ref="driverBillFacade"
                   interface="com.ly.travel.car.carowner.facade.DriverBillFacade"
                   protocol="tcdsfrest"/>

    <dubbo:service id="dsfDriverAppealService"
                   ref="driverAppealFacade"
                   interface="com.ly.travel.car.carowner.facade.DriverAppealFacade"
                   protocol="tcdsfrest"/>

    <dubbo:service id="dsfCacheOperateService"
                   ref="cacheOperateFacade"
                   interface="com.ly.travel.car.carowner.facade.CacheOperateFacade"
                   protocol="tcdsfrest"/>

    <dubbo:service id="dsfPushService"
                   ref="pushFacade"
                   interface="com.ly.travel.car.carowner.facade.PushFacade"
                   protocol="tcdsfrest"/>
</beans>
