<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
        <groupId>com.ly.travel.car.carowner</groupId>
        <artifactId>shared-mobility-carowner-core</artifactId>
        <version>1.0.1.RELEASE</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <version>1.0.8-${versionSuffix}</version>

    <artifactId>shared-mobility-carowner-core-facade</artifactId>
    <packaging>jar</packaging>

    <name>LY shared-mobility-carowner-core-facade</name>
    <description>LY shared-mobility-carowner-core-facade</description>

    <dependencies>
        <dependency>
            <groupId>com.ly.sof</groupId>
            <artifactId>sof-facade-base</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.ws.rs</groupId>
            <artifactId>javax.ws.rs-api</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.ly.dsf</groupId>
            <artifactId>dsf-tccommon</artifactId>
            <version>2.5.14</version>
            <scope>compile</scope>
        </dependency>

    </dependencies>


</project>
