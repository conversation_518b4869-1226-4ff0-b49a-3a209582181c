package com.ly.travel.car.carowner.facade;

import com.ly.travel.car.carowner.facade.request.CacheOperateRequest;
import com.ly.travel.car.carowner.facade.response.CarOwnerResponseDTO;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;

@Path("cacheoperate")
public interface CacheOperateFacade {

    /**
     * 缓存清理
     *
     * @param request
     * @return
     * @throws Exception
     */
    @Deprecated
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("delete")
    CarOwnerResponseDTO delete(CacheOperateRequest request);

    /**
     * 缓存获取
     *
     * @param request
     * @return
     * @throws Exception
     */
    @Deprecated
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("get")
    CarOwnerResponseDTO get(CacheOperateRequest request);
}