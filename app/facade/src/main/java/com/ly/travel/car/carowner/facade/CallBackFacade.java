package com.ly.travel.car.carowner.facade;

import com.ly.travel.car.carowner.facade.request.callback.*;
import com.ly.travel.car.carowner.facade.response.CarOwnerResponseDTO;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;

/**
 * <AUTHOR>
 * @desc CallBackFacade
 */
@Path("callback")
public interface CallBackFacade {


    /**
     * 司机接单
     *
     * @param request
     * @return
     * @throws Exception
     */
    @Deprecated
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("acceptOrder")
    CarOwnerResponseDTO acceptOrder(AcceptOrderRequest request);

    /**
     * 取消订单
     *
     * @param request
     * @return
     * @throws Exception
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("cancelOrder")
    CarOwnerResponseDTO cancelOrder(CancelOrderRequest request);

    /**
     * 完单
     *
     * @param request
     * @return
     * @throws Exception
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("finishOrder")
    CarOwnerResponseDTO finishOrder(FinishOrderRequest request);

    /**
     * 司机行程回调
     *
     * @param request
     * @return
     * @throws Exception
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("driverTrip")
    CarOwnerResponseDTO driverTrip(DriverTripRequest request);

    /**
     * 发送短信
     *
     * @param request
     * @return
     * @throws Exception
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("sendSms")
    CarOwnerResponseDTO sendSms(SendSmsRequest request);
}
