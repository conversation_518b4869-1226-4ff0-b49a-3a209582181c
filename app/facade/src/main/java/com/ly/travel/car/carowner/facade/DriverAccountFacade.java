package com.ly.travel.car.carowner.facade;

import com.ly.travel.car.carowner.facade.request.FreezeDriverAccountRequest;
import com.ly.travel.car.carowner.facade.request.UnfreezeDriverAccountRequest;
import com.ly.travel.car.carowner.facade.request.UpdateDriverAccountAmountRequest;
import com.ly.travel.car.carowner.facade.response.CarOwnerResponseDTO;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;

@Path("driveraccount")
public interface DriverAccountFacade {

    /**
     * <AUTHOR>
     * @Description 冻结司机账户
     * @Date 2025-06-15 22:39:37
     * @Param request
     * @Return CarOwnerResponseDTO
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("freezeDriverAccount")
    CarOwnerResponseDTO freezeDriverAccount(FreezeDriverAccountRequest request);

    /**
     * <AUTHOR>
     * @Description 解冻司机账户
     * @Date 2025-06-15 22:39:54
     * @Param request
     * @Return CarOwnerResponseDTO
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("unfreezeDriverAccount")
    CarOwnerResponseDTO unfreezeDriverAccount(UnfreezeDriverAccountRequest request);

    /**
     * <AUTHOR> @Description 修改账户余额
     * @Param request
     * @Return CarOwnerResponseDTO
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("updateDriverAmount")
    CarOwnerResponseDTO updateDriverAmount(UpdateDriverAccountAmountRequest request);
}
