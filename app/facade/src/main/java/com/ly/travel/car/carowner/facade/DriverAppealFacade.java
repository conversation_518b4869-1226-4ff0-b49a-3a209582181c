package com.ly.travel.car.carowner.facade;

import com.ly.travel.car.carowner.facade.request.DriverAdjustsBillRequest;
import com.ly.travel.car.carowner.facade.response.CarOwnerResponseDTO;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;

@Path("driverappeal")
public interface DriverAppealFacade {

    /**
     * <AUTHOR>
     * @Description 调账
     * @Date 2025-06-15 22:39:54
     * @Param request
     * @Return CarOwnerResponseDTO
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("accountAdjustment")
    CarOwnerResponseDTO accountAdjustment(DriverAdjustsBillRequest request);
}