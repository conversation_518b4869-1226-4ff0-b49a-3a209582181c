package com.ly.travel.car.carowner.facade;

import com.ly.travel.car.carowner.facade.request.FreezeDriverBillRequest;
import com.ly.travel.car.carowner.facade.request.UnfreezeDriverBillRequest;
import com.ly.travel.car.carowner.facade.response.CarOwnerResponseDTO;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;

@Path("driverbill")
public interface DriverBillFacade {

    /**
     * <AUTHOR>
     * @Description 冻结司机账单
     * @Date 2025-06-15 22:39:37
     * @Param request
     * @Return CarOwnerResponseDTO
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("freezeDriverBill")
    CarOwnerResponseDTO freezeDriverBill(FreezeDriverBillRequest request);

    /**
     * <AUTHOR>
     * @Description 解冻司机账单
     * @Date 2025-06-15 22:39:54
     * @Param request
     * @Return CarOwnerResponseDTO
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("unfreezeDriverBill")
    CarOwnerResponseDTO unfreezeDriverBill(UnfreezeDriverBillRequest request);
}
