package com.ly.travel.car.carowner.facade;

import com.ly.travel.car.carowner.facade.request.*;
import com.ly.travel.car.carowner.facade.response.CarOwnerResponseDTO;
import com.ly.travel.car.carowner.facade.response.RecognizeBankCardResponseDTO;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;

@Path("withdrawal")
public interface DriverWithdrawalFacade {

    /**
     * <AUTHOR>
     * @Description 司机提现
     * @Date 2025-06-15 22:38:14
     * @Param request
     * @Return CarOwnerResponseDTO
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("driverWithdrawal")
    CarOwnerResponseDTO driverWithdrawal(DriverWithdrawalRequest request);

    /**
     * <AUTHOR>
     * @Description 提现申请审核
     * @Date 2025-06-15 22:38:33
     * @Param request
     * @Return CarOwnerResponseDTO
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("withdrawalApplyAudit")
    CarOwnerResponseDTO withdrawalApplyAudit(WithdrawalApplyAuditRequest request);

    /**
     * <AUTHOR>
     * @Description 短信验证
     * @Date 2025-06-15 22:38:44
     * @Param request
     * @Return CarOwnerResponseDTO
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("sendSmsVerify")
    CarOwnerResponseDTO sendSmsVerify(SmsVerifyRequest request);

    /**
     * <AUTHOR>
     * @Description 易宝回调
     * @Date 2025-06-15 22:38:44
     * @Param request
     * @Return CarOwnerResponseDTO
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("yeePayCallback")
    CarOwnerResponseDTO yeePayCallback(YeePayCallbackRequest request);

    /**
     * <AUTHOR>
     * @Description 识别银行卡
     * @Date 2025-06-15 22:38:44
     * @Param request
     * @Return CarOwnerResponseDTO
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("recognizeBankCard")
    CarOwnerResponseDTO<RecognizeBankCardResponseDTO> recognizeBankCard(RecognizeBankCardRequest request);


    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("batchDeleteBankCard")
    CarOwnerResponseDTO batchDeleteBankCard(DeleteBankCardRequest request);

    /**
     * 查询订单是否提现
     *
     * @param request
     * @return
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("queryOrderIsWithdrawal")
    CarOwnerResponseDTO queryOrderIsWithdrawal(OrderIsWithdrawalRequest request);
}
