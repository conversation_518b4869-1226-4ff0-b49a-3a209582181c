package com.ly.travel.car.carowner.facade;

import com.ly.travel.car.carowner.facade.request.QuerySettlementDetailRequest;
import com.ly.travel.car.carowner.facade.request.QuerySettlementRequest;
import com.ly.travel.car.carowner.facade.request.SaveOrderSettlementRequest;
import com.ly.travel.car.carowner.facade.response.CarOwnerListResponseDTO;
import com.ly.travel.car.carowner.facade.response.CarOwnerResponseDTO;
import com.ly.travel.car.carowner.facade.response.QuerySettlementDetailRspDTO;
import com.ly.travel.car.carowner.facade.response.QuerySettlementRspDTO;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;

@Path("ordersettlement")
public interface OrderSettlementFacade {
    /**
     * 查询订单结算数据
     * @param req
     * @return
     * @throws Exception
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("querySettlement")
    CarOwnerListResponseDTO<QuerySettlementRspDTO> querySettlement(QuerySettlementRequest req) throws Exception;

    /**
     * 订单结算
     * @param req
     * @return
     * @throws Exception
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("saveSettlement")
    CarOwnerListResponseDTO saveSettlement(SaveOrderSettlementRequest req) throws Exception;


    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("querySettlementDetail")
    CarOwnerResponseDTO<QuerySettlementDetailRspDTO> querySettlementDetail(QuerySettlementDetailRequest req) throws Exception;



}
