package com.ly.travel.car.carowner.facade;

import com.ly.travel.car.carowner.facade.request.PushMsgRequest;
import com.ly.travel.car.carowner.facade.response.CarOwnerPushMsgResponseDTO;
import com.ly.travel.car.carowner.facade.response.CarOwnerResponseDTO;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;

@Path("push")
public interface PushFacade {

    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("pushMsg")
    CarOwnerPushMsgResponseDTO pushMsg(PushMsgRequest request) throws Exception;
}
