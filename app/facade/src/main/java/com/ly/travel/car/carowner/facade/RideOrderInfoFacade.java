package com.ly.travel.car.carowner.facade;

import com.ly.travel.car.carowner.facade.request.*;
import com.ly.travel.car.carowner.facade.response.CarOwnerListResponseDTO;
import com.ly.travel.car.carowner.facade.response.QueryOrderInfoRspDTO;
import com.ly.travel.car.carowner.facade.response.CarOwnerResponseDTO;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;

@Path("rideorderinfo")
public interface RideOrderInfoFacade {

    /**
     * 查询单个订单信息，根据订单号或分销单号若其它
     * @param req
     * @return
     * @throws Exception
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("queryOrderInfo")
    CarOwnerResponseDTO<QueryOrderInfoRspDTO> queryOrderInfo(QueryOrderInfoRequest req) throws Exception;


    /**
     * 根据订单号查询一批订单信息
     * @param req
     * @return
     * @throws Exception
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("queryOrderList")
    CarOwnerListResponseDTO<QueryOrderInfoRspDTO> queryOrderList(QueryOrderListRequest req) throws Exception;

    /**
     * 修改订单状态
     * @param request
     * @return
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("updateOrderStatus")
    CarOwnerResponseDTO updateOrderStatus(UpdateOrderStatusRequest request) throws Exception ;

    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("addLog")
    CarOwnerResponseDTO addLog(AddOrderLogRequest request) throws Exception ;

    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("addTag")
    CarOwnerResponseDTO addTag(AddOrderTagRequest request) throws Exception ;

    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("queryOrderTag")
    CarOwnerListResponseDTO<String> queryOrderTag(QueryOrderTagRequest request) throws Exception ;

    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("removeTag")
    public CarOwnerResponseDTO removeOrderTag(AddOrderTagRequest request) throws Exception ;

    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("removeTag")
    public CarOwnerResponseDTO existsOrderTag(AddOrderTagRequest request) throws Exception ;
    }
