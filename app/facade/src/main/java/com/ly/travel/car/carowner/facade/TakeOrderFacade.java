package com.ly.travel.car.carowner.facade;

import com.ly.travel.car.carowner.facade.request.ConfirmTakeOrderRequest;
import com.ly.travel.car.carowner.facade.request.ValidateAcceptOrderRequest;
import com.ly.travel.car.carowner.facade.request.callback.AcceptOrderRequest;
import com.ly.travel.car.carowner.facade.response.CarOwnerResponseDTO;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;

@Path("takeorder")
public interface TakeOrderFacade {



    /**
     * 废弃，写错了
     *
     * @param request
     * @return
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("updateOrderstatus")
    CarOwnerResponseDTO updateOrderstatus(ConfirmTakeOrderRequest request) throws Exception;

    /**
     * 接单成功
     *
     * @param request
     * @return
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("confirmTakeOrder")
    CarOwnerResponseDTO confirmTakeOrder(ConfirmTakeOrderRequest request) throws Exception;

    /**
     * 验证接单配置
     *
     * @param request
     * @return
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("validateAcceptOrder")
    CarOwnerResponseDTO validateAcceptOrder(ValidateAcceptOrderRequest request) throws Exception;

    /**
     * 接单
     *
     * @param request
     * @return
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("grabOrder")
    CarOwnerResponseDTO grabOrder(AcceptOrderRequest request);
}
