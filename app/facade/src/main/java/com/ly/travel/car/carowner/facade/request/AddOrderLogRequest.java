package com.ly.travel.car.carowner.facade.request;

import com.ly.sof.facade.base.BaseRequestDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class AddOrderLogRequest extends BaseRequestDTO {

    private String orderNo;

    private String createUser;

    private String remark;

    private int logType;
    private String logTypeName;
    private int logId;

}
