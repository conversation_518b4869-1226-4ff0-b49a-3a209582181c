package com.ly.travel.car.carowner.facade.request;

import com.ly.sof.facade.base.BaseRequestDTO;
import com.ly.spat.dsf.utils.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.StringJoiner;

@EqualsAndHashCode(callSuper = true)
@Data
public class DriverAdjustsBillRequest extends BaseRequestDTO {

    /**
     * 判定时间
     */
    private String arbitrationTime;

    /**
     * 调账金额
     */
    private BigDecimal appealAmount;

    /**
     * 调账类型  1-充值  2-扣款
     */
    private Integer type;

    /**
     * 备注说明
     */
    private String remark;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 操作人
     */
    private String operator;

    public String validate() {
        StringJoiner errorMessages = new StringJoiner(",");
        validateField(Objects.isNull(type), "调账类型不能为空", errorMessages);
        validateField(Objects.isNull(appealAmount), "调账金额不能为空", errorMessages);
        validateField(StringUtils.isBlank(orderNo), "订单号不能为空", errorMessages);
        validateField(StringUtils.isBlank(arbitrationTime), "判定时间不能为空", errorMessages);
        validateField(StringUtils.isBlank(remark), "备注说明不能为空", errorMessages);
        validateField(StringUtils.isBlank(operator), "操作人不能为空", errorMessages);
        return errorMessages.toString();
    }

    private void validateField(boolean condition, String errorMessage, StringJoiner errorMessages) {
        if (condition) {
            errorMessages.add(errorMessage);
        }
    }
}