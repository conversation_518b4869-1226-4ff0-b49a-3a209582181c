package com.ly.travel.car.carowner.facade.request;

import com.ly.sof.facade.base.BaseRequestDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
public class DriverWithdrawalRequest extends BaseRequestDTO {

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 提现金额
     */
    private BigDecimal amount;
}