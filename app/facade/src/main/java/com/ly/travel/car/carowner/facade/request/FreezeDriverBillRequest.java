package com.ly.travel.car.carowner.facade.request;

import com.ly.sof.facade.base.BaseRequestDTO;
import com.ly.spat.dsf.utils.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Objects;
import java.util.StringJoiner;

@EqualsAndHashCode(callSuper = true)
@Data
public class FreezeDriverBillRequest extends BaseRequestDTO {

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 冻结时长 完单冻结 单位：h
     */
    private Integer freezeHour;

    /**
     * 冻结时长 完单冻结 单位：s
     */
    private Integer freezeSecond;

    /**
     * 订单号/账单号
     */
    private String orderNo;

    /**
     * 0-完单冻结 1-结算验证不通过冻结 2-客服冻结
     */
    private Integer freezeType;

    /**
     * 操作人
     */
    private String operator;

    public String validate() {
        StringJoiner errorMessages = new StringJoiner(",");
        validateField(Objects.isNull(driverId), "司机ID不能为空", errorMessages);
        validateField(Objects.isNull(freezeType), "冻结类型不能为空", errorMessages);
        validateField(StringUtils.isBlank(orderNo), "订单号不能为空", errorMessages);
        validateFreezeTypeSpecificFields(errorMessages);
        return errorMessages.toString();
    }

    private void validateFreezeTypeSpecificFields(StringJoiner errorMessages) {
        if (freezeType == 1) {
            validateField(Objects.isNull(freezeHour), "冻结时长不能为空", errorMessages);
        } else if (freezeType == 2) {
            validateField(StringUtils.isBlank(operator), "操作人不能为空", errorMessages);
        }
    }

    private void validateField(boolean condition, String errorMessage, StringJoiner errorMessages) {
        if (condition) {
            errorMessages.add(errorMessage);
        }
    }
}