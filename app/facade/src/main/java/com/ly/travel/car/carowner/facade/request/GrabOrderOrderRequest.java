package com.ly.travel.car.carowner.facade.request;

import com.ly.sof.facade.base.BaseRequestDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class GrabOrderOrderRequest extends BaseRequestDTO {

    private String orderNo;
    private String driverTripNo;
    private Long driverId;
    private String driverName;
    private String driverMobile;
    private String vehicleNo;
    private Integer hitchPercent;

}
