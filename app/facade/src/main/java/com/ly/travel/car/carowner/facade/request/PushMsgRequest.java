package com.ly.travel.car.carowner.facade.request;

import com.ly.sof.facade.base.BaseRequestDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
public class PushMsgRequest extends BaseRequestDTO {

    private String orderNo;
    private String driverTripNo;
    private Long driverId;
    private Map<String,Object> extFields;
    private String scene;
}
