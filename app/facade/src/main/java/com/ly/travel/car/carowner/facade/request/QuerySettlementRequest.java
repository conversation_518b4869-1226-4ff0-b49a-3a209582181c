package com.ly.travel.car.carowner.facade.request;

import com.ly.sof.facade.base.BaseRequestDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class QuerySettlementRequest extends BaseRequestDTO {

    private List<String> orderNos;
    private Long driverId;
    private String couponNo;
    //=1时查DB 已接单数据，其它情况是实时计算
    private Integer queryDBflag=0;
}
