package com.ly.travel.car.carowner.facade.request;

import com.ly.sof.facade.base.BaseRequestDTO;
import com.ly.spat.dsf.utils.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Objects;
import java.util.StringJoiner;

@EqualsAndHashCode(callSuper = true)
@Data
public class UnfreezeDriverAccountRequest extends BaseRequestDTO {

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 操作人
     */
    private String operator;

    public String validate() {
        StringJoiner sj = new StringJoiner(",");
        validateField(Objects.isNull(driverId), "司机id不能为空", sj);
        validateField(StringUtils.isBlank(operator), "操作人不能为空", sj);
        return sj.toString();
    }

    private void validateField(boolean condition, String errorMessage, StringJoiner sj) {
        if (condition) {
            sj.add(errorMessage);
        }
    }
}