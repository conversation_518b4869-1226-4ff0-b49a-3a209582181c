package com.ly.travel.car.carowner.facade.request;

import com.ly.sof.facade.base.BaseRequestDTO;
import com.ly.spat.dsf.utils.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.StringJoiner;

@EqualsAndHashCode(callSuper = true)
@Data
public class UnfreezeDriverBillRequest extends BaseRequestDTO {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 解冻类型 -- 与冻结类型一致
     * 单个账单可一次解冻多个类型
     * 单个订单解冻 就是解冻全部账单的多个类型
     * 具体以传的冻结类型为基准
     */
    private List<Integer> unFreezeTypes;

    /**
     * 账单号
     */
    private List<String> billNos;

    /**
     * 操作人
     */
    private String operator;

    public String validate() {
        StringJoiner sj = new StringJoiner(",");
        validateField(StringUtils.isBlank(orderNo) && CollectionUtils.isEmpty(billNos), "订单号与帐单列表二选一必填", sj);
        validateField(CollectionUtils.isEmpty(unFreezeTypes), "解冻类型列表不能为空", sj);
        return sj.toString();
    }

    private void validateField(boolean condition, String errorMessage, StringJoiner sj) {
        if (condition) {
            sj.add(errorMessage);
        }
    }
}