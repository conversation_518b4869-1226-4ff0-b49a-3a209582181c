package com.ly.travel.car.carowner.facade.request;

import lombok.Data;

import java.math.BigDecimal;

/**
 */
@Data
public class UpdateDriverAccountAmountRequest {
    // 司机ID
    private Long driverId;
    // 总金额
    private BigDecimal totalAmount;
    // 冻结金额
    private BigDecimal freezeAmount;
    // 提现中金额
    private BigDecimal withdrawingAmount;
    // 已提现金额
    private BigDecimal withdrawnAmount;
    // 可用金额
    private BigDecimal availableAmount;
    // 操作人
    private String operator;
}
