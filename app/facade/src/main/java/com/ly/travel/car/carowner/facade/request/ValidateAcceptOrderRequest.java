package com.ly.travel.car.carowner.facade.request;

import com.ly.sof.facade.base.BaseRequestDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.Objects;
import java.util.StringJoiner;

@EqualsAndHashCode(callSuper = true)
@Data
public class ValidateAcceptOrderRequest extends BaseRequestDTO {

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 乘客用车时间
     */
    private Date startingTime;

    /**
     * 出发城市id
     */
    private Long startCityId;

    /**
     * 到达城市id
     */
    private Long endCityId;

    public String validate() {
        StringJoiner errorMessages = new StringJoiner(",");
        validateField(Objects.isNull(startingTime), "乘客用车时间不能为空", errorMessages);
        validateField(Objects.isNull(driverId), "司机id不能为空", errorMessages);
        validateField(Objects.isNull(endCityId), "到达城市id不能为空", errorMessages);
        validateField(Objects.isNull(startCityId), "出发城市id不能为空", errorMessages);
        return errorMessages.toString();
    }

    private void validateField(boolean condition, String errorMessage, StringJoiner errorMessages) {
        if (condition) {
            errorMessages.add(errorMessage);
        }
    }
}
