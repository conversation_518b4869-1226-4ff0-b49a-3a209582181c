package com.ly.travel.car.carowner.facade.request.callback;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @desc AcceptOrderRequest
 */
@Data
public class AcceptOrderRequest extends ApiCallBackBaseRequest {


    /**
     * 接单司机姓名
     */
    private String driverName;
    /**
     * 司机手机号
     */
    private String driverPhone;
    /**
     * 司机当前位置经度
     */
    private BigDecimal longitude;
    /**
     * 司机当前位置纬度
     */
    private BigDecimal latitude;
    /**
     * 品牌
     */
    private String vehicleBrand;
    /**
     * 型号
     */
    private String vehicleModel;
    /**
     * 颜色
     */
    private String vehicleColor;
    /**
     * 车牌号
     */
    private String vehiclePlateNum;
    /**
     * 司机行程订单号
     */
    private String driverTripNo;
    /**
     * 司机顺路度
     */
    private Integer hitchPercent;
    /**
     *  0或空为普通接单，1自动接单
     */
    private Integer acceptType;
}
