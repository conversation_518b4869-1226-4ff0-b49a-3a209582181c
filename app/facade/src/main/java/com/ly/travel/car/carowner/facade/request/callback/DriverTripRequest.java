package com.ly.travel.car.carowner.facade.request.callback;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @desc DriverTripRequest
 */
@Data
public class DriverTripRequest extends ApiCallBackBaseRequest {

    /**
     * 行程类型 50-司机出发接乘客;100-司机到达乘客起点;200-乘客已上车;260-司机到达目的地
     */
    private Integer tripType;
    /**
     * 司机经度
     */

    private BigDecimal drivingLon;
    /**
     * 司机纬度
     */

    private BigDecimal drivingLat;

    /**
     * 乘客尾号后四位
     */
    private String endPhoneNo;

    /**
     * 自动完单标识 0-非自动完单 1-自动完单
     */
    private Integer autoCompletionFlag;
}
