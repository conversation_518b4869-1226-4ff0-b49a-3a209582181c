package com.ly.travel.car.carowner.facade.response;

import com.ly.sof.facade.base.BaseResponseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.UUID;

@EqualsAndHashCode(callSuper = true)
@Data
public class CarOwnerResponseDTO<T> extends BaseResponseDTO {

    private T data;

    public static <T> CarOwnerResponseDTO<T> fail(String errorMsg) {
        CarOwnerResponseDTO<T> responseDTO = new CarOwnerResponseDTO<>();
        responseDTO.setErrorCode("500");
        responseDTO.setSuccess(false);
        responseDTO.setErrorMessage(errorMsg);
        responseDTO.setTraceId(UUID.randomUUID().toString());
        return responseDTO;
    }

    public static <T> CarOwnerResponseDTO<T> fail(String errorCode, String errorMsg) {
        CarOwnerResponseDTO<T> responseDTO = new CarOwnerResponseDTO<>();
        responseDTO.setErrorCode(errorCode);
        responseDTO.setSuccess(false);
        responseDTO.setErrorMessage(errorMsg);
        responseDTO.setTraceId(UUID.randomUUID().toString());
        return responseDTO;
    }

    public static <T> CarOwnerResponseDTO<T> succeed(T body) {
        CarOwnerResponseDTO<T> responseDTO = new CarOwnerResponseDTO<>();
        responseDTO.setSuccess(true);
        responseDTO.setTraceId(UUID.randomUUID().toString());
        responseDTO.setData(body);
        return responseDTO;
    }
}
