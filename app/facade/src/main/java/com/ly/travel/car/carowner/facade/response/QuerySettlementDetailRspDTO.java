package com.ly.travel.car.carowner.facade.response;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class QuerySettlementDetailRspDTO extends QuerySettlementRspDTO {


    private List<SettlementRefundInfo> refundList;

    @Data
    @Builder
    public static class SettlementRefundInfo{

        private String createTime;
        private BigDecimal refundAmount;
    }
}
