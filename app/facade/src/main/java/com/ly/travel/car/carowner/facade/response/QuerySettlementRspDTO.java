package com.ly.travel.car.carowner.facade.response;

import com.ly.sof.facade.base.BaseResponseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
public class QuerySettlementRspDTO   {
    private String orderNo;

    /** 司机ID */
    private Long driverId;

    /** 供应商结算价 */
    private BigDecimal supplierSettlementPrice;

    /** 平台结算收益 */
    private BigDecimal platformSettlementPrice;

    /** 供应商分佣比例 */
    private BigDecimal supplierMaidRate;

    /** 平台分佣比例 */
    private BigDecimal platformMaidRate;

    /** 司机结算价，接单时的 */
    private BigDecimal driverSettlementPrice;
    /** 司机结算价,最新 */
    private BigDecimal driverSettlementPrice_now;
    //司机额外免佣金额
    private  BigDecimal driverExtraSettlementPrice;

}
