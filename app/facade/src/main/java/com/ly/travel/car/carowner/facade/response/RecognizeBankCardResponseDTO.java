package com.ly.travel.car.carowner.facade.response;

import lombok.Data;

/**
 * @className: RecognizeBankCardResponseDTO
 * @author: zmj
 * @date: 2025-6-17 16:22
 * @Version: 1.0
 * @description:
 */

@Data
public class RecognizeBankCardResponseDTO {

    // DEBIT:借记卡 CREDIT:贷记卡 CORPORATE:对公银行账户
    private String bankCardType;
    //卡类型名称
    private String bankCardTypeName;

    private String bankName;

    private String bankCardCode;
}
                        