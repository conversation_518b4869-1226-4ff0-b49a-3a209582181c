package com.ly.travel.car.carowner.facade.response;

import com.ly.sof.facade.base.BaseResponseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class SaveTestRspDTO extends BaseResponseDTO {

    private static final long serialVersionUID = 7542396243705756829L;

    public static SaveTestRspDTO fail(String traceId, String errorCode, String errorMessage) {
        SaveTestRspDTO response = new SaveTestRspDTO();
        response.setTraceId(traceId);
        response.setSuccess(false);
        response.setErrorCode(errorCode);
        response.setErrorMessage(errorMessage);
        return response;
    }

    public static SaveTestRspDTO success(String traceId) {
        SaveTestRspDTO response = new SaveTestRspDTO();
        response.setTraceId(traceId);
        response.setSuccess(true);
        return response;
    }
}
