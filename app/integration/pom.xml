<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
        <groupId>com.ly.travel.car.carowner</groupId>
        <artifactId>shared-mobility-carowner-core</artifactId>
        <version>1.0-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>shared-mobility-carowner-core-integration</artifactId>
    <packaging>jar</packaging>

    <name>LY shared-mobility-carowner-core-integration</name>
    <description>LY shared-mobility-carowner-core-integration</description>

    <dependencies>
        <dependency>
            <groupId>com.ly.travel.car.carowner</groupId>
            <artifactId>shared-mobility-carowner-core-common</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-lang3</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--region log dependecies -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
        </dependency>
        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>

        <!-- log4j2 -->
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j-impl</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>jcl-over-slf4j</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-web</artifactId>
        </dependency>
        <!--endregion -->
        <!-- Test dependecies -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-all</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.easymock</groupId>
            <artifactId>easymock</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-easymock</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.ly.flight.toolkit</groupId>
            <artifactId>object-diff</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ly.sof</groupId>
            <artifactId>sof-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-miniapp</artifactId>
            <version>4.7.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-lang3</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ly.dsf</groupId>
            <artifactId>dsf-client</artifactId>
            <version>2.5.14</version>
        </dependency>
        <!-- 热部署组件依赖redis 依赖tcbase cache后开启 -->
        <!--		<dependency>-->
        <!--			<groupId>com.ly.flight.toolkit</groupId>-->
        <!--			<artifactId>deploy-static-resource</artifactId>-->
        <!--		</dependency>-->

       <!-- <dependency>
            <groupId>com.ly.travel.car.carowner</groupId>
            <artifactId>MainFlowDSFApi</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-to-slf4j</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>configcenterclient</artifactId>
                    <groupId>com.ly.tcbase</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>logback-classic</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>logback-core</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>pagehelper-spring-boot-starter</artifactId>
                    <groupId>com.github.pagehelper</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>TCBUSNewCommonTools</artifactId>
                    <groupId>com.ly.busticket</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>skylog-slf4j-adapter</artifactId>
                    <groupId>com.ly.traffic</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ly.travel.car.carowner</groupId>
            <artifactId>order-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-to-slf4j</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>configcenterclient</artifactId>
                    <groupId>com.ly.tcbase</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>logback-classic</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>logback-core</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>

                <exclusion>
                    <groupId>com.ly.busticket</groupId>
                    <artifactId>TCBUSNewCommonTools</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>pagehelper-spring-boot-starter</artifactId>
                    <groupId>com.github.pagehelper</groupId>
                </exclusion>

            </exclusions>

        </dependency>
-->
        <dependency>
            <groupId>com.yeepay.yop.sdk</groupId>
            <artifactId>yop-java-sdk</artifactId>
            <version>4.4.7</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-lang3</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.yeepay.yop.sdk</groupId>
            <artifactId>yop-java-sdk-crypto-gm</artifactId>
            <version>4.4.7</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-lang3</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.yeepay.g3.yop</groupId>
            <artifactId>yop-java-sdk</artifactId>
            <version>3.2.11</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ly.travel.pushcore</groupId>
            <artifactId>travel-push-platform-core-facade</artifactId>
            <version>1.0.0.30.RELEASE</version>
        </dependency>

    </dependencies>
</project>
