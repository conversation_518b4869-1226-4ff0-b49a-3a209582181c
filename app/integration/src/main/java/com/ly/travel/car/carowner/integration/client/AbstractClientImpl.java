package com.ly.travel.car.carowner.integration.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.ly.sof.api.exception.LYRuntimeException;
import com.ly.sof.utils.common.UUID;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.integration.exception.IntegrationException;
import com.ly.travel.car.carowner.integration.exception.IntegrationTimeoutException;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.function.Function;

public abstract class AbstractClientImpl {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * Call t out.
     *
     * @param <TIn>          the type parameter
     * @param <TOut>         the type parameter
     * @param request        the request
     * @param function       the function
     * @param module         the module
     * @param category       the category
     * @param traceId        the trace id
     * @param retry          当遇到服务调用超时时,需要重试的次数,小于1视为不需要重试.
     * @param ignoreRequest  the ignore request
     * @param ignoreResponse the ignore response
     * @param <TIn>          the type parameter
     * @param <TOut>         the type parameter
     * @param request        the request
     * @param function       the function
     * @return the t out
     * @throws IntegrationException the integration exception
     */
    protected <TIn, TOut> TOut call(TIn request, Function<TIn, TOut> function, String module, String category, String traceId, int retry, boolean ignoreRequest,
                                    boolean ignoreResponse) throws IntegrationException {
        if (retry <= 0) {
            return this.call(request, function, module, category, traceId, ignoreRequest, ignoreResponse);
        }

        // 当前服务重试次数
        int cur = 0;
        do {
            try {
                return this.call(request, function, module, category, traceId, ignoreRequest, ignoreResponse);
            } catch (IntegrationTimeoutException e) {
                cur++;
                if (cur > retry) {
                    throw e;
                }
                LoggerUtils.info(logger, "服务调用超时,准备重试,当前是第{}次重试,最大可重试{}次", cur, retry);
            }
        } while (true);
    }

    /**
     * Call t out.
     *
     * @param <TIn>          the type parameter
     * @param <TOut>         the type parameter
     * @param request        the request
     * @param function       the function
     * @param module         the module
     * @param category       the category
     * @param traceId        the traceId
     * @param ignoreRequest  the ignore request
     * @param ignoreResponse the ignore response
     * @return the t out
     * @throws IntegrationException 与二方交互过程中发生的异常
     */
    @SuppressWarnings("all")
    protected <TIn, TOut> TOut call(TIn request, Function<TIn, TOut> function, String module, String category, String traceId, boolean ignoreRequest,
                                    boolean ignoreResponse) throws IntegrationException {
        String requestJSON = "";
        String responseJSON = "";
        String errorJSON = "";
        String uuid = UUID.generateRandomUUID();
        long start = System.currentTimeMillis();
        try {
            if (!ignoreRequest) {
                requestJSON = FastJsonUtils.toJSONString(request);
            } else {
                requestJSON = "ignore";
            }

            LoggerUtils.info(logger, "[Integration][{}.{}] >>> uuid：{}，request：{}", module, category, uuid, requestJSON);
            TOut response = function.apply(request);
            if (!ignoreResponse) {
                responseJSON = FastJsonUtils.toJSONString(response);
            } else {
                responseJSON = "ignore";
            }

            return response;
        } catch (LYRuntimeException e) {
            errorJSON = "LYRuntimeException：" + e.toString();
            throw e;
        } catch (RuntimeException e) {
            errorJSON = "RuntimeException：" + e.toString();
            throw e;
        } catch (Exception e) {
            errorJSON = "Exception：" + e.toString();
            throw e;
        } finally {
            if (StringUtils.isNotBlank(errorJSON)) {
                LoggerUtils.error(logger, "[Integration][{}.{}] >>> uuid：{}，cost：{}，exception：{}", module, category, uuid, System.currentTimeMillis() - start, errorJSON);
            } else {
                LoggerUtils.info(logger, "[Integration][{}.{}] >>> uuid：{}，cost：{}，response：{}", module, category, uuid, System.currentTimeMillis() - start, responseJSON);
            }
        }
    }

    public <T> T doExecute(OkHttpClient okHttpClient, Request request, Class<T> clazz) throws IOException {
        Response response = okHttpClient.newCall(request).execute();
        String result = response.body().string();
        LoggerUtils.info(logger, "[Integration][doExecute] >>> response：{}", result);

        if (clazz == String.class && clazz == byte[].class) {
            return (T) result;
        }
        return FastJsonUtils.fromJSONString(result, clazz);
    }

    public <T> T doExecute(OkHttpClient okHttpClient, Request request, TypeReference<T> ref) throws IOException {
        Response response = okHttpClient.newCall(request).execute();
        String result = response.body().string();
        LoggerUtils.info(logger, "[Integration][doExecute] >>> response：{}", result);

        if (ref.getType() == String.class && ref.getType() == byte[].class) {
            return (T) result;
        }

        try {
            return FastJsonUtils.fromJSONString(result, ref);
        } catch (Exception e) {
            return JSON.parseObject(result, ref);
        }
    }
}
