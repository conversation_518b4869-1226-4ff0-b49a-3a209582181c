package com.ly.travel.car.carowner.integration.client;

import com.alibaba.fastjson.TypeReference;
import com.ly.sof.utils.log.LogContextUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.integration.exception.IntegrationException;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.util.StringUtils;

import java.util.concurrent.TimeUnit;

@Slf4j
public class BaseClient extends AbstractClientImpl {
    private static final MediaType JSON_MEDIA_TYPE = MediaType.parse("application/json;charset=UTF-8");

    public OkHttpClient okHttpClient = new OkHttpClient.Builder()
            .retryOnConnectionFailure(true)
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(10, TimeUnit.SECONDS)
            .writeTimeout(10, TimeUnit.SECONDS)
            .connectionPool(new ConnectionPool(300, 5L, TimeUnit.MINUTES))
            .build();

    /**
     * post请求
     *
     * @param request      入参
     * @param url          请求地址
     * @param responseType 返参类型
     * @param <T>          返参
     * @return
     * @throws IntegrationException
     */
    public <T> T executePost(Object request, String url, Class<T> responseType) throws IntegrationException {
        log.info("[REQUEST] >>> url={} req={}", url, FastJsonUtils.toJSONString(request));
        Request requestJson = buildRequest(request, url);
        T response = super.call(requestJson, req -> {
            try {
                return doExecute(okHttpClient, requestJson, responseType);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }, getClass().getSimpleName(), StringUtils.getFilename(url), LogContextUtils.getTraceId(), Boolean.FALSE, Boolean.FALSE);
        log.info("[RESPONSE] >>> url={} rsp={}", url, FastJsonUtils.toJSONString(response));
        return response;
    }

    /**
     * post请求
     *
     * @param request 入参
     * @param url     请求地址
     * @param ref     返参类型
     * @param <T>     返参
     * @return
     * @throws IntegrationException
     */
    public <T> T executePost(Object request, String url, TypeReference<T> ref) throws IntegrationException {
        log.info("[REQUEST] >>> url={} req={}", url, FastJsonUtils.toJSONString(request));
        Request requestJson = buildRequest(request, url);
        T response = super.call(requestJson, req -> {
            try {
                return doExecute(okHttpClient, requestJson, ref);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }, getClass().getSimpleName(), StringUtils.getFilename(url), LogContextUtils.getTraceId(), Boolean.FALSE, Boolean.FALSE);
        log.info("[RESPONSE] >>> url={} rsp={}", url, FastJsonUtils.toJSONString(response));
        return response;
    }

    public Request buildRequest(Object request, String url) {
        RequestBody requestBody = RequestBody.create(JSON_MEDIA_TYPE, FastJsonUtils.toJSONString(request));
        return new Request.Builder()
                .url(url)
                .post(requestBody)
                .build();
    }
}
