package com.ly.travel.car.carowner.integration.client.api;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Maps;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.common.utils.OkHttpClientUtil;
import com.ly.travel.car.carowner.integration.client.api.lbs.DistanceReq;
import com.ly.travel.car.carowner.integration.client.api.lbs.DistanceRsp;
import com.ly.travel.car.carowner.integration.client.api.lbs.Path;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 基础数据lbs服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Setter
public class LbsClient {

    @Value("${http.lbs.root.url}")
    private String lbsUrl;

    private final static long TIME_OUT = 1;
    public static final String  APP_KEY  = "carowner_mobile_key";


    Map<String, String> httpHeaders = Maps.newHashMap();

    {
        httpHeaders.put("app-key", APP_KEY);
    }

    /**
     * 获取路径信息
     *
     * @param distanceReq
     * @return
     */
    public Path getPath(DistanceReq distanceReq) {
        try {
            String requestStr = JSON.toJSONString(distanceReq);
            long s = System.currentTimeMillis();
            log.info("LbsClient.getPath request:{}", requestStr);
            String responseStr = OkHttpClientUtil.getInstance().post(lbsUrl + "location/driver/navigate", requestStr, httpHeaders, TIME_OUT, TimeUnit.SECONDS);
            if (StringUtils.isBlank(responseStr)) {
                log.warn("LbsClient.getPath response is empty");
                return null;
            }
            DistanceRsp distanceRsp = FastJsonUtils.fromJSONString(responseStr, DistanceRsp.class);
            if (!NumberUtils.INTEGER_ZERO.equals(distanceRsp.getCode())) {
                log.warn("LbsClient.getPath response error:{}", responseStr);
                return null;
            }
            if (Objects.isNull(distanceRsp.getResult()) || ArrayUtils.isEmpty(distanceRsp.getResult().getPaths())) {
                log.warn("LbsClient.getPath response path is empty:{}", responseStr);
                return null;
            }
            log.info("LbsClient.getPath response:{}, expend:{}", FastJsonUtils.toJSONString(distanceRsp), System.currentTimeMillis() - s);
            DistanceRsp.PathV5 pathV5 = distanceRsp.getResult().getPaths()[0];
            Path path = new Path();
            path.setDistance(new BigDecimal(pathV5.getDistance()).intValue());
            path.setDuration(new BigDecimal(pathV5.getCost().getDuration()).intValue());
            path.setTolls(new BigDecimal(pathV5.getCost().getTolls()));
            return path;
        } catch (Throwable throwable) {
            log.error("lbs error", throwable);
        }
        return null;
    }
}