package com.ly.travel.car.carowner.integration.client.api.lbs;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class DistanceReq {

    /**
     * 开始的经纬度，GCJ-02高德的火星坐标系
     */
    private BigDecimal origin_latitude;
    /**
     * 开始的经纬度，GCJ-02高德的火星坐标系
     */
    private BigDecimal origin_longitude;
    /**
     * 目的地的经纬度，GCJ-02高德的火星坐标系
     */
    private BigDecimal destination_latitude;
    /**
     * 目的地的经纬度，GCJ-02高德的火星坐标系
     */
    private BigDecimal destination_longitude;
    /**
     * 点位类型
     */
    private String geo_type;
    /**
     * 开始的高德 POI_ID，没有就留空
     */
    private String origin_poi_id;
    /**
     * 目的地的高德 POI_ID，没有就留空
     */
    private String destination_poi_id;
    /**
     * 平台的枚举, 这里传 9999
     */
    private Integer platform;
    /**
     * sharding_key platfrom + UserIdentity 能够找到对应的用户
     * UserIdentity 为memberId
     */
    private String user_identity;
    /**
     * 请求追踪ID
     */
    private String trace_id;
}