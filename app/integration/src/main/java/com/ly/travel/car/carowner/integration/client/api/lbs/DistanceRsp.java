package com.ly.travel.car.carowner.integration.client.api.lbs;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class DistanceRsp {

    private Integer code;

    private String msg;

    private Route result;

    @Data
    public static class Route {
        private PathV5[] paths;
    }

    @Data
    public static class PathV5 {
        private String distance;
        private CostV5 cost;
    }

    @Data
    public static class CostV5 {
        private String duration;
        private String tolls;
    }
}