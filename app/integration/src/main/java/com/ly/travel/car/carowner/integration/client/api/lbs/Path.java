package com.ly.travel.car.carowner.integration.client.api.lbs;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class Path {

    /**
     * 行驶距离，单位：米
     */
    private Integer distance;
    /**
     * 预计行驶时间，单位：秒
     */
    private Integer duration;
    /**
     * 此导航方案道路收费，单位：元
     */
    private BigDecimal tolls;
}
