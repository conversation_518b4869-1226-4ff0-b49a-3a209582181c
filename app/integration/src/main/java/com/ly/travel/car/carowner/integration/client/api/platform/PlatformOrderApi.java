package com.ly.travel.car.carowner.integration.client.api.platform;

import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.integration.client.BaseClient;
import com.ly.travel.car.carowner.integration.client.api.platform.request.QueryOrderAmountRequest;
import com.ly.travel.car.carowner.integration.client.api.platform.response.QueryOrderAmountResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PlatformOrderApi extends BaseClient {

    @Value("${orderPayAmount.callBackUrl}")
    private String orderPayAmountCallBackUrl;

    public QueryOrderAmountResponse findByOrderAmount(QueryOrderAmountRequest request) {
        try {
            return executePost(request, orderPayAmountCallBackUrl + "/orderSettlement/callback/queryOrderSettlementAmount", QueryOrderAmountResponse.class);
        } catch (Exception e) {
            log.error("查询订单扣款状态和金额失败 >>> request={}", FastJsonUtils.toJSONString(request), e);
        }
        return null;
    }
}