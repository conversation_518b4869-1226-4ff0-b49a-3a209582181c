package com.ly.travel.car.carowner.integration.client.api.platform.request;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@Builder
public class QueryOrderAmountRequest implements Serializable {

    /**
     * 同程订单号
     */
    private String tcSerialNo;

    /**
     * 供应商订单号
     */
    private String supplierOrderNo;

    /**
     * Unix时间戳，秒级别
     */
    private Long timestamp;

    /**
     * 是否需要校验可提现
     */
    private Boolean checkWithdraw;

    /**
     * 链路id
     */
    private String traceId;
}