package com.ly.travel.car.carowner.integration.client.api.platform.response;

import lombok.Data;

import java.io.Serializable;

@Data
public class QueryOrderAmountResponse implements Serializable {

    /**
     * 链路id
     */
    private String traceId;

    /**
     * 订单结算价
     */
    private String amount;

    /**
     * 乘客扣款状态：0-未支付，1-已支付
     */
    private Integer payStatus;

    /**
     * 是否可以提现 false:不可以 true:可以
     */
    private Boolean ownerCanWithdrawal;

    /**
     * 200：成功，其他：失败
     */
    private String code;

    /**
     * 描述
     */
    private String msg;
}