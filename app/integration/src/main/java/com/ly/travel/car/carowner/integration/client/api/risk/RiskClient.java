package com.ly.travel.car.carowner.integration.client.api.risk;

import com.ly.travel.car.carowner.integration.client.api.risk.model.request.RiskApiRequest;
import com.ly.travel.car.carowner.integration.client.api.risk.model.request.UnifyCheckRequest;
import com.ly.travel.car.carowner.integration.client.api.risk.model.response.SimpleCheckResponse;
import com.ly.travel.car.carowner.integration.client.api.risk.model.response.UnifyCheckResponse;
import com.ly.travel.car.carowner.integration.exception.IntegrationException;

public interface RiskClient {

    SimpleCheckResponse simpleCheck(RiskApiRequest request) throws IntegrationException;

    UnifyCheckResponse unifyCheck(RiskApiRequest<UnifyCheckRequest> request) throws IntegrationException;
}
