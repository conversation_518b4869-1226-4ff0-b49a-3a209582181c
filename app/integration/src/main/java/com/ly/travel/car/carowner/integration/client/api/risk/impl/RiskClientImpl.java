package com.ly.travel.car.carowner.integration.client.api.risk.impl;

import com.alibaba.fastjson.TypeReference;
import com.ly.travel.car.carowner.common.constant.RiskConstant;
import com.ly.travel.car.carowner.integration.client.BaseClient;
import com.ly.travel.car.carowner.integration.client.api.risk.RiskClient;
import com.ly.travel.car.carowner.integration.client.api.risk.model.request.RiskApiRequest;
import com.ly.travel.car.carowner.integration.client.api.risk.model.request.UnifyCheckRequest;
import com.ly.travel.car.carowner.integration.client.api.risk.model.response.RiskApiResponse;
import com.ly.travel.car.carowner.integration.client.api.risk.model.response.SimpleCheckResponse;
import com.ly.travel.car.carowner.integration.client.api.risk.model.response.UnifyCheckResponse;
import com.ly.travel.car.carowner.integration.exception.IntegrationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service("riskClient")
public class RiskClientImpl extends BaseClient implements RiskClient {

    @Value("${risk.url}")
    private String riskUrl;

    @Override
    public SimpleCheckResponse simpleCheck(RiskApiRequest request) throws IntegrationException {
        RiskApiResponse<SimpleCheckResponse> riskApiResponse = executePost(request, riskUrl + "/riskCheck/simpleCheck", new TypeReference<RiskApiResponse<SimpleCheckResponse>>() {
        });
        if (riskApiResponse != null && riskApiResponse.getErrCode() == RiskConstant.RiskReturnCodeEnum.SUCCESS.getCode()) {
            return riskApiResponse.getData();
        }
        return null;
    }

    @Override
    public UnifyCheckResponse unifyCheck(RiskApiRequest<UnifyCheckRequest> request) throws IntegrationException {
        RiskApiResponse<UnifyCheckResponse> riskApiResponse = executePost(request, riskUrl + "/riskCheck/unifyCheck", new TypeReference<RiskApiResponse<UnifyCheckResponse>>() {
        });
        if (riskApiResponse != null && riskApiResponse.getErrCode() == RiskConstant.RiskReturnCodeEnum.SUCCESS.getCode()) {
            return riskApiResponse.getData();
        }
        return null;
    }
}
