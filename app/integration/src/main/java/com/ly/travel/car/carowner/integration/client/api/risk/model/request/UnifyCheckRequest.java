package com.ly.travel.car.carowner.integration.client.api.risk.model.request;

import lombok.Data;

/**
 * 统一风控全场景校验请求参数
 */
@Data
public class UnifyCheckRequest {

    /**
     * 场景值
     */
    private String scene;

    /**
     * 会员id
     */
    private String memberId;

    /**
     * unionId
     */
    private String unionId;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 产品线
     */
    private String productLine;

    /**
     * 交易订单号
     */
    private String orderId;

    private String userPhone;

    private String passengerPhone;

    private String carNum;

    //private String  ip;
    //private String  supplierCode;
    //private String  supplierName;

    /**
     * 用车分销单标识
     * false/true，默认false
     */
    //private boolean distributionFlag;

    /**
     * 权益单标识  false/true，默认false
     */
    //private boolean rightsOrderFlag;


    /**
     * 支付账号
     */
    //private String  payAccount;

    /**
     * 设备id
     */
    //private String  deviceId;

    /**
     * 全链路上下文id
     */
    private String traceId;

    /**
     * 业务订单号
     */
    private String supplierOrderId;

    /**
     * 司机id
     */
    private String driverId;

    /**
     *
     */
    private String toDriverId;

    /**
     * 证件号
     */
    private String certNo;

    /**
     * 证件姓名
     */
    private String certName;

    /**
     * 司机手机号
     */
    private String driverPhone;

    /**
     * 行驶证所有人姓名
     */
    private String carOwnerName;

    /**
     * 车牌类型
     */
    //private String  plateType;

    /**
     * 银行卡号
     */
    //private String  bankCardNo;
}
