package com.ly.travel.car.carowner.integration.client.api.risk.model.response;

import com.ly.travel.car.carowner.integration.client.api.risk.model.dto.RiskStrategyRespResult;
import lombok.Data;

import java.util.List;

@Data
public class UnifyCheckResponse {

    /**
     * 响应码  false：代表通过  true：未通过
     */
    private boolean riskFlag;

    /**
     * 响应消息
     */
    private String riskMsg;

    /**
     * 匹配的策略
     */
    private List<String> matchedStrategy;

    /**
     * 命中策略集合
     */
    private List<RiskStrategyRespResult> matchedStrategyList;

    /**
     * 名单类型
     */
    private int customerType;

    /**
     * 冻结到期时间 -- yyyy-MM-dd HH:mm:ss
     */
    private String freezeTime;

    /**
     * 交易订单号
     */
    private String orderId;
}
