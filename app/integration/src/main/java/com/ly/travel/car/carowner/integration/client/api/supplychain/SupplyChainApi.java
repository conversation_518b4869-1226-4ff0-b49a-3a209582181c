package com.ly.travel.car.carowner.integration.client.api.supplychain;

import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.common.utils.CarownerConfigCenterUtils;
import com.ly.travel.car.carowner.integration.client.BaseClient;
import com.ly.travel.car.carowner.integration.client.api.supplychain.request.CallbackRequest;
import com.ly.travel.car.carowner.integration.client.api.supplychain.response.CallbackBaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @desc SupplyChainApi
 */
@Slf4j
@Component
public class SupplyChainApi extends BaseClient {

  

    public CallbackBaseResponse callback(CallbackRequest request) {
        try {
            return executePost(request, CarownerConfigCenterUtils.carPlatformCallBackUrl + "/sfc/tcsfc/status", CallbackBaseResponse.class);
        } catch (Exception e) {
            log.error("供应商回调失败 >>> request={}", FastJsonUtils.toJSONString(request), e);
        }
        CallbackBaseResponse response = new CallbackBaseResponse();
        response.setCode("-1");
        return response;
    }
}
