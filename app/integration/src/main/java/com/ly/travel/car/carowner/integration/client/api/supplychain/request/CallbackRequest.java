package com.ly.travel.car.carowner.integration.client.api.supplychain.request;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @desc CallbackRequest
 */
@Data
public class CallbackRequest {

    private String traceId;
    /**
     * 时间戳
     */
    private Long timestamp;
    /**
     * 订单号
     */
    private String tcSerialNo;
    /**
     * 订单号
     */
    private String supplierOrderNo;
    /**
     * 订单状态
     */
    private Integer status;
    /**
     * 司机id
     */
    private String driverId;
    /**
     * 接单司机姓名
     */
    private String driverName;
    /**
     * 司机手机号
     */
    private String driverPhone;
    /**
     * 司机当前位置经度
     */
    private BigDecimal longitude;
    /**
     * 司机当前位置纬度
     */
    private BigDecimal latitude;
    /**
     * 品牌
     */
    private String vehicleBrand;
    /**
     * 型号
     */
    private String vehicleModel;
    /**
     * 颜色
     */
    private String vehicleColor;
    /**
     * 车牌号
     */
    private String vehiclePlateNum;
    /**
     * 司机行程订单号
     */
    private String driverTripNo;
    /**
     * 司机顺路度
     */
    private Integer hitchPercent;
    /**
     * 取消原因
     */
    private String cancelReason;
    /**
     * 取消类型
     */
    private Integer cancelType;
}
