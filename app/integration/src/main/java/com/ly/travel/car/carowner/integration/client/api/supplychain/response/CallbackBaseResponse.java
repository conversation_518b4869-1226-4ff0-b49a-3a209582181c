package com.ly.travel.car.carowner.integration.client.api.supplychain.response;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @desc CallbackBaseResponse
 */
@Data
public class CallbackBaseResponse {

    /**
     * 链路id
     */
    private String traceId;
    /**
     * 200：成功，其他：失败
     */
    private String code;
    /**
     * 描述
     */
    private String msg;

    public boolean isSuccess() {
        return StringUtils.isNotEmpty(code) && code.equals("200");
    }
}
