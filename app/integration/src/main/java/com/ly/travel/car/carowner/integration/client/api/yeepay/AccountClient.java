package com.ly.travel.car.carowner.integration.client.api.yeepay;

import com.ly.travel.car.carowner.integration.client.api.yeepay.request.PayOrderRequest;
import com.ly.travel.car.carowner.integration.client.api.yeepay.response.PayOrderResponse;
import com.yeepay.yop.sdk.exception.YopClientException;

public interface AccountClient {

    /**
     * 付款-下单
     * 为了保证出款成功，各农信社卡或账号16位以下的农业银行卡，建议或尽可能填写支行编码；
     *
     * @return PayOrderResponse
     * @throws YopClientException if fails to make API call
     */
    PayOrderResponse payOrder(PayOrderRequest request) throws YopClientException;

    /**
     * Shuts down this client object, releasing any resources that might be held open. This is an optional method, and
     * callers are not expected to call it, but can if they want to explicitly release any open resources. Once a client
     * has been shutdown, it should not be used to make any more requests.
     */
    void shutdown();
}
