package com.ly.travel.car.carowner.integration.client.api.yeepay;

import com.yeepay.yop.sdk.auth.req.AuthorizationReqRegistry;
import com.yeepay.yop.sdk.auth.req.DefaultAuthorizationReqRegistry;
import com.yeepay.yop.sdk.client.AbstractServiceClientBuilder;
import com.yeepay.yop.sdk.client.ClientParams;

public class AccountClientBuilder extends AbstractServiceClientBuilder<AccountClientBuilder, AccountClientImpl> {

    private static final AuthorizationReqRegistry REGISTRY;

    static {
        REGISTRY = new DefaultAuthorizationReqRegistry();
        REGISTRY.register("payOrder", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
    }

    @Override
    protected AuthorizationReqRegistry authorizationReqRegistry() {
        return REGISTRY;
    }

    @Override
    protected AccountClientImpl build(ClientParams params) {
        return new AccountClientImpl(params);
    }

    /**
     * @return Create new instance of builder with all defaults set.
     */
    public static AccountClientBuilder builder() {
        return new AccountClientBuilder();
    }

}
