package com.ly.travel.car.carowner.integration.client.api.yeepay;

import com.ly.travel.car.carowner.integration.client.api.yeepay.request.PayOrderRequest;
import com.ly.travel.car.carowner.integration.client.api.yeepay.request.PayOrderRequestMarshaller;
import com.ly.travel.car.carowner.integration.client.api.yeepay.response.PayOrderResponse;
import com.yeepay.yop.sdk.client.*;
import com.yeepay.yop.sdk.exception.YopClientException;
import com.yeepay.yop.sdk.http.HttpResponseAnalyzerSupport;
import com.yeepay.yop.sdk.http.HttpResponseHandler;
import com.yeepay.yop.sdk.http.handler.DefaultHttpResponseHandler;
import com.yeepay.yop.sdk.model.transform.RequestMarshaller;

public class AccountClientImpl implements AccountClient {

    private final ClientHandler clientHandler;

    AccountClientImpl(ClientParams clientParams) {
        this.clientHandler = new ClientHandlerImpl(new ClientHandlerParams().withClientParams(clientParams));
    }

    @Override
    public PayOrderResponse payOrder(PayOrderRequest request) throws YopClientException {
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<PayOrderRequest> requestMarshaller = PayOrderRequestMarshaller.getInstance();
        HttpResponseHandler<PayOrderResponse> responseHandler =
                new DefaultHttpResponseHandler<>(PayOrderResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<PayOrderRequest, PayOrderResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }

    @Override
    public void shutdown() {
        clientHandler.shutdown();
    }
}
