package com.ly.travel.car.carowner.integration.client.api.yeepay;

import com.ly.travel.car.carowner.common.utils.CarownerConfigCenterUtils;
import com.yeepay.g3.sdk.yop.utils.JsonUtils;
import com.yeepay.yop.sdk.base.auth.credentials.provider.YopFixedCredentialsProvider;
import com.yeepay.yop.sdk.base.config.YopAppConfig;
import com.yeepay.yop.sdk.config.enums.CertStoreType;
import com.yeepay.yop.sdk.config.provider.file.YopCertConfig;
import com.yeepay.yop.sdk.security.CertTypeEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class CustomFixedCredentialsProvider extends YopFixedCredentialsProvider {



    @Override
    protected YopAppConfig loadAppConfig(String appKey) {
        YopAppConfig yopAppConfig = new YopAppConfig();
        yopAppConfig.setAppKey(appKey);

        // SM2 example
        YopCertConfig sm2CertConfig = new YopCertConfig();
        sm2CertConfig.setCertType(CertTypeEnum.SM2);
        sm2CertConfig.setStoreType(CertStoreType.STRING);
        sm2CertConfig.setValue(CarownerConfigCenterUtils.yeePaySecret);

        // load into sdk config
        List<YopCertConfig> isvPrivateKeys = new ArrayList<>();
        isvPrivateKeys.add(sm2CertConfig);
        yopAppConfig.setIsvPrivateKey(isvPrivateKeys);

        log.info("易宝支付loadAppConfig打印sm2CertConfig："+ JsonUtils.toJsonString(sm2CertConfig) + ",appKey打印：" +appKey);

        return yopAppConfig;
    }

}
