/*
 * 前置收银台
 * <p>前置收银台</p>
 *
 * OpenAPI spec version: 1.0
 *
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.ly.travel.car.carowner.integration.client.api.yeepay;


import com.ly.travel.car.carowner.integration.client.api.yeepay.request.GetcardbinRequest;
import com.ly.travel.car.carowner.integration.client.api.yeepay.response.GetcardbinResponse;
import com.yeepay.yop.sdk.exception.YopClientException;

public interface FrontcashierClient {

    /**
     * 银行卡bin识别
     * 银行卡bin识别
     * @return GetcardbinResponse
     * @throws YopClientException if fails to make API call
     */
    GetcardbinResponse getcardbin(GetcardbinRequest request) throws YopClientException;
    void shutdown();
}
