/*
 * 前置收银台
 * <p>前置收银台</p>
 *
 * OpenAPI spec version: 1.0
 *
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.ly.travel.car.carowner.integration.client.api.yeepay;


import com.yeepay.yop.sdk.auth.req.AuthorizationReqRegistry;
import com.yeepay.yop.sdk.auth.req.DefaultAuthorizationReqRegistry;
import com.yeepay.yop.sdk.client.AbstractServiceClientBuilder;
import com.yeepay.yop.sdk.client.ClientParams;

public class FrontcashierClientBuilder extends AbstractServiceClientBuilder<FrontcashierClientBuilder, FrontcashierClientImpl>{

    private static final AuthorizationReqRegistry REGISTRY;

    static {
        REGISTRY = new DefaultAuthorizationReqRegistry();
        REGISTRY.register("getcardbin", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
    }

    @Override
    protected AuthorizationReqRegistry authorizationReqRegistry() {
        return REGISTRY;
    }

    @Override
    protected FrontcashierClientImpl build(ClientParams params) {
        return new FrontcashierClientImpl(params);
    }

    /**
     * @return Create new instance of builder with all defaults set.
     */
    public static FrontcashierClientBuilder builder(){
        return new FrontcashierClientBuilder();
    }

}
