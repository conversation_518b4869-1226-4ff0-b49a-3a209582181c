/*
 * 前置收银台
 * <p>前置收银台</p>
 *
 * OpenAPI spec version: 1.0
 *
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.ly.travel.car.carowner.integration.client.api.yeepay;

import com.ly.travel.car.carowner.integration.client.api.yeepay.request.GetcardbinRequest;
import com.ly.travel.car.carowner.integration.client.api.yeepay.request.GetcardbinRequestMarshaller;
import com.ly.travel.car.carowner.integration.client.api.yeepay.response.GetcardbinResponse;
import com.yeepay.yop.sdk.base.security.cert.parser.YopCertParserFactory;
import com.yeepay.yop.sdk.client.*;
import com.yeepay.yop.sdk.config.provider.file.YopCertConfig;
import com.yeepay.yop.sdk.exception.YopClientException;
import com.yeepay.yop.sdk.http.HttpResponseAnalyzerSupport;
import com.yeepay.yop.sdk.http.HttpResponseHandler;
import com.yeepay.yop.sdk.http.handler.DefaultHttpResponseHandler;
import com.yeepay.yop.sdk.model.transform.RequestMarshaller;
import com.yeepay.yop.sdk.security.CertTypeEnum;
import com.yeepay.yop.sdk.security.cert.YopCertCategory;

public class FrontcashierClientImpl implements FrontcashierClient {

    private final ClientHandler clientHandler;

    FrontcashierClientImpl(ClientParams clientParams) {
        this.clientHandler = new ClientHandlerImpl(new ClientHandlerParams().withClientParams(clientParams));
    }

    @Override
    public GetcardbinResponse getcardbin(GetcardbinRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<GetcardbinRequest> requestMarshaller = GetcardbinRequestMarshaller.getInstance();
        HttpResponseHandler<GetcardbinResponse> responseHandler =
                new DefaultHttpResponseHandler<GetcardbinResponse>(GetcardbinResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<GetcardbinRequest, GetcardbinResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }

    public static void main(String[] args) {
        YopCertParserFactory.getCertParser(YopCertCategory.PRIVATE, CertTypeEnum.SM2).parse(new YopCertConfig());
    }


    @Override
    public void shutdown() {
        clientHandler.shutdown();
    }

}
