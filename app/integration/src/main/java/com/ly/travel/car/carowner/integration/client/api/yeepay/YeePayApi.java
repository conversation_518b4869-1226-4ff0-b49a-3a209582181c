package com.ly.travel.car.carowner.integration.client.api.yeepay;

import com.alibaba.fastjson.JSON;
import com.ly.travel.car.carowner.common.enums.BankCardTypeEnum;
import com.ly.travel.car.carowner.common.utils.CarownerConfigCenterUtils;
import com.ly.travel.car.carowner.integration.client.api.yeepay.request.GetcardbinRequest;
import com.ly.travel.car.carowner.integration.client.api.yeepay.request.PayOrderRequest;
import com.ly.travel.car.carowner.integration.client.api.yeepay.response.BankCardRecognizeResp;
import com.ly.travel.car.carowner.integration.client.api.yeepay.response.GetcardbinRecognizeCardBinResponseDTOResult;
import com.ly.travel.car.carowner.integration.client.api.yeepay.response.GetcardbinResponse;
import com.ly.travel.car.carowner.integration.client.api.yeepay.response.PayOrderResponse;
import com.yeepay.g3.sdk.yop.utils.JsonUtils;
import com.yeepay.yop.sdk.auth.credentials.provider.YopCredentialsProviderRegistry;
import com.yeepay.yop.sdk.exception.YopServiceException;
import com.yeepay.yop.sdk.model.BaseRequest;
import com.yeepay.yop.sdk.model.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.function.Function;

@Component
@Slf4j
public class YeePayApi {
    public static AccountClient accountClient = null;

    static FrontcashierClient frontcashierClient = null;




    @PostConstruct
    public void init() {
        CustomFixedCredentialsProvider provider = new CustomFixedCredentialsProvider();
        YopCredentialsProviderRegistry.registerProvider(provider);
        accountClient = AccountClientBuilder.builder().build();
        frontcashierClient = FrontcashierClientBuilder.builder().build();
    }

    /**
     * 付款-下单
     * https://open.yeepay.com/docs/solutions/hlzxssfkjjfa/apis/others/options__rest__v1.0__account__pay__order
     *
     * @param request
     * @return
     */
    public PayOrderResponse payOrder(PayOrderRequest request) {
        return execute(request.getOperationId(), s -> accountClient.payOrder(request), request);
    }

    public GetcardbinResponse getCardBin(GetcardbinRequest request) {
        GetcardbinResponse resp = execute(request.getOperationId(), s -> frontcashierClient.getcardbin(request), request);
        return resp;
    }

    public static boolean isSuccess(String code) {
        return "NOP00000".equals(code);
    }

    public BankCardRecognizeResp recognizeBankCard(GetcardbinRequest request) {
        //调用易宝接口
        GetcardbinResponse carBinResp = getCardBin(request);
        if (carBinResp == null || carBinResp.getResult() == null) {
            throw new RuntimeException("识别银行卡，易宝返回空");
        }
        GetcardbinRecognizeCardBinResponseDTOResult result = carBinResp.getResult();
        if (!YeePayApi.isSuccess(result.getCode())) {
            throw new RuntimeException("银行卡识别，" + result.getMessage());
        }
        //赋值
        BankCardRecognizeResp resp = new BankCardRecognizeResp();
        resp.setBankCardType(result.getBankCardType());
        resp.setBankCardTypeName(BankCardTypeEnum.getDescByCode(result.getBankCardType()));
        resp.setBankName(result.getBankName());
        resp.setBankCardCode(result.getBankCode());
        return resp;
    }

    public boolean isPayOrderSuccess(String code) {
        return "UA00000".equals(code);
    }

    public <Req extends BaseRequest, Resp extends BaseResponse> Resp execute(String apiName, Function<Req, Resp> fun, Req req) {
        Resp resp = null;
        long startTime = System.currentTimeMillis();

        try {
            BaseRequest yeePayBaseReq = req;
            String yeePayKey = req.getHeaders().get("key");
            yeePayBaseReq.getRequestConfig().setAppKey(yeePayKey);
            resp = fun.apply(req);
            return resp;
        } catch (YopServiceException e) {
            log.error("易宝接口异常：", e);
        } catch (Exception e) {
            log.error("系统异常：", e);
            throw new RuntimeException("易宝系统错误");
        } finally {
            Map<String, String> map = new HashMap<>();
            map.put("Method", apiName);
            map.put("Request", JsonUtils.toJsonString(req));
            map.put("Response", JsonUtils.toJsonString(resp));
            map.put("Cost", String.valueOf(System.currentTimeMillis() - startTime));
            log.info("Api名称：" + apiName + "，请求和响应参数打印：" + JsonUtils.toJsonString(map));
        }
        return null;
    }

    public static void main(String[] args) {
        YeePayApi yeePayApi=new YeePayApi();
        yeePayApi.init();

                GetcardbinRequest getcardbinRequest=new GetcardbinRequest();
        getcardbinRequest.setBankCardNo("****************");
        getcardbinRequest.addHeader("key", "app_***********");

        GetcardbinResponse response=yeePayApi.getCardBin(getcardbinRequest);
        System.out.println(JSON.toJSONString(response));


        //"{\"requestConfig\":{\"appKey\":\"app_***********\",\"readTimeout\":0,\"connectTimeout\":0,\"encryptAlg\":\"SM4/CBC/PKCS5Padding\",\"encryptHeaders\":[],\"encryptParams\":[],\"signExpirationInSeconds\":0},\"headers\":{\"key\":\"app_***********\"},\"parentMerchantNo\":\"***********\",\"merchantNo\":\"***********\",\"requestNo\":\"TX68650380801AAU429U_2\",\"orderAmount\":6.49,\"receiveType\":\"REAL_TIME\",\"receiverAccountNo\":\"****************\",\"receiverAccountName\":\"周冠群\",\"receiverBankCode\":\"CMBCHINA\",\"bankAccountType\":\"CREDIT_CARD\",\"notifyUrl\":\"http://tcmobileapi.qa.17usoft.com/carownermobile/yeePay/test/callback\",\"operationId\":\"payOrder\"}","Method":"payOrder","Cost":"54"}
//        PayOrderRequest payOrderRequest=new PayOrderRequest();
//        payOrderRequest.addHeader("key", "app_***********");
//        payOrderRequest.setParentMerchantNo("***********");
//        payOrderRequest.setMerchantNo("***********");
//        payOrderRequest.setRequestNo(UUID.randomUUID().toString().replace("-",""));
//        payOrderRequest.setOrderAmount(new BigDecimal(1));
////        payOrderRequest.setFeeChargeSide();
//        payOrderRequest.setReceiveType("REAL_TIME");
//        payOrderRequest.setReceiverAccountNo("****************");
//        payOrderRequest.setReceiverAccountName("周冠群");
//        payOrderRequest.setReceiverBankCode("CMBCHINA");
//        payOrderRequest.setBankAccountType("CREDIT_CARD");
////        payOrderRequest.setBranchBankCode();
////        payOrderRequest.setComments();
////        payOrderRequest.setTerminalType();
//        payOrderRequest.setNotifyUrl("http://tcmobileapi.qa.17usoft.com/carownermobile/yeePay/test/callback\\");
//
//
//
//        PayOrderResponse payOrderResponse=yeePayApi.payOrder(payOrderRequest);
//        System.out.println(JSON.toJSONString(payOrderResponse));

    }
}
