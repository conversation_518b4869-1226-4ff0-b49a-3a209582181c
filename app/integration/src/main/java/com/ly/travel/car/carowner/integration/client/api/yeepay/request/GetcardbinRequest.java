/*
 * 前置收银台
 * <p>前置收银台</p>
 *
 * OpenAPI spec version: 1.0
 *
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.ly.travel.car.carowner.integration.client.api.yeepay.request;

import com.yeepay.yop.sdk.model.BaseRequest;


public class GetcardbinRequest extends BaseRequest {
    private static final long serialVersionUID = 1L;

    private String bankCardNo;

    private String appIndentify;


    /**
     * Get bankCardNo
     * @return bankCardNo
     **/

    public String getBankCardNo() {
        return bankCardNo;
    }

    public void setBankCardNo(String bankCardNo) {
        this.bankCardNo = bankCardNo;
    }

    public String getAppIndentify() {
        return appIndentify;
    }

    public void setAppIndentify(String appIndentify) {
        this.appIndentify = appIndentify;
    }

    @Override
    public String getOperationId() {
        return "getcardbin";
    }
}
