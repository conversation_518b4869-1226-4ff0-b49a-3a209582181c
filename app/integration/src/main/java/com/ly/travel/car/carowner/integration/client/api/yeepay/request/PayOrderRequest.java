/*
 * 账户
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 *
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.ly.travel.car.carowner.integration.client.api.yeepay.request;

import com.yeepay.yop.sdk.model.BaseRequest;

import java.math.BigDecimal;

public class PayOrderRequest extends BaseRequest {
    private static final long serialVersionUID = 1L;

    private String parentMerchantNo;

    private String merchantNo;

    private String requestNo;

    private BigDecimal orderAmount;

    private String feeChargeSide;

    /**
     * 到账类型
     * 可选项如下:
     * REAL_TIME:实时
     * TWO_HOUR:两小时到账
     * NEXT_DAY:次日到账（无特殊情况资金于次日上午7点左右到收款银行账户中）
     */
    private String receiveType = "REAL_TIME";

    private String receiverAccountNo;

    private String receiverAccountName;

    private String receiverBankCode;

    private String bankAccountType;

    private String branchBankCode;

    private String comments;

    private String terminalType;

    private String notifyUrl;

    private String remark;

    private String receiptComments;

    private String riskInfo;


    /**
     * Get parentMerchantNo
     *
     * @return parentMerchantNo
     **/

    public String getParentMerchantNo() {
        return parentMerchantNo;
    }

    public void setParentMerchantNo(String parentMerchantNo) {
        this.parentMerchantNo = parentMerchantNo;
    }

    /**
     * Get merchantNo
     *
     * @return merchantNo
     **/

    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    /**
     * Get requestNo
     *
     * @return requestNo
     **/

    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    /**
     * Get orderAmount
     *
     * @return orderAmount
     **/

    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    /**
     * Get feeChargeSide
     *
     * @return feeChargeSide
     **/

    public String getFeeChargeSide() {
        return feeChargeSide;
    }

    public void setFeeChargeSide(String feeChargeSide) {
        this.feeChargeSide = feeChargeSide;
    }

    /**
     * Get receiveType
     *
     * @return receiveType
     **/

    public String getReceiveType() {
        return receiveType;
    }

    public void setReceiveType(String receiveType) {
        this.receiveType = receiveType;
    }

    /**
     * Get receiverAccountNo
     *
     * @return receiverAccountNo
     **/

    public String getReceiverAccountNo() {
        return receiverAccountNo;
    }

    public void setReceiverAccountNo(String receiverAccountNo) {
        this.receiverAccountNo = receiverAccountNo;
    }

    /**
     * Get receiverAccountName
     *
     * @return receiverAccountName
     **/

    public String getReceiverAccountName() {
        return receiverAccountName;
    }

    public void setReceiverAccountName(String receiverAccountName) {
        this.receiverAccountName = receiverAccountName;
    }

    /**
     * Get receiverBankCode
     *
     * @return receiverBankCode
     **/

    public String getReceiverBankCode() {
        return receiverBankCode;
    }

    public void setReceiverBankCode(String receiverBankCode) {
        this.receiverBankCode = receiverBankCode;
    }

    /**
     * Get bankAccountType
     *
     * @return bankAccountType
     **/

    public String getBankAccountType() {
        return bankAccountType;
    }

    public void setBankAccountType(String bankAccountType) {
        this.bankAccountType = bankAccountType;
    }

    /**
     * Get branchBankCode
     *
     * @return branchBankCode
     **/

    public String getBranchBankCode() {
        return branchBankCode;
    }

    public void setBranchBankCode(String branchBankCode) {
        this.branchBankCode = branchBankCode;
    }

    /**
     * Get comments
     *
     * @return comments
     **/

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    /**
     * Get terminalType
     *
     * @return terminalType
     **/

    public String getTerminalType() {
        return terminalType;
    }

    public void setTerminalType(String terminalType) {
        this.terminalType = terminalType;
    }

    /**
     * Get notifyUrl
     *
     * @return notifyUrl
     **/

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    /**
     * Get remark
     *
     * @return remark
     **/

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * Get receiptComments
     *
     * @return receiptComments
     **/

    public String getReceiptComments() {
        return receiptComments;
    }

    public void setReceiptComments(String receiptComments) {
        this.receiptComments = receiptComments;
    }

    /**
     * Get riskInfo
     *
     * @return riskInfo
     **/

    public String getRiskInfo() {
        return riskInfo;
    }

    public void setRiskInfo(String riskInfo) {
        this.riskInfo = riskInfo;
    }

    @Override
    public String getOperationId() {
        return "payOrder";
    }
}
