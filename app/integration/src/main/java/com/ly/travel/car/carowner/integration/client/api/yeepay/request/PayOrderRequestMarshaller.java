/*
 * 账户
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 *
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.ly.travel.car.carowner.integration.client.api.yeepay.request;

import com.yeepay.yop.sdk.http.Headers;
import com.yeepay.yop.sdk.http.HttpMethodName;
import com.yeepay.yop.sdk.http.YopContentType;
import com.yeepay.yop.sdk.internal.DefaultRequest;
import com.yeepay.yop.sdk.internal.Request;
import com.yeepay.yop.sdk.model.transform.RequestMarshaller;
import com.yeepay.yop.sdk.protocol.marshaller.PrimitiveMarshallerUtils;

import java.util.Map;
import java.util.UUID;

public class PayOrderRequestMarshaller implements RequestMarshaller<PayOrderRequest> {
    private final String serviceName = "Account";
    private final String resourcePath = "/rest/v1.0/account/pay/order";
    private final String contentType = "application/x-www-form-urlencoded";
    private final HttpMethodName httpMethodName = HttpMethodName.POST;

    @Override
    public Request<PayOrderRequest> marshall(PayOrderRequest request) {
        Request<PayOrderRequest> internalRequest = new DefaultRequest<PayOrderRequest>(request, serviceName);
        internalRequest.setResourcePath(resourcePath);
        internalRequest.setHttpMethod(httpMethodName);
        if (!internalRequest.getHeaders().containsKey(Headers.YOP_REQUEST_ID)) {
            String requestId = UUID.randomUUID().toString();
            internalRequest.addHeader(Headers.YOP_REQUEST_ID, requestId);
        }
        Map<String, String> customerHeaders = request.getHeaders();
        if (customerHeaders != null) {
            for (String key : customerHeaders.keySet()) {
                internalRequest.addHeader(key, customerHeaders.get(key));
            }
        }
        if (request.getParentMerchantNo() != null) {
            internalRequest.addParameter("parentMerchantNo", PrimitiveMarshallerUtils.marshalling(request.getParentMerchantNo(), "String"));
        }
        if (request.getMerchantNo() != null) {
            internalRequest.addParameter("merchantNo", PrimitiveMarshallerUtils.marshalling(request.getMerchantNo(), "String"));
        }
        if (request.getRequestNo() != null) {
            internalRequest.addParameter("requestNo", PrimitiveMarshallerUtils.marshalling(request.getRequestNo(), "String"));
        }
        if (request.getOrderAmount() != null) {
            internalRequest.addParameter("orderAmount", PrimitiveMarshallerUtils.marshalling(request.getOrderAmount(), "BigDecimal"));
        }
        if (request.getFeeChargeSide() != null) {
            internalRequest.addParameter("feeChargeSide", PrimitiveMarshallerUtils.marshalling(request.getFeeChargeSide(), "String"));
        }
        if (request.getReceiveType() != null) {
            internalRequest.addParameter("receiveType", PrimitiveMarshallerUtils.marshalling(request.getReceiveType(), "String"));
        }
        if (request.getReceiverAccountNo() != null) {
            internalRequest.addParameter("receiverAccountNo", PrimitiveMarshallerUtils.marshalling(request.getReceiverAccountNo(), "String"));
        }
        if (request.getReceiverAccountName() != null) {
            internalRequest.addParameter("receiverAccountName", PrimitiveMarshallerUtils.marshalling(request.getReceiverAccountName(), "String"));
        }
        if (request.getReceiverBankCode() != null) {
            internalRequest.addParameter("receiverBankCode", PrimitiveMarshallerUtils.marshalling(request.getReceiverBankCode(), "String"));
        }
        if (request.getBankAccountType() != null) {
            internalRequest.addParameter("bankAccountType", PrimitiveMarshallerUtils.marshalling(request.getBankAccountType(), "String"));
        }
        if (request.getBranchBankCode() != null) {
            internalRequest.addParameter("branchBankCode", PrimitiveMarshallerUtils.marshalling(request.getBranchBankCode(), "String"));
        }
        if (request.getComments() != null) {
            internalRequest.addParameter("comments", PrimitiveMarshallerUtils.marshalling(request.getComments(), "String"));
        }
        if (request.getTerminalType() != null) {
            internalRequest.addParameter("terminalType", PrimitiveMarshallerUtils.marshalling(request.getTerminalType(), "String"));
        }
        if (request.getNotifyUrl() != null) {
            internalRequest.addParameter("notifyUrl", PrimitiveMarshallerUtils.marshalling(request.getNotifyUrl(), "String"));
        }
        if (request.getRemark() != null) {
            internalRequest.addParameter("remark", PrimitiveMarshallerUtils.marshalling(request.getRemark(), "String"));
        }
        if (request.getReceiptComments() != null) {
            internalRequest.addParameter("receiptComments", PrimitiveMarshallerUtils.marshalling(request.getReceiptComments(), "String"));
        }
        if (request.getRiskInfo() != null) {
            internalRequest.addParameter("riskInfo", PrimitiveMarshallerUtils.marshalling(request.getRiskInfo(), "String"));
        }
        internalRequest.setContentType(YopContentType.FORM_URL_ENCODE);
        internalRequest.addHeader(Headers.CONTENT_TYPE, internalRequest.getContentType().getValue());
        return internalRequest;
    }

    private static class CacheInstanceHolder {
        public static PayOrderRequestMarshaller INSTANCE = new PayOrderRequestMarshaller();

        private CacheInstanceHolder() {
        }
    }

    public static PayOrderRequestMarshaller getInstance() {
        return CacheInstanceHolder.INSTANCE;
    }
}
