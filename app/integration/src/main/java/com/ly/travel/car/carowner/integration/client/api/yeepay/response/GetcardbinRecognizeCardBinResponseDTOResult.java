/*
 * 前置收银台
 * <p>前置收银台</p>
 *
 * OpenAPI spec version: 1.0
 *
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.ly.travel.car.carowner.integration.client.api.yeepay.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.apache.commons.lang3.ObjectUtils;

import java.io.Serializable;

/**
 * GetcardbinRecognizeCardBinResponseDTOResult
 */
public class GetcardbinRecognizeCardBinResponseDTOResult implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 返回码
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * 返回信息
   */
  @JsonProperty("message")
  private String message = null;

  /**
   * 银行卡类型
   */
  @JsonProperty("bankCardType")
  private String bankCardType = null;

  /**
   * 银行名称
   */
  @JsonProperty("bankName")
  private String bankName = null;

  /**
   * 银行编码
   */
  @JsonProperty("bankCode")
  private String bankCode = null;

  public GetcardbinRecognizeCardBinResponseDTOResult code(String code) {
    this.code = code;
    return this;
  }

   /**
   * 返回码
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public GetcardbinRecognizeCardBinResponseDTOResult message(String message) {
    this.message = message;
    return this;
  }

   /**
   * 返回信息
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public GetcardbinRecognizeCardBinResponseDTOResult bankCardType(String bankCardType) {
    this.bankCardType = bankCardType;
    return this;
  }

   /**
   * 银行卡类型
   * @return bankCardType
  **/

  public String getBankCardType() {
    return bankCardType;
  }

  public void setBankCardType(String bankCardType) {
    this.bankCardType = bankCardType;
  }

  public GetcardbinRecognizeCardBinResponseDTOResult bankName(String bankName) {
    this.bankName = bankName;
    return this;
  }

   /**
   * 银行名称
   * @return bankName
  **/

  public String getBankName() {
    return bankName;
  }

  public void setBankName(String bankName) {
    this.bankName = bankName;
  }

  public GetcardbinRecognizeCardBinResponseDTOResult bankCode(String bankCode) {
    this.bankCode = bankCode;
    return this;
  }

   /**
   * 银行编码
   * @return bankCode
  **/

  public String getBankCode() {
    return bankCode;
  }

  public void setBankCode(String bankCode) {
    this.bankCode = bankCode;
  }


  @Override
  public boolean equals(Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    GetcardbinRecognizeCardBinResponseDTOResult getcardbinRecognizeCardBinResponseDTOResult = (GetcardbinRecognizeCardBinResponseDTOResult) o;
    return ObjectUtils.equals(this.code, getcardbinRecognizeCardBinResponseDTOResult.code) &&
    ObjectUtils.equals(this.message, getcardbinRecognizeCardBinResponseDTOResult.message) &&
    ObjectUtils.equals(this.bankCardType, getcardbinRecognizeCardBinResponseDTOResult.bankCardType) &&
    ObjectUtils.equals(this.bankName, getcardbinRecognizeCardBinResponseDTOResult.bankName) &&
    ObjectUtils.equals(this.bankCode, getcardbinRecognizeCardBinResponseDTOResult.bankCode);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, message, bankCardType, bankName, bankCode);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class GetcardbinRecognizeCardBinResponseDTOResult {\n");

    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    bankCardType: ").append(toIndentedString(bankCardType)).append("\n");
    sb.append("    bankName: ").append(toIndentedString(bankName)).append("\n");
    sb.append("    bankCode: ").append(toIndentedString(bankCode)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

