/*
 * 账户
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 *
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.ly.travel.car.carowner.integration.client.api.yeepay.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.apache.commons.lang3.ObjectUtils;

import java.io.Serializable;

/**
 * PayOrderRemitRespDTOResult
 */
public class PayOrderRemitRespDTOResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 返回码
     */
    @JsonProperty("returnCode")
    private String returnCode = null;

    /**
     * 返回信息
     */
    @JsonProperty("returnMsg")
    private String returnMsg = null;

    /**
     * 商户请求号
     */
    @JsonProperty("requestNo")
    private String requestNo = null;

    /**
     * 易宝付款订单号
     */
    @JsonProperty("orderNo")
    private String orderNo = null;

    /**
     * 订单状态
     */
    @JsonProperty("status")
    private String status = null;

    public PayOrderRemitRespDTOResult returnCode(String returnCode) {
        this.returnCode = returnCode;
        return this;
    }

    /**
     * 返回码
     *
     * @return returnCode
     **/

    public String getReturnCode() {
        return returnCode;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode;
    }

    public PayOrderRemitRespDTOResult returnMsg(String returnMsg) {
        this.returnMsg = returnMsg;
        return this;
    }

    /**
     * 返回信息
     *
     * @return returnMsg
     **/

    public String getReturnMsg() {
        return returnMsg;
    }

    public void setReturnMsg(String returnMsg) {
        this.returnMsg = returnMsg;
    }

    public PayOrderRemitRespDTOResult requestNo(String requestNo) {
        this.requestNo = requestNo;
        return this;
    }

    /**
     * 商户请求号
     *
     * @return requestNo
     **/

    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    public PayOrderRemitRespDTOResult orderNo(String orderNo) {
        this.orderNo = orderNo;
        return this;
    }

    /**
     * 易宝付款订单号
     *
     * @return orderNo
     **/

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public PayOrderRemitRespDTOResult status(String status) {
        this.status = status;
        return this;
    }

    /**
     * 订单状态
     *
     * @return status
     **/

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        PayOrderRemitRespDTOResult payOrderRemitRespDTOResult = (PayOrderRemitRespDTOResult) o;
        return ObjectUtils.equals(this.returnCode, payOrderRemitRespDTOResult.returnCode) &&
                ObjectUtils.equals(this.returnMsg, payOrderRemitRespDTOResult.returnMsg) &&
                ObjectUtils.equals(this.requestNo, payOrderRemitRespDTOResult.requestNo) &&
                ObjectUtils.equals(this.orderNo, payOrderRemitRespDTOResult.orderNo) &&
                ObjectUtils.equals(this.status, payOrderRemitRespDTOResult.status);
    }

    @Override
    public int hashCode() {
        return ObjectUtils.hashCodeMulti(returnCode, returnMsg, requestNo, orderNo, status);
    }


    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class PayOrderRemitRespDTOResult {\n");

        sb.append("    returnCode: ").append(toIndentedString(returnCode)).append("\n");
        sb.append("    returnMsg: ").append(toIndentedString(returnMsg)).append("\n");
        sb.append("    requestNo: ").append(toIndentedString(requestNo)).append("\n");
        sb.append("    orderNo: ").append(toIndentedString(orderNo)).append("\n");
        sb.append("    status: ").append(toIndentedString(status)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }
}

