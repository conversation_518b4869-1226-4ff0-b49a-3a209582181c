/*
 * 账户
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 *
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.ly.travel.car.carowner.integration.client.api.yeepay.response;

import com.yeepay.yop.sdk.model.BaseResponse;

public class PayOrderResponse extends BaseResponse {
    private static final long serialVersionUID = 1L;

    private PayOrderRemitRespDTOResult result;

    public PayOrderRemitRespDTOResult getResult() {
        return result;
    }

    public void setResult(PayOrderRemitRespDTOResult result) {
        this.result = result;
    }
}
