package com.ly.travel.car.carowner.integration.es;

import java.util.List;

/**
 * <AUTHOR>
 * @version EsClient, 2025/6/12 16:35
 */
public interface EsClient {

    /**
     * 搜索模块接口
     *
     * @param index       索引
     * @param tempName    模板名称
     * @param temps       模板参数
     * @param token       模板参数
     * @return the string
     */
    <T> String search(String index, String tempName, T temps, String token);

    <T> String search(String index, String tempName, String tempVersion, T temps, String token);

    /**
     * 保存模块接口
     *
     * @param index 索引
     * @param models 保存模型
     * @return 保存结果
     */
    <T> boolean save(String index, List<T> models, String token);
}
