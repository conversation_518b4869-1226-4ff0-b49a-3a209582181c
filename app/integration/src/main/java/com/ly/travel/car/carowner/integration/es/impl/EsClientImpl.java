package com.ly.travel.car.carowner.integration.es.impl;

import com.google.common.collect.Maps;
import com.ly.sof.utils.log.LogContextUtils;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.carowner.common.utils.CarownerConfigCenterUtils;
import com.ly.travel.car.carowner.common.utils.HttpUtils;
import com.ly.travel.car.carowner.integration.es.EsClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 * @version EsClientImpl, 2025/6/12 17:26
 */
@Slf4j
@Service("esClient")
public class EsClientImpl implements EsClient {

    private final static String ES_HOST          = "http://es.dss.17usoft.com";
    private final static String VERSION_LATEST   = "latest";
    private final static String HTTP_AUTH_HEADER = "Authentication";

    public String queryConfigVersion(String template){
        if(CarownerConfigCenterUtils.esQueryTemplateList!=null){
            return CarownerConfigCenterUtils.esQueryTemplateList.getOrDefault(template,VERSION_LATEST);
        }
        return VERSION_LATEST;
    }

    public <T> String search(String index, String tempName, T temps, String token) {
        return search(index, tempName, queryConfigVersion(tempName), temps, token);
    }

    @Override
    public <T> String search(String index, String tempName,String tempVersion, T temps, String token) {
        LogContextUtils.setCategory("EsClientImpl");
        LogContextUtils.setSubCategory("search");
        String result = "";
        String url = getSearchEsUrl(ES_HOST, index, tempVersion, tempName);
        LoggerUtils.info(log, "开始查询es...模板名称 tempName:{} , 查询入参 temps:{} ,请求的url:{}", tempName, FastJsonUtils.toJSONString(temps), url);
        try {
            Map<String, String> headerMap = Maps.newHashMap();
            headerMap.put(HTTP_AUTH_HEADER, token);
            Map<String, String> paramMap = Maps.newHashMap();
            paramMap.put("ignore_unavailable", "true");
            result = HttpUtils.postJson(url, FastJsonUtils.toJSONString(temps), headerMap, paramMap, 3000, TimeUnit.MILLISECONDS);
            LoggerUtils.info(log, "es查询结果 result:{}", result);
            return result;
        } catch (Exception e) {
            LoggerUtils.error(log, "requestUrl=" + url + " \n 参数=" + FastJsonUtils.toJSONString(temps) + "\n response=" + result, e);
            return null;
        } finally {
            LoggerUtils.info(log, "requestUrl=" + url + " \n 参数=" + FastJsonUtils.toJSONString(temps) + "\n response=" + result);
        }
    }


    @Override
    public <T> boolean save(String index, List<T> models, String token) {
        return saveEs(ES_HOST, index, token, models);
    }


    /**
     * 保存
     *
     * @param host host
     * @param index 索引
     * @param token token
     * @param models
     * @return 保存结果
     */
    protected <T> boolean saveEs(String host, String index, String token, List<T> models) {
        String url = getSaveUrl(host, index);
        try {
            Map<String, String> headerMap = Maps.newHashMap();
            headerMap.put(HTTP_AUTH_HEADER, token);
            String req = FastJsonUtils.toJSONString(models);
            String result = HttpUtils.postJson(url, models, headerMap, 3000, TimeUnit.MILLISECONDS);
            LoggerUtils.info(log, "[save] 保存ES完成 req:{}, res:{}", req, result);
            return true;
        } catch (Exception e) {
            LoggerUtils.error(log, "[save] 保存ES失败:{}", e, e.getMessage());
            return false;
        }
    }

    private String getSearchEsUrl(String host, String index, String tempVersion, String tempName) {
        // ?ignore_unavailable=true 自动忽略不可用索引,避免查询报错.
        return host + "/index/" + index + "/template/" + tempName + "/" + tempVersion + "/search";
    }

    /**
     * http://{host}/index/{index}/type/info/bulk
     * @param host host
     * @param index 索引
     * @return url
     */
    private String getSaveUrl(String host, String index) {
        return host + "/index/" + index + "/type/info/bulk/sync";
    }
}
