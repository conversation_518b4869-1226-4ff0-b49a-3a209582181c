package com.ly.travel.car.carowner.integration.es.model;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version OrderListESSearchRequest, 2025/6/12 16:36
 */
@Data
public class OrderListESSearchRequest implements Serializable {

    /** 出发城市 */
    private Integer startCityId;

    /**
     * 距离 单位km
     */
    private String  distance;

    /** 到达城市id */
    private Integer endCityId;

    /**
     * 坐标点（维度,经度）
     */
    private String  location;

    /**
     * 出发地坐标（维度,经度）
     */
    private String  locationStart;

    /**
     * 出发地坐标距离 km
     */
    private String  distanceStart;

    /**
     * 到达地坐标（维度,经度）
     */
    private String  locationEnd;

    /**
     * 目的地坐标距离 km
     */
    private String  distanceEnd;

    private Long    gmtUsageStart;

    private Long    gmtUsageEnd;

    private Long    gmtUsageDelayStart;

    private Long    gmtUsageDelayEnd;

    private Integer orderType;

    /** 生产:prod，预发:stage，测试:qa，dev测试:test，集成测试:uat */
    private String  env;

    /**
     * 当前页码
     */
    private int     from;

    /**
     * 页容量
     */
    private int     pageSize;
}
