package com.ly.travel.car.carowner.integration.es.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version OrderListESSearchResponse, 2025/6/12 17:42
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderListESSearchResponse {

    /** 查询记录数量 */
    private Long                         count;

    private List<OrderSearchESDetailDTO> list;

    @Data
    public static class OrderSearchESDetailDTO {
        private String       ref_id;
        private String       distributor_order_no;
        private int          passenger_count;
        private String       start_city_id;
        private String       source;
        private String       order_commission_cap;
        private String       real_telephone;
        private String       start_district_code;
        private long         id;
        private String       start_city;
        private String       end_city;
        private String       starting_add;
        private Integer      order_type;
        private String       starting_lat;
        private String       order_commission_ratio;
        private List<String> ending_location;
        private long         create_time;
        private String       ending_add_detail;
        private String       telephone;
        private String       distributor_main_order_no;
        private String       pay_time;
        private String       create_user;
        private Integer      status;
        private String       order_no;
        private String       distance;
        private List<String> starting_location;
        private String       start_district;
        private String       remark;
        private Integer      duration;
        private Integer      pay_status;

        private long         update_time;
        private String       update_user;
        private String       starting_add_detail;
        private String       ending_lat;
        private long         starting_time;
        private Integer      pay_type;
        private String       end_district;
        private String       ending_add;
        private long         last_starting_time;
        private Integer      commission_type;
        private long         member_id;
        private String       ending_lon;
        private String       amount;
        private String       pay_price;
        private String       end_district_code;
        private String       env;
        private String      end_city_id;
        private String       starting_lon;
    }
}
