package com.ly.travel.car.carowner.integration.es.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version RideOrderListEsSearchResponse, 2025/6/19 12:32
 */
@Data
public class RideOrderListEsSearchResponse {

    /** 查询记录数量 */
    private Long                                                       count;

    private List<OrderSearchESDetailDTO> list;

    @Data
    public static class OrderSearchESDetailDTO {

        /**
         * 自增id        db_column: id
         */
        private long   id;
        /**
         * 环境        db_column: env
         */

        @JSONField(name = "env")
        private String env;
        /**
         * 订单号        db_column: order_no
         */

        @JSONField(name = "order_no")
        private String orderNo;
        /**
         * 供应链订单号        db_column: distributor_order_no
         */

        @JSONField(name = "distributor_order_no")
        private String distributorOrderNo;
        /**
         * 交易主订单号        db_column: distributor_main_order_no
         */

        @JSONField(name = "distributor_main_order_no")
        private String distributorMainOrderNo;
        /**
         * 司机行程号        db_column: driver_trip_no
         */

        @JSONField(name = "driver_trip_no")
        private String driverTripNo;
        /**
         * 订单来源  0:未知 1:C端        db_column: source
         */

        @JSONField(name = "source")
        private int    source;
        /**
         * 顺路度        db_column: hitch_percent
         */

        @JSONField(name = "hitch_percent")
        private int    hitchPercent;
        /**
         * 出发城市id        db_column: start_city_id
         */

        @JSONField(name = "start_city_id")
        private long   startCityId;
        /**
         * 到达城市id        db_column: end_city_id
         */

        @JSONField(name = "end_city_id")
        private long   endCityId;
        /**
         * 出发城市        db_column: start_city
         */

        @JSONField(name = "start_city")
        private String startCity;
        /**
         * 到达城市        db_column: end_city
         */

        @JSONField(name = "end_city")
        private String endCity;
        /**
         * 出发城市行政区code        db_column: start_district_code
         */

        @JSONField(name = "start_district_code")
        private String startDistrictCode;
        /**
         * 出发城市行政区        db_column: start_district
         */

        @JSONField(name = "start_district")
        private String startDistrict;
        /**
         * 目的城市行政区code        db_column: end_district_code
         */
        private String end_district_code;
        /**
         * 目的城市行政区        db_column: end_district
         */
        private String end_district;
        /**
         * 出发地点        db_column: starting_add
         */
        private String starting_add;
        /**
         * 目的地点        db_column: ending_add
         */
        private String ending_add;
        /**
         * 出发点经度        db_column: starting_lon
         */
        private double starting_lon;
        /**
         * 出发点纬度        db_column: starting_lat
         */
        private double starting_lat;
        /**
         * 目的点经度        db_column: ending_lon
         */
        private double ending_lon;
        /**
         * 目的点纬度        db_column: ending_lat
         */
        private double ending_lat;
        /**
         * 用车时间        db_column: starting_time
         */
        private long   starting_time;
        /**
         * 最晚用车时间        db_column: last_starting_time
         */
        private long   last_starting_time;
        /**
         * 乘客联系电号        db_column: telephone
         */
        private String telephone;
        /**
         * 真实号码        db_column: real_telephone
         */
        private String real_telephone;
        /**
         * 数量        db_column: passenger_count
         */
        private int    passenger_count;
        /**
         * 订单金额        db_column: amount
         */
        private double amount;
        /**
         * 付款金额        db_column: pay_price
         */
        private double pay_price;
        /**
         * 订单状态   -1已删除     1.已派车   2已完成   3已取消        db_column: status
         */
        private int    status;
        /**
         * 支付状态   0未支付   1 已支付  2部分支付  3已退款  4部分退款        db_column: pay_status
         */
        private int    pay_status;
        /**
         * 支付类型   1普通  2信用分        db_column: pay_type
         */
        private int    pay_type;
        /**
         * refId        db_column: ref_id
         */
        private String ref_id;
        /**
         * 订单类型：1、包车； 2、拼车；        db_column: order_type
         */
        private int    order_type;
        /**
         * 用户id        db_column: member_id
         */
        private long   member_id;
        /**
         * 车牌        db_column: vehicle_no
         */
        private String vehicle_no;
        /**
         * 司机姓名        db_column: driver_name
         */
        private String driver_name;
        /**
         * 司机电话        db_column: driver_mobile
         */
        private String driver_mobile;
        /**
         * 司机id        db_column: driver_id
         */
        private long   driver_id;
        /**
         * 下单时间        db_column: book_time
         */
        private Date   book_time;
        /**
         * 创建人        db_column: create_user
         */
        private String create_user;
        /**
         * 创建时间        db_column: create_time
         */
        private Date   create_time;
        /**
         * 修改人        db_column: update_user
         */
        private String update_user;
        /**
         * 修改时间        db_column: update_time
         */
        private long   update_time;
        /**
         * 备注        db_column: remark
         */
        private String remark;
        /**
         * 出发详细地址        db_column: starting_add_detail
         */
        private String starting_add_detail;
        /**
         * 到达详细地址        db_column: ending_add_detail
         */
        private String ending_add_detail;
        /**
         * 总里程数(单位：千米）        db_column: distance
         */
        private double distance;
        /**
         * 时长，分钟单位        db_column: duration
         */
        private int    duration;
        /**
         * 随单分佣比例0-1之间        db_column: order_commission_ratio
         */
        private double order_commission_ratio;
        /**
         * 随单分佣上限(单位元)        db_column: order_commission_cap
         */
        private double order_commission_cap;
        /**
         * 支付时间        db_column: pay_time
         */
        private long   pay_time;
    }
}
