package com.ly.travel.car.carowner.integration.exception;

import com.ly.sof.api.error.LYError;

/**
 * 请求二方熔断异常,方便业务特殊处理
 */
public class IntegrationDegradedException extends IntegrationException {

    private static final long serialVersionUID = -4894232616426212422L;

    /**
     * Instantiates a new Integration timeout exception.
     *
     * @param error the error
     * @param cause the cause
     */
    public IntegrationDegradedException(LYError error, Throwable cause) {
        super(error, cause);
    }

    /**
     * Instantiates a new Integration timeout exception.
     *
     * @param error the error
     */
    public IntegrationDegradedException(LYError error) {
        super(error);
    }

    /**
     * Instantiates a new Integration timeout exception.
     *
     * @param cause the cause
     */
    public IntegrationDegradedException(Throwable cause) {
        super(cause);
    }
}
