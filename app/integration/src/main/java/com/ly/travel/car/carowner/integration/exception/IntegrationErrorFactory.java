/*
 * LY.com Inc.
 * Copyright (c) 2004-2016 All Rights Reserved.
 */

package com.ly.travel.car.carowner.integration.exception;

import com.ly.sof.api.error.AbstractErrorFactory;
import com.ly.sof.api.error.LYError;

/**
 * 搜索核心Integration模块错误工厂
 *
 * <AUTHOR>
 * @version $Id : IntegrationErrorFactory.java, v 0.1 2017年2月26日 下午2:45:52 kim.mrzhangl Exp $
 */
public class IntegrationErrorFactory extends AbstractErrorFactory {

    /**
     * Gets instance.
     *
     * @return the instance
     */
    public static IntegrationErrorFactory getInstance() {
        return IntegrationErrorFactoryHolder.INSTANCE;
    }

    @Override
    protected String provideErrorBundleName() {
        return "shared-mobility-pricing-rule-integration";
    }

    /**
     * integrationError
     *
     * @param module     the module
     * @param category   the category
     * @param traceId    the trace id
     * @param innerError the inner error
     * @return LYError ly error
     */
    public LYError integrationError(String module, String category, String traceId, String innerError) {
        return createError("LY0521001001", module, category, traceId, innerError);
    }

    /**
     * 供应链询价无结果
     * 将多个供应商无结果错误统一归类,特殊错误特殊处理
     *
     * @return ly error
     */
    public LYError integrationNoResourceError(String code, String message) {
        return createError("LY0521001002", code, message);
    }

    /**
     * 围栏校验异常
     *
     * @param message the message
     * @return the ly error
     */
    public LYError integrationBarrierError(String message) {
        return createError("LY0521001010", message);
    }

    /**
     * 单例实现
     * IntegrationErrorFactory instance keeper
     *
     * <AUTHOR>
     * @version $Id : IntegrationErrorFactoryHolder.java, v 0.1 2016年4月10日 下午2:53:49 allen Exp $
     */
    private static final class IntegrationErrorFactoryHolder {
        /**
         * instance
         */
        private static final IntegrationErrorFactory INSTANCE = new IntegrationErrorFactory();
    }
}
