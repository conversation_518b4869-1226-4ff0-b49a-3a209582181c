/*
 * LY.com Inc.
 * Copyright (c) 2004-2016 All Rights Reserved.
 */
package com.ly.travel.car.carowner.integration.exception;

import com.ly.sof.api.error.LYError;
import com.ly.sof.api.exception.LYException;

/**
 * Integration Exception
 *
 * <AUTHOR>
 * @version $Id : IntegrationException.java, v 0.1 2017年2月26日 下午2:45:24 kim.mrzhangl Exp $
 */
public class IntegrationException extends LYException {

    /**
     * The constant serialVersionUID.
     */
    private static final long serialVersionUID = 90909158194467892L;

    /**
     * Instantiates a new Integration exception.
     *
     * @param error the error
     * @param cause the cause
     */
    public IntegrationException(LYError error, Throwable cause) {
        super(error, cause);
    }

    /**
     * Instantiates a new Integration exception.
     *
     * @param error the error
     */
    public IntegrationException(LYError error) {
        super(error);
    }

    /**
     * Instantiates a new Integration exception.
     *
     * @param cause the cause
     */
    public IntegrationException(Throwable cause) {
        super(cause);
    }
}
