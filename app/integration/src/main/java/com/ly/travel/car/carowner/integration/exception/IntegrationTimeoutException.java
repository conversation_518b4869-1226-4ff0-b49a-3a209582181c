package com.ly.travel.car.carowner.integration.exception;

import com.ly.sof.api.error.LYError;

/**
 * 请求二方超时异常,需要单独识别此异常,方便业务进行补偿.
 *
 * <AUTHOR>
 * @version Id : IntegrationTimeoutException, v 0.1 2021/5/13 17:32 kim.zhangl Exp $
 */
public class IntegrationTimeoutException extends IntegrationException {
    private static final long serialVersionUID = -1629812796954302833L;

    /**
     * Instantiates a new Integration timeout exception.
     *
     * @param error the error
     * @param cause the cause
     */
    public IntegrationTimeoutException(LYError error, Throwable cause) {
        super(error, cause);
    }

    /**
     * Instantiates a new Integration timeout exception.
     *
     * @param error the error
     */
    public IntegrationTimeoutException(LYError error) {
        super(error);
    }

    /**
     * Instantiates a new Integration timeout exception.
     *
     * @param cause the cause
     */
    public IntegrationTimeoutException(Throwable cause) {
        super(cause);
    }
}
