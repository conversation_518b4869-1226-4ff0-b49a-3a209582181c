package com.ly.travel.car.carowner.integration.pushmsg;


import com.ly.travel.car.carowner.integration.sms.BaseSmsTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

public abstract class BaseWxTemplate {

    public BaseWxTemplate(String name, String templateId) {

        this.name = name;
        this.templateId = templateId;
    }
    public BaseWxTemplate(String name, String templateId,BaseSmsTemplate smsTemplate) {

        this.name = name;
        this.templateId = templateId;
        this.smsTemplate=smsTemplate;
    }

    public String getToUser() {
        return toUser;
    }

    public <T extends BaseWxTemplate> T setToUser(String toUser) {
        this.toUser = toUser;
        return (T)this;
    }
    public String getPage() {
        return page;
    }

    <T extends BaseWxTemplate> T setPage(String page) {
        this.page = page;
        return (T)this;
    }
    private BaseSmsTemplate smsTemplate;
    private String toUser;
    private String name;
    private String page;

    public BaseSmsTemplate getSmsTemplate() {
        return smsTemplate;
    }

    void setSmsTemplate(BaseSmsTemplate smsTemplate) {
        this.smsTemplate = smsTemplate;
    }

    public String getName() {
        return name;
    }

    void setName(String name) {
        this.name = name;
    }

    String getTemplateId() {
        return templateId;
    }

    void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    private String templateId;
    private Map<String,String> params;


    Map<String,String> getParams() {
        return this.params;
    }

    <T extends BaseWxTemplate> T putParam(String name, String value ) {
        if(this.params==null){
            this.params=new HashMap<>();
        }
        this.params.put(name,value);
        return (T)this;
    }

}
