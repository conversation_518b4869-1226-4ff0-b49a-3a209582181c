package com.ly.travel.car.carowner.integration.pushmsg;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaUniformMessage;
import cn.binarywang.wx.miniapp.bean.scheme.WxMaGenerateSchemeRequest;
import com.ly.travel.car.carowner.common.cache.CacheUtils;
import com.ly.travel.car.carowner.common.enums.CacheKeyEnum;
import com.ly.travel.car.carowner.common.thread.MTThreadPoolEnum;
import com.ly.travel.car.carowner.common.thread.MTThreadPoolExecutors;
import com.ly.travel.car.carowner.common.utils.CarownerConfigCenterUtils;
import com.ly.travel.car.carowner.common.utils.EnvUtils;
import com.ly.travel.car.carowner.common.utils.ShortUrlUtil;
import com.ly.travel.car.carowner.integration.pushmsg.BaseWxTemplate;
import com.ly.travel.car.carowner.integration.sms.BaseSmsTemplate;
import com.ly.travel.car.carowner.integration.sms.SmsComponent;
import com.ly.travel.car.carowner.integration.sms.SmsTemplatesDefinedList;
import com.ly.travel.pushcore.facade.model.request.PushParam;
import com.ly.travel.pushcore.facade.model.request.PushVO;
import com.ly.travel.pushcore.facade.request.PushRequest;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Component
public class PushComponent {
    private static final Logger logger = LoggerFactory.getLogger(PushComponent.class);
    public static final String USECAR_MDSFCCZD = "USECAR_MDSFCCZD";
    @Autowired(required = false)
    private WxMaService wxMaService;
    @Autowired
    PushDsfClient pushDsfClient;

    @Autowired
    private SmsComponent smsComponent;

    public void pushTemplate(BaseWxTemplate wxTemplate) {

        PushRequest request = getCommonBody(wxTemplate);
        pushDsfClient.sender(request, wxTemplate.getName());

    }

    private PushRequest getCommonBody(BaseWxTemplate wxTemplate) {
        PushRequest pushRequest = new PushRequest();

        pushRequest.setTraceId(UUID.randomUUID().toString());
        PushVO pushVO = new PushVO();
        pushVO.setScene(wxTemplate.getTemplateId());

        pushVO.setDomain(USECAR_MDSFCCZD);
//        pushVO.setUnionId("");
        pushVO.setChannel(10327);
        pushVO.setSkipBury(false);
        pushVO.setNotUsedScript(true);

        PushParam pushParam = new PushParam();
        pushVO.setSenderParam(pushParam);

        if(StringUtils.isNotBlank(wxTemplate.getToUser())) {
            pushVO.setOpenId(wxTemplate.getToUser());
            pushParam.setPagePath(wxTemplate.getPage());
            pushParam.setAppId(CarownerConfigCenterUtils.WXAPP_ID);
            pushParam.setWeChatParam(wxTemplate.getParams());
        }
        BaseSmsTemplate smsTemplate = wxTemplate.getSmsTemplate();
        if (smsTemplate != null) {
            if(smsTemplate instanceof SmsTemplatesDefinedList.TemplateWxPageAction) {
                ((SmsTemplatesDefinedList.TemplateWxPageAction) smsTemplate).loadWxPage(this::convertWxShortUrl);
            }
            pushParam.setSmsParam(smsTemplate.getParams());
            pushVO.setMobile(smsTemplate.getMobile());
        }
//        pushVO.setNotUsedScript(true);
        pushRequest.setPushVO(pushVO);
        return pushRequest;
    }


    public String convertWxShortUrl(String wxPage,boolean cache) {
        String url=null;
        String key= CacheKeyEnum.WX_JUM_PAGE.format(wxPage);
        key=key.replaceAll("/","_").replaceAll("\\\\","_");
        if(cache){
            url= CacheUtils.getCacheValue(key);
        }
        if(StringUtils.isNotBlank(url)){
            logger.info("从缓存中取得地址,{}",url);
            return url;
        }
        try {
            WxMaGenerateSchemeRequest.JumpWxa.JumpWxaBuilder jumpWxaBuilder=WxMaGenerateSchemeRequest.JumpWxa.newBuilder().envVersion(EnvUtils.getEnv().product()?"release":"trial");
            int index=wxPage.indexOf("?");
            if(index>0){
                jumpWxaBuilder.path(wxPage.substring(0,index)).query(wxPage.substring(index+1));
            }else{
                jumpWxaBuilder.path(wxPage);
            }
            WxMaGenerateSchemeRequest.JumpWxa jumpWxa = jumpWxaBuilder.build();
            //默认值"release"。要打开的小程序版本。正式版为"release"，体验版为"trial"，开发版为"develop"，
            WxMaGenerateSchemeRequest request = WxMaGenerateSchemeRequest.newBuilder().jumpWxa(jumpWxa).build();
            url = CarownerConfigCenterUtils.wxAppJumpUrl+ wxMaService.getWxMaSchemeService().generate(request);
            if(StringUtils.isBlank(url)){
                return url;
            }
            url = ShortUrlUtil.create(url);
            if(StringUtils.isBlank(url)){
                return url;
            }
            if(cache) {
                CacheUtils.setCacheValue(key, url, CacheKeyEnum.WX_JUM_PAGE.getExpire());
            }
        }catch (Exception ex){
            logger.error("获取微信小程序跳转地址出错,",ex);
        }
        return url;
    }


}
