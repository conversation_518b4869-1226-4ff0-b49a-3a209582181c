package com.ly.travel.car.carowner.integration.pushmsg;

import com.alibaba.fastjson.JSON;
import com.ly.spat.dsf.client.v2.DSFProxy;
import com.ly.spat.dsf.client.v2.HttpService;
import com.ly.travel.car.carowner.common.utils.CarownerConfigCenterUtils;
import com.ly.travel.pushcore.facade.request.PushRequest;
import com.ly.travel.pushcore.facade.response.PushResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
@Slf4j
@Component
public class PushDsfClient {

    private  HttpService getServiceClient() {
        return DSFProxy.newInstance().getService("dsf.travelsystem.sms.platform.core", "push", CarownerConfigCenterUtils.cx_push_dsf_version);
    }

    public  boolean sender(PushRequest request,String name) {
        String requestStr = JSON.toJSONString(request);
        log.info("出行推送sender {}: 请求参数:{}",name, requestStr);

        try {
            HttpService pushFacade = getServiceClient();
            String result = pushFacade.action("sender", String.class, requestStr, null, 2,2000, TimeUnit.MILLISECONDS);
            log.info("出行推送sender {}: 返回结果:{}",name, result);
            PushResponse pushResponse = JSON.parseObject(result, PushResponse.class);

            if (Objects.nonNull(pushResponse) && pushResponse.isSuccess()&&Objects.nonNull(pushResponse.getSendResult())&&pushResponse.getSendResult().isSuccess()) {
                return true;
            }
        } catch (Exception e) {
            log.error("出行推送sender {} 失败",name, e);
        }
        return false;
    }


}
