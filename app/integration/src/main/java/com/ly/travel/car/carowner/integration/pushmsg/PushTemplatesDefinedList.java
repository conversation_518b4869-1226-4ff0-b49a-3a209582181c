package com.ly.travel.car.carowner.integration.pushmsg;


import com.ly.travel.car.carowner.common.utils.DateUtil;
import com.ly.travel.car.carowner.integration.sms.BaseSmsTemplate;
import com.ly.travel.car.carowner.integration.sms.SmsTemplatesDefinedList;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.Date;

public class PushTemplatesDefinedList {


    public  final static CancelOrderTemplate  CANCEL_ORDER_TEMPLATE=new CancelOrderTemplate("取消订单推送","SCENE_003");
    public  final static BoratcastOrderTemplate BROASTCASE_TEMPLATE=new BoratcastOrderTemplate("司机行程新订单通知","SCENE_001");
    public  final static BoratcastLineOrderTemplate BROASTCASE_LINE_TEMPLATE=new BoratcastLineOrderTemplate("司机线路新订单通知","SCENE_001");
    public  final static CancelTripOrderTemplate CANCEL_TRIP_ORDER_TEMPLATE=new CancelTripOrderTemplate("行程取消通知","SCENE_004");
    public  final static InviteOrderTemplate INVITE_ORDER_TEMPLATE=new InviteOrderTemplate("邀请司机行程通知","SCENE_002");


    public static class CancelOrderTemplate extends BaseWxTemplate {

        public CancelOrderTemplate(String name, String templateId) {
            super(name, templateId);
        }

        public CancelOrderTemplate setTemplateParams(Date startTime,String startCity,String startDistrict, String startAddress,String endCity,String endDistrict, String arriveAddress, String orderNo, String driverTripNo,String orderMobile,String driverMobile) {


            CancelOrderTemplate baseSmsTemplate=new CancelOrderTemplate(getName(),getTemplateId());
            baseSmsTemplate.setSmsTemplate(SmsTemplatesDefinedList.CANCEL_TEMPLATE.setTemplateParams(startTime,orderNo,driverTripNo).setMobile(driverMobile));

            if(StringUtils.hasText(orderMobile)&&orderMobile.length()>2){
                orderMobile=orderMobile.substring(orderMobile.length()-2);
            }
            baseSmsTemplate.putParam("{{number4.DATA}}", orderMobile);
            baseSmsTemplate.putParam("{{time3.DATA}}", DateUtil.date2String(startTime, DateUtil.DATE_PATTERN_MONTH_DAY_HH_MM));
            String address=startCity+startDistrict+startAddress+"-"+endCity+endDistrict+arriveAddress;
            if(address.length()>20){
                address=address.substring(0,18)+"…";
            }
            baseSmsTemplate.putParam("{{thing2.DATA}}", address);
            baseSmsTemplate.putParam("{{const8.DATA}}", "乘客取消");

            baseSmsTemplate.setPage(String.format("/pages/mine/index", orderNo, driverTripNo));
            return baseSmsTemplate;
        }
    }

    public static class BoratcastOrderTemplate extends BaseWxTemplate {


        public BoratcastOrderTemplate(String name, String templateId) {
            super(name, templateId);
        }
        public BoratcastOrderTemplate setTemplateParams(String shunludu, Date startTime, String startCity, String startDistrict, String startAddress, String endCity, String endDistrict, String endAddress, String orderNo, String driverTripNo, String orderMobile, String driverMobile, int orderType, BigDecimal amount) {
            if(shunludu!=null&&shunludu.indexOf(".")>0) {
                shunludu=shunludu.substring(0, shunludu.indexOf("."));
            }
            BoratcastOrderTemplate baseSmsTemplate=new BoratcastOrderTemplate(getName(),getTemplateId());
            baseSmsTemplate.setSmsTemplate(SmsTemplatesDefinedList.BROASTCASE_TEMPLATE.setTemplateParams(shunludu,startTime,startCity,startDistrict,startAddress,endCity,endDistrict,endAddress,orderNo,driverTripNo).setMobile(driverMobile));

            if(StringUtils.hasText(orderMobile)&&orderMobile.length()>2){
                orderMobile=orderMobile.substring(orderMobile.length()-2);
            }
            String address=startCity+startDistrict+startAddress+"-"+endCity+endDistrict+endAddress;
            if(address.length()>20){
                address=address.substring(0,18)+"…";
            }
            baseSmsTemplate.putParam("{{thing3.DATA}}", address);
            baseSmsTemplate.putParam("{{time9.DATA}}", DateUtil.date2String(startTime, DateUtil.DATE_PATTERN_MONTH_DAY_HH_MM));
            baseSmsTemplate.putParam("{{character_string12.DATA}}", "**"+orderMobile);
            baseSmsTemplate.putParam("{{const14.DATA}}", orderType==1?"独享":"拼车"); //拼车 独享
            baseSmsTemplate.putParam("{{thing2.DATA}}", String.format("%s顺路，价格%s元，请查看",shunludu+"%",amount.toPlainString()));

            baseSmsTemplate.setPage(String.format("/pages/confirmTogether/index?orderSerialNo=%s&tripOrderSerialNo=%s", orderNo, driverTripNo));
            return baseSmsTemplate;
        }
    }

    public static class CancelTripOrderTemplate extends BaseWxTemplate {


        public CancelTripOrderTemplate(String name, String templateId) {
            super(name, templateId);
        }

        public CancelTripOrderTemplate setTemplateParams(Date startTime, String startCity, String startDistrict, String startAddress, String endCity, String endDistrict, String endAddress,  String driverTripNo, String driverMobile) {
            CancelTripOrderTemplate baseSmsTemplate=new CancelTripOrderTemplate(getName(),getTemplateId());
            baseSmsTemplate.setSmsTemplate(SmsTemplatesDefinedList.CANCEL_DRIVER_TRIP_TEMPLATE.setTemplateParams(startTime,startCity,startDistrict,startAddress,endCity,endDistrict,endAddress).setMobile(driverMobile));

            String address=startCity+startDistrict+startAddress+"-"+endCity+endDistrict+endAddress;
            if(address.length()>20){
                address=address.substring(0,18)+"…";
            }
            baseSmsTemplate.putParam("{{thing5.DATA}}", address);
            baseSmsTemplate.putParam("{{time3.DATA}}", DateUtil.date2String(startTime, DateUtil.DATE_PATTERN_MONTH_DAY_HH_MM));
            baseSmsTemplate.putParam("{{phrase4.DATA}}", "行程已过期");

//            baseSmsTemplate.setPage("/pages/index/index");
            return baseSmsTemplate;
        }
    }

    public static class BoratcastLineOrderTemplate extends BaseWxTemplate {


        public BoratcastLineOrderTemplate(String name, String templateId) {
            super(name, templateId);
        }
        public BoratcastLineOrderTemplate setTemplateParams(String lineName,String shunludu, Date startTime, String startCity, String startDistrict, String startAddress, String endCity, String endDistrict, String endAddress, String orderNo, String driverLineNo, String orderMobile, String driverMobile, int orderType, BigDecimal amount) {
            if(shunludu!=null&&shunludu.indexOf(".")>0) {
                shunludu=shunludu.substring(0, shunludu.indexOf("."));
            }

            BoratcastLineOrderTemplate baseSmsTemplate=new BoratcastLineOrderTemplate(getName(),getTemplateId());
            baseSmsTemplate.setSmsTemplate(SmsTemplatesDefinedList.BROASTCASE_LINE_TEMPLATE.setTemplateParams(shunludu,startTime,startCity,startDistrict,startAddress,endCity,endDistrict,endAddress,orderNo,driverLineNo).setMobile(driverMobile));

            if(StringUtils.hasText(orderMobile)&&orderMobile.length()>2){
                orderMobile=orderMobile.substring(orderMobile.length()-2);
            }
            String address=startCity+startDistrict+startAddress+"-"+endCity+endDistrict+endAddress;
            if(address.length()>20){
                address=address.substring(0,18)+"…";
            }
            baseSmsTemplate.putParam("{{thing3.DATA}}", address);
            baseSmsTemplate.putParam("{{time9.DATA}}", DateUtil.date2String(startTime, DateUtil.DATE_PATTERN_MONTH_DAY_HH_MM));
            baseSmsTemplate.putParam("{{character_string12.DATA}}", "**"+orderMobile);
            baseSmsTemplate.putParam("{{const14.DATA}}", orderType==1?"独享":"拼车"); //拼车 独享
            baseSmsTemplate.putParam("{{thing2.DATA}}", String.format("您的常用路线上有顺路乘客发单了，请查看"));

            baseSmsTemplate.setPage(String.format("/pages/confirmTogether/index?orderSerialNo=%s&lineNo=%s", orderNo, driverLineNo));
            return baseSmsTemplate;
        }
    }


    public static class InviteOrderTemplate extends BaseWxTemplate {


        public InviteOrderTemplate(String name, String templateId) {
            super(name, templateId);
        }
        public InviteOrderTemplate setTemplateParams(String shunludu, Date startTime, String startCity, String startDistrict, String startAddress, String endCity, String endDistrict, String endAddress, String orderNo, String driverTripNo, String orderMobile, String driverMobile, int orderType, BigDecimal amount) {
            if(shunludu!=null&&shunludu.indexOf(".")>0) {
                shunludu=shunludu.substring(0, shunludu.indexOf("."));
            }
            InviteOrderTemplate baseSmsTemplate=new InviteOrderTemplate(getName(),getTemplateId());
            baseSmsTemplate.setSmsTemplate(SmsTemplatesDefinedList.INVITE_DRIVER_TRIP_TEMPLATE.setTemplateParams(shunludu,startTime,startCity,startDistrict,startAddress,endCity,endDistrict,endAddress,orderNo,driverTripNo).setMobile(driverMobile));

            if(StringUtils.hasText(orderMobile)&&orderMobile.length()>2){
                orderMobile=orderMobile.substring(orderMobile.length()-2);
            }
            String address=startCity+startDistrict+startAddress+"-"+endCity+endDistrict+endAddress;
            if(address.length()>20){
                address=address.substring(0,18)+"…";
            }
            baseSmsTemplate.putParam("{{thing3.DATA}}", address);
            baseSmsTemplate.putParam("{{time9.DATA}}", DateUtil.date2String(startTime, DateUtil.DATE_PATTERN_MONTH_DAY_HH_MM));
            baseSmsTemplate.putParam("{{character_string12.DATA}}", "**"+orderMobile);
            baseSmsTemplate.putParam("{{const14.DATA}}", orderType==1?"独享":"拼车"); //拼车 独享
            baseSmsTemplate.putParam("{{thing2.DATA}}", String.format("乘客向您发起同行邀请，%s顺路，请查看",shunludu+"%"));

            baseSmsTemplate.setPage(String.format("/pages/confirmTogether/index?orderSerialNo=%s&tripOrderSerialNo=%s", orderNo, driverTripNo));
            return baseSmsTemplate;
        }
    }
}
