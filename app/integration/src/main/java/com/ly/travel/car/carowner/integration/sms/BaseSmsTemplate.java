package com.ly.travel.car.carowner.integration.sms;

import java.util.HashMap;
import java.util.Map;

public abstract class BaseSmsTemplate<T> {



    public BaseSmsTemplate(String name, int templateId) {

        this.name = name;
        this.templateId = templateId;
    }

    public String getMobile() {
        return mobile;
    }

    public <T extends BaseSmsTemplate> T setMobile(String mobile) {
        this.mobile = mobile;
        return (T)this;
    }

    private String mobile;
    private String name;

    public String getName() {
        return name;
    }

    void setName(String name) {
        this.name = name;
    }

    int getTemplateId() {
        return templateId;
    }

    void setTemplateId(int templateId) {
        this.templateId = templateId;
    }

    private int templateId;
    private Map<String, String> params;


    public Map<String, String> getParams() {
        return this.params;
    }

    <T extends BaseSmsTemplate> T putParam(String name,String value ) {
        if(this.params==null){
            this.params=new HashMap<>();
        }
        this.params.put(name,value);
        return (T)this;
    }



}
