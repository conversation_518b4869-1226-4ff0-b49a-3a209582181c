package com.ly.travel.car.carowner.integration.sms;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.Map;

/**
 * Created by zhz5350 on 2017/11/29.
 */
public class SMSBody {

    @J<PERSON><PERSON>ield(name = "Account")
    private String Account;
    @JSO<PERSON>ield(name = "Password")
    private String Password;
    @JSONField(name = "Action")
    private String Action;

    @JSONField(name = "Mobile")
    private String Mobile;

    @JSONField(name = "IsGlobal")
    private int IsGlobal;

    @JSONField(name = "GlobalNumber")
    private String GlobalNumber;

    @JSONField(name = "NodeId")
    private int NodeId;

    @JSONField(name = "PlatformId")
    private String PlatformId;



    @J<PERSON>NField(name = "InputParameters")
    private Map<String, String> InputParameters;

    public String getAccount() {
        return Account;
    }

    public void setAccount(String account) {
        Account = account;
    }

    public String getPassword() {
        return Password;
    }

    public void setPassword(String password) {
        Password = password;
    }

    public String getAction() {
        return Action;
    }

    public void setAction(String action) {
        Action = action;
    }

    public String getMobile() {
        return Mobile;
    }

    public void setMobile(String mobile) {
        Mobile = mobile;
    }


    public int getIsGlobal() {
        return IsGlobal;
    }

    public void setIsGlobal(int isGlobal) {
        IsGlobal = isGlobal;
    }

    public String getGlobalNumber() {
        return GlobalNumber;
    }

    public void setGlobalNumber(String globalNumber) {
        GlobalNumber = globalNumber;
    }

    public int getNodeId() {
        return NodeId;
    }

    public void setNodeId(int nodeId) {
        NodeId = nodeId;
    }

    public String getPlatformId() {
        return PlatformId;
    }

    public void setPlatformId(String platformId) {
        PlatformId = platformId;
    }


    public Map<String, String> getInputParameters() {
        return InputParameters;
    }

    public void setInputParameters(Map<String, String> inputParameters) {
        InputParameters = inputParameters;
    }
}
