package com.ly.travel.car.carowner.integration.sms;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.scheme.WxMaGenerateSchemeRequest;
import com.alibaba.fastjson.JSON;
import com.ly.travel.car.carowner.common.cache.CacheUtils;
import com.ly.travel.car.carowner.common.enums.CacheKeyEnum;
import com.ly.travel.car.carowner.common.thread.MTThreadPoolEnum;
import com.ly.travel.car.carowner.common.thread.MTThreadPoolExecutors;
import com.ly.travel.car.carowner.common.utils.CarownerConfigCenterUtils;
import com.ly.travel.car.carowner.common.utils.EnvUtils;
import com.ly.travel.car.carowner.common.utils.ShortUrlUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
public class SmsComponent {
    private static final Logger logger = LoggerFactory.getLogger(SmsComponent.class);
    private static final String ACTION = "CreateAndSend";
    private static final String ACCOUNT="groundtravel.shared.mobile";
    private static final String PASSWORD="1hC5xqrV";


    @Autowired(required = false)
    private WxMaService wxMaService;

    public void sendTemplateMsg(BaseSmsTemplate baseSmsTemplate) {


        MTThreadPoolExecutors.create(MTThreadPoolEnum.SMS_NOTICE).execute(()->{
            String resultMsg = "";

            SMSBody smsBody = getCommonBody(baseSmsTemplate.getTemplateId());
            smsBody.setMobile(baseSmsTemplate.getMobile());
            if(baseSmsTemplate instanceof SmsTemplatesDefinedList.TemplateWxPageAction) {
                ((SmsTemplatesDefinedList.TemplateWxPageAction) baseSmsTemplate).loadWxPage(this::convertWxShortUrl);
            }
            smsBody.setInputParameters(baseSmsTemplate.getParams());
            try {
                resultMsg = sendMsg(smsBody,baseSmsTemplate.getName());
                if(StringUtils.isNotBlank(resultMsg)){
                    if(JSON.parseObject(resultMsg).getBigInteger("code").intValue()!=0){
                        logger.warn("调用短信失败");
                    }
                }
            } catch (Exception e) {
                logger.error("发送短信失败:{}", e);
            }
        });


    }

    public String convertWxShortUrl(String wxPage,boolean cache) {
        String url=null;
        String key=CacheKeyEnum.WX_JUM_PAGE.format(wxPage);
        key=key.replaceAll("/","_").replaceAll("\\\\","_");
        if(cache){
            url=CacheUtils.getCacheValue(key);
        }
        if(StringUtils.isNotBlank(url)){
            logger.info("从缓存中取得地址,{}",url);
            return url;
        }
        try {
            WxMaGenerateSchemeRequest.JumpWxa.JumpWxaBuilder jumpWxaBuilder=WxMaGenerateSchemeRequest.JumpWxa.newBuilder().envVersion(EnvUtils.getEnv().product()?"release":"trial");
            int index=wxPage.indexOf("?");
            if(index>0){
                jumpWxaBuilder.path(wxPage.substring(0,index)).query(wxPage.substring(index+1));
            }else{
                jumpWxaBuilder.path(wxPage);
            }
            WxMaGenerateSchemeRequest.JumpWxa jumpWxa = jumpWxaBuilder.build();
            //默认值"release"。要打开的小程序版本。正式版为"release"，体验版为"trial"，开发版为"develop"，
            WxMaGenerateSchemeRequest request = WxMaGenerateSchemeRequest.newBuilder().jumpWxa(jumpWxa).build();
            url = CarownerConfigCenterUtils.wxAppJumpUrl+ wxMaService.getWxMaSchemeService().generate(request);
            if(StringUtils.isBlank(url)){
                return url;
            }
            url = ShortUrlUtil.create(url);
            if(StringUtils.isBlank(url)){
                return url;
            }
            if(cache) {
                CacheUtils.setCacheValue(key, url, CacheKeyEnum.WX_JUM_PAGE.getExpire());
            }
        }catch (Exception ex){
            logger.error("获取微信小程序跳转地址出错,",ex);
        }
        return url;
    }

    public static void main(String[] args) {
        new SmsComponent().sendTemplateMsg(SmsTemplatesDefinedList.CANCEL_TEMPLATE
                .setTemplateParams(new Date(),"","").setMobile("********"));
    }





    private SMSBody getCommonBody(int nodeId) {
        SMSBody smsBody = new SMSBody();
        smsBody.setAccount(ACCOUNT);
        smsBody.setPassword(PASSWORD);
        smsBody.setAction(ACTION);
        smsBody.setNodeId(nodeId);
        smsBody.setPlatformId("1");
        return smsBody;
    }

    private String sendMsg(SMSBody postBody,String name) {

        String data= JSON.toJSONString(postBody);

        String result=null;
        try {
            result = SmsHttpClientUtil.getInstance().post(CarownerConfigCenterUtils.SMS_URL, data, null);
        }catch (Exception ex) {
            logger.error("短信提醒，{},传入参数：{}", name,JSON.toJSONString(postBody), ex);
        }finally {
            logger.info("短信提醒，{},传入参数：{}，返回参数：{}", name,JSON.toJSONString(postBody), result);

        }
        return result;
    }
}
