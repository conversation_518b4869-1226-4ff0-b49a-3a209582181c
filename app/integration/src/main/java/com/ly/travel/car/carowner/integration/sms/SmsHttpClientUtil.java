package com.ly.travel.car.carowner.integration.sms;

import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.TimeUnit;

public class SmsHttpClientUtil {
    static final Logger logger = LoggerFactory.getLogger(SmsHttpClientUtil.class);

    public static SmsHttpClientUtil instance = null;
    private OkHttpClient okHttpClient;

    SmsHttpClientUtil() {
        okHttpClient = new OkHttpClient.Builder()
                .retryOnConnectionFailure(true)
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(5, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .connectionPool(new ConnectionPool(1,1L, TimeUnit.MINUTES))
                .build();
    }

    public static SmsHttpClientUtil getInstance() {
        if (instance == null) {
            synchronized (SmsHttpClientUtil.class) {
                if (instance == null) {
                    instance = new SmsHttpClientUtil();
                }
            }
        }
        return instance;
    }

    public String post(String url, String jsonData, Map<String, String> headerMap) {
        Response response;
        try {
            Request.Builder builder = new Request.Builder();
            if (headerMap != null && headerMap.size() > 0) {
                for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                    builder.header(entry.getKey(), entry.getValue());
                }
            }

            RequestBody requestBody = RequestBody.create(MediaType.parse("application/json;charset=UTF-8"),jsonData);
            Request request = builder
                    .url(url)
                    .post(requestBody)
                    .build();
            response = okHttpClient.newCall(request).execute();
            String result = null;
            try {
                result = response.body().string();
            } catch (Exception e) {
                result = "";
            }
            return result;
        } catch (Exception e) {
            logger.error("请求失败" + url + " body" + jsonData, e);
        }
        return null;
    }

}
