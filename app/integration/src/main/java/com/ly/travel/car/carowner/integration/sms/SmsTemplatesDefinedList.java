package com.ly.travel.car.carowner.integration.sms;


import com.ly.travel.car.carowner.common.utils.DateUtil;

import java.util.Date;

public class SmsTemplatesDefinedList {
//pages/index/index 首页
//pages/order-list/index 订单列表
//pages/mine/index 我的


    public  final static DriverCertificationTemplate DRIVER_CERTIFICATION_TEMPLATE=new DriverCertificationTemplate("司机认证环节",170623);
    public  final static CancelOrderTemplate CANCEL_TEMPLATE=new CancelOrderTemplate("订单取消",170637);
    public  final static WithdrawalTemplate WITHDRAWAL_TEMPLATE=new WithdrawalTemplate("司机提现",1008611);
    public  final static DriverReadyUnVerifyTemplate DRIVER_READY_UN_VERIFY_TEMPLATE = new DriverReadyUnVerifyTemplate("行程前出发通知-未验证", 170636);
    public  final static DriverReadyVerifyTemplate DRIVER_READY_VERIFY_TEMPLATE = new DriverReadyVerifyTemplate("行程前出发通知-已验证", 170625);
    public  final static BoratcastOrderTemplate BROASTCASE_TEMPLATE=new BoratcastOrderTemplate("司机行程播单通知",170639);
    public  final static LineBoratcastOrderTemplate BROASTCASE_LINE_TEMPLATE=new LineBoratcastOrderTemplate("常用线路播单通知",170639);
    public  final static DriversShareAutomaticOrderTemplate DRIVERS_SHARE_AUTOMATIC_ORDER_TEMPLATE=new DriversShareAutomaticOrderTemplate("司机分享自动接单",170666);
    public final static InviteDriverTripTemplate INVITE_DRIVER_TRIP_TEMPLATE = new InviteDriverTripTemplate("邀请司机行程通知", 170656);
    public final static CancelDriverTripTemplate CANCEL_DRIVER_TRIP_TEMPLATE = new CancelDriverTripTemplate("行程过期自动取消", 170638);

    //如果要转成小程序跳转的地址，需继续这个接口
    public interface TemplateWxPageAction{
        void loadWxPage(TemplateWxPage templateWxPageAction);
    }
    //从微信获取地址
    @FunctionalInterface
    public interface TemplateWxPage{
        public String getWxPage(String baseUrl,boolean cache);
    }


    public static class DriverCertificationTemplate extends BaseSmsTemplate {

        public DriverCertificationTemplate(String name, int templateId) {
            super(name, templateId);
        }

        public DriverCertificationTemplate setTemplateParams(String phone4, String result,String url) {
            DriverCertificationTemplate baseSmsTemplate=new DriverCertificationTemplate(getName(),getTemplateId());
            baseSmsTemplate.putParam("@phone4", phone4);
            baseSmsTemplate.putParam("@result", result);
            baseSmsTemplate.putParam("@url", url);
            return baseSmsTemplate;
        }
    }

    public static class CancelOrderTemplate extends BaseSmsTemplate implements TemplateWxPageAction{
        private String orderNo;
        private String driverTripNo;

        public CancelOrderTemplate(String name, int templateId) {
            super(name, templateId);
        }

        public CancelOrderTemplate setTemplateParams(Date star_time,String orderNo, String driverTripNo) {
            CancelOrderTemplate baseSmsTemplate=new CancelOrderTemplate(getName(),getTemplateId());
            baseSmsTemplate.putParam("@star_time", DateUtil.date2String(star_time, DateUtil.DATE_PATTERN_MONTH_DAY_HH_MM));
            //baseSmsTemplate.putParam("@url", url);
            baseSmsTemplate.driverTripNo = driverTripNo;
            baseSmsTemplate.orderNo = orderNo;
            return baseSmsTemplate;
        }

        @Override
        public void loadWxPage(SmsTemplatesDefinedList.TemplateWxPage templateWxPageAction) {
            this.putParam("@url", templateWxPageAction.getWxPage(String.format("/pages/confirmTogether/index?orderSerialNo=%s&tripOrderSerialNo=%s", this.orderNo, this.driverTripNo), false));

        }
    }

    public static class WithdrawalTemplate extends BaseSmsTemplate {

        public WithdrawalTemplate(String name, int templateId) {
            super(name, templateId);
        }

        public WithdrawalTemplate setTemplateParams(String verifyCode, String url) {
            WithdrawalTemplate withdrawalTemplate = new WithdrawalTemplate(getName(), getTemplateId());
            withdrawalTemplate.putParam("@verify_code", verifyCode);
            withdrawalTemplate.putParam("@url", url);
            return withdrawalTemplate;
        }
    }

    public static class DriverReadyUnVerifyTemplate extends BaseSmsTemplate implements TemplateWxPageAction {

        private String orderNo;
        private String driverTripNo;

        public DriverReadyUnVerifyTemplate(String name, int templateId) {
            super(name, templateId);
        }

        public DriverReadyUnVerifyTemplate setTemplateParams(String startTime, String startAddress, String arriveAddress, String orderNo, String driverTripNo) {
            DriverReadyUnVerifyTemplate driverReadyUnVerifyTemplate = new DriverReadyUnVerifyTemplate(getName(), getTemplateId());
            driverReadyUnVerifyTemplate.putParam("@start_time", startTime);
            driverReadyUnVerifyTemplate.putParam("@start_address", startAddress);
            driverReadyUnVerifyTemplate.putParam("@arrive_address", arriveAddress);
            driverReadyUnVerifyTemplate.driverTripNo = driverTripNo;
            driverReadyUnVerifyTemplate.orderNo = orderNo;
            return driverReadyUnVerifyTemplate;
        }

        @Override
        public void loadWxPage(SmsTemplatesDefinedList.TemplateWxPage templateWxPageAction) {
            this.putParam("@url", templateWxPageAction.getWxPage(String.format("/pages/confirmTogether/index?orderSerialNo=%s&tripOrderSerialNo=%s", this.orderNo, this.driverTripNo), false));
        }
    }

    public static class DriverReadyVerifyTemplate extends BaseSmsTemplate implements TemplateWxPageAction {

        private String orderNo;
        private String driverTripNo;

        public DriverReadyVerifyTemplate(String name, int templateId) {
            super(name, templateId);
        }

        public DriverReadyVerifyTemplate setTemplateParams(String startTime, String startAddress, String arriveAddress, String orderNo, String driverTripNo) {
            DriverReadyVerifyTemplate driverReadyVerifyTemplate = new DriverReadyVerifyTemplate(getName(), getTemplateId());
            driverReadyVerifyTemplate.putParam("@start_time", startTime);
            driverReadyVerifyTemplate.putParam("@start_address", startAddress);
            driverReadyVerifyTemplate.putParam("@arrive_address", arriveAddress);
            driverReadyVerifyTemplate.driverTripNo = driverTripNo;
            driverReadyVerifyTemplate.orderNo = orderNo;
            return driverReadyVerifyTemplate;
        }

        @Override
        public void loadWxPage(SmsTemplatesDefinedList.TemplateWxPage templateWxPageAction) {
            this.putParam("@url", templateWxPageAction.getWxPage(String.format("/pages/confirmTogether/index?orderSerialNo=%s&tripOrderSerialNo=%s", this.orderNo, this.driverTripNo), false));
        }
    }


    public static class BoratcastOrderTemplate extends BaseSmsTemplate<BoratcastOrderTemplate> implements TemplateWxPageAction {

        public BoratcastOrderTemplate(String name, int templateId) {
            super(name, templateId);
        }
        private String orderNo;
        private String driverTripNo;
        public BoratcastOrderTemplate setTemplateParams(String shunludu, Date star_time, String startCity, String startDistrict, String startAddress, String endCity, String endDistrict, String arriveAddress, String orderNo, String driverTripNo) {
            if (startCity != null && !startCity.endsWith("市")) {
                startCity = startCity + "市";
            }
            BoratcastOrderTemplate template = new BoratcastOrderTemplate(getName(), getTemplateId());
            template.putParam("@shunludu", shunludu+"%");
            template.putParam("@star_time", DateUtil.date2String(star_time,DateUtil.DATE_PATTERN_MONTH_DAY_HH_MM));
            template.putParam("@star_address", startCity+startDistrict+startAddress);
            template.driverTripNo=driverTripNo;
            template.orderNo=orderNo;
            return template;
        }

        public void loadWxPage(SmsTemplatesDefinedList.TemplateWxPage templateWxPageAction) {
//            this.putParam("@url", templateWxPageAction.getWxPage("pages/order-list/index", false));
            this.putParam("@url", templateWxPageAction.getWxPage(String.format("/pages/confirmTogether/index?orderSerialNo=%s&tripOrderSerialNo=%s",this.orderNo,this.driverTripNo), false));
        }
    }

    public static class LineBoratcastOrderTemplate extends BaseSmsTemplate<LineBoratcastOrderTemplate> implements TemplateWxPageAction {

        public LineBoratcastOrderTemplate(String name, int templateId) {
            super(name, templateId);
        }
        private String orderNo;
        private String driverLineNo;
        public LineBoratcastOrderTemplate setTemplateParams(String shunludu, Date star_time, String startCity, String startDistrict, String startAddress, String endCity, String endDistrict, String arriveAddress,String orderNo,String driverLineNo) {
            if (startCity != null && !startCity.endsWith("市")) {
                startCity = startCity + "市";
            }
            LineBoratcastOrderTemplate template = new LineBoratcastOrderTemplate(getName(), getTemplateId());
            template.putParam("@shunludu", shunludu+"%");
            template.putParam("@star_time", DateUtil.date2String(star_time,DateUtil.DATE_PATTERN_MONTH_DAY_HH_MM));
            template.putParam("@star_address", startCity+startDistrict+startAddress);
            template.driverLineNo=driverLineNo;
            template.orderNo=orderNo;
            return template;
        }

        public void loadWxPage(SmsTemplatesDefinedList.TemplateWxPage templateWxPageAction) {
//            this.putParam("@url", templateWxPageAction.getWxPage("pages/order-list/index", false));
            this.putParam("@url", templateWxPageAction.getWxPage(String.format("/pages/confirmTogether/index?orderSerialNo=%s&lineNo=%s",this.orderNo,this.driverLineNo), false));
        }
    }
    public static class DriversShareAutomaticOrderTemplate extends BaseSmsTemplate<DriversShareAutomaticOrderTemplate> implements TemplateWxPageAction {

        public DriversShareAutomaticOrderTemplate(String name, int templateId) {
            super(name, templateId);
        }
        private String orderNo;
        private String driverLineNo;
        public DriversShareAutomaticOrderTemplate setTemplateParams(Integer shunludu, Date star_time, String star_address,String orderNo,String driverLineNo) {
            DriversShareAutomaticOrderTemplate template = new DriversShareAutomaticOrderTemplate(getName(), getTemplateId());
            template.putParam("@shunludu", shunludu+"%");
            template.putParam("@star_time", DateUtil.date2String(star_time,DateUtil.DATE_PATTERN_MONTH_DAY_HH_MM));
            template.putParam("@star_address", star_address);
            template.driverLineNo=driverLineNo;
            template.orderNo=orderNo;
            return template;
        }

        public void loadWxPage(SmsTemplatesDefinedList.TemplateWxPage templateWxPageAction) {
            this.putParam("@url", templateWxPageAction.getWxPage(String.format("/pages/confirmTogether/index?orderSerialNo=%s&tripOrderSerialNo=%s",this.orderNo,this.driverLineNo), false));
        }
    }


    public static class InviteDriverTripTemplate extends BaseSmsTemplate<InviteDriverTripTemplate> implements TemplateWxPageAction {

        public InviteDriverTripTemplate(String name, int templateId) {
            super(name, templateId);
        }

        private String orderNo;
        private String driverTripNo;

        public InviteDriverTripTemplate setTemplateParams(String shunludu, Date star_time, String startCity, String startDistrict, String startAddress, String endCity, String endDistrict, String arriveAddress, String orderNo, String driverTripNo) {
            InviteDriverTripTemplate template = new InviteDriverTripTemplate(getName(), getTemplateId());
            if (startCity != null && !startCity.endsWith("市")) {
                startCity = startCity + "市";
            }
            template.putParam("@shunludu", shunludu + "%");
            template.putParam("@star_time", DateUtil.date2String(star_time, DateUtil.DATE_PATTERN_MONTH_DAY_HH_MM));
            template.putParam("@star_address", startCity+startDistrict+startAddress);
            template.driverTripNo = driverTripNo;
            template.orderNo = orderNo;
            return template;
        }

        public void loadWxPage(SmsTemplatesDefinedList.TemplateWxPage templateWxPageAction) {
            this.putParam("@url", templateWxPageAction.getWxPage(String.format("/pages/confirmTogether/index?orderSerialNo=%s&tripOrderSerialNo=%s", this.orderNo, this.driverTripNo), false));
        }
    }

    public static class CancelDriverTripTemplate extends BaseSmsTemplate<CancelDriverTripTemplate> implements TemplateWxPageAction {

        public CancelDriverTripTemplate(String name, int templateId) {
            super(name, templateId);
        }

        private String orderNo;
        private String driverTripNo;

        public CancelDriverTripTemplate setTemplateParams(Date star_time, String startCity, String startDistrict, String startAddress, String endCity, String endDistrict, String arriveAddress) {
            CancelDriverTripTemplate template = new CancelDriverTripTemplate(getName(), getTemplateId());
            if (startCity != null && !startCity.endsWith("市")) {
                startCity = startCity + "市";
            }
            template.putParam("@star_time", DateUtil.date2String(star_time, DateUtil.DATE_PATTERN_MONTH_DAY_HH_MM));
            template.putParam("@star_address", startCity+startDistrict+startAddress);

            return template;
        }

        public void loadWxPage(SmsTemplatesDefinedList.TemplateWxPage templateWxPageAction) {
            this.putParam("@url", templateWxPageAction.getWxPage("/pages/order-list/index", true));
        }
    }


}
