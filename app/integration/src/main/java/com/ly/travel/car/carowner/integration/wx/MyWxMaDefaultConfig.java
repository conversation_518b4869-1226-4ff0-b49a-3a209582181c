package com.ly.travel.car.carowner.integration.wx;

import cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class MyWxMaDefaultConfig extends WxMaDefaultConfigImpl {


    @Override
    public boolean autoRefreshToken() {
        return false;
    }

    @Override
    public boolean isAccessTokenExpired() {
        return false;
    }



    //放入数据库或者哪里
    @Override
    public synchronized void updateAccessToken(String accessToken, int expiresInSeconds) {
        log.info("刷新access_token内容,{},{}",accessToken,expiresInSeconds);
        this.setAccessToken(accessToken);
        this.setExpiresTime(this.expiresAheadInMillis(expiresInSeconds));
    }

    @Override
    public String getAccessToken(){
        return accessTokenFunction.getAccessToken(this.getAppid());
    }

    public void setAccessTokenFunction(IAccessTokenFunction accessTokenFunction){
        this.accessTokenFunction=accessTokenFunction;
    }
    private IAccessTokenFunction accessTokenFunction;



    @Override
    public boolean isJsapiTicketExpired() {
        return false;
    }

    @Override
    public String getJsapiTicket() {
        return accessTokenFunction.getJSAccessToken(this.getAppid());
    }
}
