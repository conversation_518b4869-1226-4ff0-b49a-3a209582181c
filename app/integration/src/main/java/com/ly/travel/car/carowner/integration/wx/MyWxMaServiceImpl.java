package com.ly.travel.car.carowner.integration.wx;

import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.config.WxMaConfig;
import cn.binarywang.wx.miniapp.util.WxMaConfigHolder;
import com.ly.travel.car.carowner.common.utils.CarownerConfigCenterUtils;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.error.WxRuntimeException;

import java.io.IOException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;

public class MyWxMaServiceImpl extends WxMaServiceImpl {
    @Override
    public WxMaConfig getWxMaConfig() {
        if(WxMaConfigHolder.get().equals("default")){
            WxMaConfigHolder.set(CarownerConfigCenterUtils.WXAPP_ID);
        }
        return super.getWxMaConfig();
    }
}
