package com.ly.travel.car.carowner.integration.wx;

import cn.binarywang.wx.miniapp.api.WxMaService;
import com.ly.travel.car.carowner.common.utils.CarownerConfigCenterUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class WxMaConfig {


    @Bean
    public WxMaService wxMaService(IAccessTokenFunction accessTokenFunction) {

        WxMaService maService = new MyWxMaServiceImpl();

        MyWxMaDefaultConfig wxMaDefaultConfig=new MyWxMaDefaultConfig();
        wxMaDefaultConfig.setAppid(CarownerConfigCenterUtils.WXAPP_ID);
        wxMaDefaultConfig.setSecret(CarownerConfigCenterUtils.WXAPPSECRET);
        wxMaDefaultConfig.setAccessTokenFunction(accessTokenFunction);
        maService.setWxMaConfig(wxMaDefaultConfig);

        MyWxMaDefaultConfig wxMaDefaultConfig_gzh=new MyWxMaDefaultConfig();
        wxMaDefaultConfig_gzh.setAppid(CarownerConfigCenterUtils.WXGZH_ID);
        wxMaDefaultConfig_gzh.setSecret(CarownerConfigCenterUtils.WXGZHSECRET);
        wxMaDefaultConfig_gzh.setAccessTokenFunction(accessTokenFunction);
        maService.addConfig(CarownerConfigCenterUtils.WXGZH_ID,wxMaDefaultConfig_gzh);
        return maService;
    }

}
