package com.ly.travel.car.carowner.integration.wx.push;

import cn.binarywang.wx.miniapp.bean.WxMaTemplateData;

import java.util.ArrayList;
import java.util.List;

public abstract class BaseWxTemplate {



    public BaseWxTemplate(String name, String templateId) {

        this.name = name;
        this.templateId = templateId;
    }

    public String getToUser() {
        return toUser;
    }

    public <T extends BaseWxTemplate> T setToUser(String toUser) {
        this.toUser = toUser;
        return (T)this;
    }
    public String getPage() {
        return toUser;
    }

    public <T extends BaseWxTemplate> T setPage(String toUser) {
        this.toUser = toUser;
        return (T)this;
    }
    private String toUser;
    private String name;
    private String page;


    public String getName() {
        return name;
    }

    void setName(String name) {
        this.name = name;
    }

    String getTemplateId() {
        return templateId;
    }

    void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    private String templateId;
    private List<WxMaTemplateData> params;


    List<WxMaTemplateData> getParams() {
        return this.params;
    }

    <T extends BaseWxTemplate> T putParam(String name, String value ) {
        if(this.params==null){
            this.params=new ArrayList<>();
        }
        this.params.add(new WxMaTemplateData(name,value));
        return (T)this;
    }

}
