package com.ly.travel.car.carowner.integration.wx.push;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaUniformMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class WechatPushComponent {
    private static final Logger logger = LoggerFactory.getLogger(WechatPushComponent.class);

    @Autowired
    private WxMaService wxMaService;
    @Value("${dsf.service.config.env:}")
    private String dsfEnv;



    public boolean pushTemplate(BaseWxTemplate wxTemplate){

        WxMaUniformMessage wxMaUniformMessage = getCommonBody(wxTemplate);
        try {
             wxMaService.getMsgService().sendUniformMsg(wxMaUniformMessage);
             return true;
        } catch (Exception e) {
            logger.warn("发送微信失败:{}", e);
        }

        return false;
    }
    private WxMaUniformMessage getCommonBody(BaseWxTemplate wxTemplate){
        WxMaUniformMessage wxMaUniformMessage=new WxMaUniformMessage();
        wxMaUniformMessage.setToUser(wxTemplate.getToUser());
        wxMaUniformMessage.setPage(wxTemplate.getPage());
        wxMaUniformMessage.setTemplateId(wxTemplate.getTemplateId());
        wxMaUniformMessage.setData(wxTemplate.getParams());
        return wxMaUniformMessage;
    }

    public static void main(String[] args) {
        new WechatPushComponent().pushTemplate(WxTemplatesDefinedList.TEST_TEMPALTE.setTemplateParams("姓名","111").setToUser("kkkkk"));
    }
}
