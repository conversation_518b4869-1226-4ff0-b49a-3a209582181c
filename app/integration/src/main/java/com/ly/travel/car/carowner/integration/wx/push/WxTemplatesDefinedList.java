package com.ly.travel.car.carowner.integration.wx.push;


public class WxTemplatesDefinedList {


    public  final static TestTemplate  TEST_TEMPALTE=new TestTemplate("测试模板","11111111");



    public static class TestTemplate extends BaseWxTemplate {

        public TestTemplate(String name, String templateId) {
            super(name, templateId);
        }

        public TestTemplate setTemplateParams(String userName, String time) {
            TestTemplate baseSmsTemplate=new TestTemplate(getName(),getTemplateId());
            baseSmsTemplate.putParam("@keyword1", userName);
            baseSmsTemplate.putParam("@keyword2", time);
            return baseSmsTemplate;
        }
    }


}
