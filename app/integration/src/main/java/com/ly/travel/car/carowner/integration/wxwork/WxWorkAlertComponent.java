package com.ly.travel.car.carowner.integration.wxwork;

import com.alibaba.fastjson.JSON;
import com.ly.travel.car.carowner.common.thread.MTThreadPoolEnum;
import com.ly.travel.car.carowner.common.thread.MTThreadPoolExecutors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;

@Component
public class WxWorkAlertComponent {
    static final String wxWorkUrl = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=";

    @Autowired(required = false)
    private IGetWxWorkUrl getWxWorkUrlAction;
    /**
     * 调用企业微信提醒
     * @param wxWorkTemplateEnum  模板类型
     * @param userList  通知企业员工工号
     * @param args  与模板拼接参数
     */
    public void alert(final WxWorkTemplateEnum wxWorkTemplateEnum, final List<String> userList, final Object... args) {
        MTThreadPoolExecutors.create(MTThreadPoolEnum.WXWORK_ALTER).execute(()->{
            WxWorkModel wxWorkModel = WxWorkModel.createContext(wxWorkTemplateEnum.getContext(args));
            if (!CollectionUtils.isEmpty(userList)) {
                wxWorkModel.setUserList(userList);
            }
            String url=null;
            if(getWxWorkUrlAction!=null&& StringUtils.hasText(wxWorkTemplateEnum.getSysConfigKey())){
                url=getWxWorkUrlAction.getSysConfigWxWorkUrl(wxWorkTemplateEnum.getSysConfigKey());
            }
            else{
                url=wxWorkUrl+wxWorkTemplateEnum.getWxKey();
            }
            WxWorkHttpClientUtil.getInstance().post(url, JSON.toJSONString(wxWorkModel), null);
        });
    }

    public static void main(String[] args) {
        new WxWorkAlertComponent().alert(WxWorkTemplateEnum.TEST, Arrays.asList("1201819"), "t");
    }
}
