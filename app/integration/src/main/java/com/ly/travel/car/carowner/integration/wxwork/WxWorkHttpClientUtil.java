package com.ly.travel.car.carowner.integration.wxwork;

import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.TimeUnit;

public class WxWorkHttpClientUtil {
    static final Logger logger = LoggerFactory.getLogger(WxWorkHttpClientUtil.class);

    public static WxWorkHttpClientUtil instance = null;
    private OkHttpClient okHttpClient;

    WxWorkHttpClientUtil() {
        okHttpClient = new OkHttpClient.Builder()
                .retryOnConnectionFailure(true)
                .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(5, TimeUnit.SECONDS)
                .writeTimeout(10, TimeUnit.SECONDS)
                .connectionPool(new ConnectionPool(1,1L, TimeUnit.MINUTES))
                .build();
    }

    public static WxWorkHttpClientUtil getInstance() {
        if (instance == null) {
            synchronized (WxWorkHttpClientUtil.class) {
                if (instance == null) {
                    instance = new WxWorkHttpClientUtil();
                }
            }
        }
        return instance;
    }

    public String post(String url, String jsonData, Map<String, String> headerMap) {
        Response response;
        try {
            Request.Builder builder = new Request.Builder();
            if (headerMap != null && headerMap.size() > 0) {
                for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                    builder.header(entry.getKey(), entry.getValue());
                }
            }

            RequestBody requestBody = RequestBody.create(MediaType.parse("application/json;charset=UTF-8"),jsonData);
            Request request = builder
                    .url(url)
                    .post(requestBody)
                    .build();
            response = okHttpClient.newCall(request).execute();
            String result = null;
            try {
                result = response.body().string();
            } catch (Exception e) {
                result = "";
            }
            return result;
        } catch (Exception e) {
            logger.error("请求失败" + url + " body" + jsonData, e);
        }
        return null;
    }

}
