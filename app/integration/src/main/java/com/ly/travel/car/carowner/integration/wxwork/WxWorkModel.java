package com.ly.travel.car.carowner.integration.wxwork;

import java.util.List;

public class WxWorkModel {
    private WxWorkModel(String context) {
        this.text = new ContentText(context);
    }

    private ContentText text;
    private String msgtype="text";

    public ContentText getText() {
        return text;
    }

    public void setText(ContentText text) {
        this.text = text;
    }

    public String getMsgtype() {
        return msgtype;
    }

    public void setMsgtype(String msgtype) {
        this.msgtype = msgtype;
    }

    public void setUserList(List<String> userList){
        this.text.setMentioned_list(userList);
    }


    public static WxWorkModel createContext(String context){
        WxWorkModel wxWorkModel=new WxWorkModel(context);
        return wxWorkModel;
    }

    private static class  ContentText{
        public ContentText(String content) {
            this.content = content;
        }

        private List<String> mentioned_list;
        private String content;

        public List<String> getMentioned_list() {
            return mentioned_list;
        }

        public void setMentioned_list(List<String> mentioned_list) {
            this.mentioned_list = mentioned_list;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }
    }
}
