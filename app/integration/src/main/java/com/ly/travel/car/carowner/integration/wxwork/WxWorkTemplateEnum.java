package com.ly.travel.car.carowner.integration.wxwork;

import com.ly.travel.car.carowner.common.enums.SysConfigEnum;

public enum WxWorkTemplateEnum {
    TEST("测试", "这是一个测试模板%s","884cc9c5-1254-40be-bb0f-2538494ec7e5",null),
    AMOUNT_NO_THAN_ZERO_NOTICE("司机提现账户余额小于0通知", "车主账户异常：%s账户余额为%s，请及时关注处理。发生时间：%s","",SysConfigEnum.DRIVER_ACCOUNT_ERROR_NOTICE_KEY.getKey()),
    SETTLEMENT_INCORRECT_FREEZE_NOTICE("结算验证不通过或订单后付订单通知", "订单金额异常：订单号%s，账单被冻结，将无法提现，请及时处理。发生时间：%s","", SysConfigEnum.SETTLEMENT_VERIFICATION_ERROR_NOTICE_KEY.getKey()),
    YEE_PAY_FAIL_NOTICE("提现易宝返回失败提醒", "账单提现失败异常：%s提现失败，失败原因：%s。提现时间：%s","", SysConfigEnum.YEE_PAY_FAIL_NOTICE.getKey()),
    DRIVER_RISK_FAIL("车主命中风控","%s%s风控不通过，请及时处理","dff7fcd9-edf6-4a70-a22c-fbb2ab35dd11", SysConfigEnum.VERIFY_NOTIFY_TEMPLE_ID.getKey()),
    WITHDRAW_RISK("提现后风控","订单号%s触发提现后风控，请知晓","", SysConfigEnum.WITHDRAWAL_RISK_NOTICE.getKey()),;



    /**
     *
     * @param name
     * @param template
     * @param wxKey   wxKey固定配置
     * @param sysConfigKey  如果有配置sysConfgKey则使用sysConfig表配置
     */
    WxWorkTemplateEnum(String name, String template, String wxKey,String sysConfigKey) {
        this.name = name;
        this.template = template;
        this.wxKey = wxKey;
        this.sysConfigKey = sysConfigKey;
    }

    private String name;
    private String template;
    private String wxKey;
    private String sysConfigKey;

    public String getSysConfigKey() {
        return sysConfigKey;
    }

    public void setSysConfigKey(String sysConfigKey) {
        this.sysConfigKey = sysConfigKey;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getWxKey() {
        return wxKey;
    }

    public void setWxKey(String wxKey) {
        this.wxKey = wxKey;
    }

    public String getTemplate() {
        return template;
    }

    public void setTemplate(String template) {
        this.template = template;
    }

    public String getContext(Object... args){
        return String.format(template,args);
    }


}
