<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ly.travel.car.carowner</groupId>
        <artifactId>shared-mobility-carowner-core</artifactId>
        <version>1.0-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <artifactId>shared-mobility-carowner-core-test</artifactId>
    <packaging>jar</packaging>
    <name>LY shared-mobility-carowner-core-test</name>
    <description>LY shared-mobility-carowner-core-test</description>

    <dependencies>
        <dependency>
            <groupId>com.ly.flight.toolkit</groupId>
            <artifactId>sof-dev-launcher</artifactId>
            <version>*******.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.ly.travel.car.carowner</groupId>
            <artifactId>shared-mobility-carowner-core-biz</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ly.travel.car.carowner</groupId>
            <artifactId>shared-mobility-carowner-core-facade-impl</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-dbcp</groupId>
            <artifactId>commons-dbcp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ibatis</groupId>
            <artifactId>ibatis</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <!-- Test dependecies -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-all</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.easymock</groupId>
            <artifactId>easymock</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-easymock</artifactId>
            <scope>test</scope>
        </dependency>

    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>com.ly.smart-doc</groupId>
                <artifactId>smart-doc-maven-plugin</artifactId>
                <version>3.0.0</version>
                <configuration>
                    <!-- smart-doc配置文档路径 -->
                    <configFile>./src/main/resources/smart-doc.json</configFile>
                    <projectName>${project.description}</projectName>
                    <excludes>
                        <!--需要排除的包-->
                        <exclude>com.alibaba:.*</exclude>
                    </excludes>
                    <includes>
                        <!--引用的三方包，如果需要将第三方包里面的字段解析出来在这边引用-->
                        <include>com.ly.sof:sof-facade-base</include>
                    </includes>
                </configuration>
                <executions>
                    <execution>
                        <!--如果不需要在执行编译时启动smart-doc，则将phase注释掉-->
                        <!--                        <phase>compile</phase>-->
                        <goals>
                            <!--smart-doc提供了html、openapi、markdown等goal，可按需配置-->
                            <!--带rpc前缀的用来生成dubbo协议的接口文档 不带的生成controller协议的接口文档-->
                            <goal>rpc-markdown</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
