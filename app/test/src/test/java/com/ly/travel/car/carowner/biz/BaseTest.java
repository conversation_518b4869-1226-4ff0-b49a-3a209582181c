package com.ly.travel.car.carowner.biz;

import com.ly.flight.toolkit.ApplicationLauncher;
import com.ly.flight.toolkit.config.Configure;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <p>集成测试基类，载入所有spring配置。</p>
 * <p>在集成测试时存在一些场景，比如不想使用真实数据库，不注册dsf等，这些都需要自定义spring配置。</p>
 */
@RunWith(SpringJUnit4ClassRunner.class)
//src/main/resources/META-INF/spring/biz-beans.xml
@ContextConfiguration(locations = {"classpath*:WEB-INF/sofweb-servlet.xml"})
//@ContextConfiguration(classes = {com.ly.travel.car.carowner.test.Application.class})
//@SpringBootTest(classes = com.ly.travel.car.carowner.test.Application.class)
public class BaseTest {




    @Before
    public void befort(){
        Configure configure = new Configure();
        configure.setEnv("dev");
        // 1、当前项目应用标志
        configure.setAppUk("groundtravel.dsf.shared.mobility.carowner.core");
        // 2、当前项目启动端口
        configure.setPort(8080);
        // 3、【只留其一】当前项目是DSF项目
        configure.setProjectType(Configure.SofProjectType.DSF);
        // 3、【只留其一】当前项目是WEB项目
//        configure.setProjectType(Configure.SofProjectType.WEB);
        // 4、【DSF】是否挂载DSF服务
        configure.setStartDsf(false);
        // 4、是否使用本地的配置文件做替换，比如在有remote-config之前，都是在filter目录下配置的各环境的配置文件值
        // 接入远程配置后改为false，默认false
        configure.useLocalConfig(true);

        // 5、【WEB】当前WEB应用启动路径
        configure.setWebPath("/carownerdsf");
        configure.setWebServletXmlPath("WEB-INF/sofweb-servlet.xml");
        // 替换本地启动目录的日志根目录
        configure.getMockConfig().put("log.root", "D:\\data\\");

        ApplicationLauncher launcher = new ApplicationLauncher();
        launcher.start(configure);

    }

    @Test
    public void add() {



    }
}
