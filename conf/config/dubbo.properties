#basic
sof.version=${sof.version}
app.name=${app.name}
app.version=${app.version}
app.type=${app.type}
sof-env=${sof-env}

#db
uniform.env=${uniform.env}
uniform.skyCode=${uniform.skyCode}
uniform.dbName=${uniform.dbName}

#dubbo
dubbo.application.name=${dubbo.application.name}
dubbo.registry.address=${dubbo.registry.address}
dubbo.container=${dubbo.container}
dubbo.service.gsname=${dubbo.service.gsname}
dubbo.service.port=${dubbo.service.port}
dubbo.service.registry.address=${dubbo.service.registry.address}
dubbo.service.deploy.container=${dubbo.service.deploy.container}
dubbo.service.version=${dubbo.service.version}

#turbomq
mq.nameSrvAddress=${mq.nameSrvAddress}
driver.bill.unfreeze.topic=${driver.bill.unfreeze.topic}
driver.bill.unfreeze.group=${driver.bill.unfreeze.group}
order.create.top.topic=${order.create.top.topic}
order.create.top.group=${order.create.top.group}

api.call.retry.topic=${api.call.retry.topic}
api.call.retry.group=${api.call.retry.group}

update.status.topic=${update.status.topic}
update.status.group=${update.status.group}

driver.ready.topic=${driver.ready.topic}
driver.ready.group=${driver.ready.group}

order.settlement.group=${order.settlement.group}

order.complete.risk.topic=${order.complete.risk.topic}
order.complete.risk.group=${order.complete.risk.group}

order.risk.update.freeze.time.topic=${order.risk.update.freeze.time.topic}
order.risk.update.freeze.time.group=${order.risk.update.freeze.time.group}

#redis
redis.groupName=${redis.groupName}

# yeePay
yeePay.callBackUrl=${yeePay.callBackUrl}

short.create.url=${short.create.url}
short.search.url=${short.search.url}

# order callback
orderPayAmount.callBackUrl=${orderPayAmount.callBackUrl}


# tc domain name
tc.domain.name=${tc.domain.name}

#risk
risk.url=${risk.url}

#lbs
http.lbs.root.url=${http.lbs.root.url}


wx.app.url=${wx.app.url}

coupon.recharge.topic=${coupon.recharge.topic}
coupon.recharge.group=${coupon.recharge.group}
coupon.recharge.topic_group=travel_carowner_coupon_recharge_group

coupon.expired.topic=${coupon.expired.topic}
coupon.expired.group=${coupon.expired.group}


finance.settlement.group=${finance.settlement.group}
finance.settlement.topic=${finance.settlement.topic}