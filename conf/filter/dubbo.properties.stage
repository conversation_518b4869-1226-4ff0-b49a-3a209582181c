#basic
sof.version=********
app.name=carownercore
app.version=*******-SNAPSHOT
app.type=web
sof-env=stage

#db
uniform.env=stage
uniform.skyCode=groundtravel.dsf.shared.mobility.carowner.core
uniform.dbName=TECarOwner

#dubbo
dubbo.application.name=groundtravel.dsf.shared.mobility.carowner.core
dubbo.registry.address=tcdsf://testdsf.tcent.cn
dubbo.container=spring,log4j
dubbo.service.gsname=dsf.shared.mobility.carowner.core
dubbo.service.port=11012
dubbo.service.registry.address=t.dsf2.17usoft.com
dubbo.service.deploy.container=tomcat
dubbo.service.version=*******

#turbomq
mq.nameSrvAddress=mqnameserver.t.17usoft.com:9876
driver.bill.unfreeze.topic=tc_shared_mobility_carowner_topic_driver_bill_unfreeze
driver.bill.unfreeze.group=tc_shared_mobility_carowner_group_driver_bill_unfreeze_t

order.create.top.topic=tc_shared_mobility_carowner_topic_order_create
order.create.top.group=order_create_group_t

api.call.retry.topic=tc_shared_mobility_carowner_topic_retry_task
api.call.retry.group=tc_shared_mobility_carowner_group_retry_task

update.status.topic=tc_shared_mobility_carowner_topic_order_status_change
update.status.group=tc_shared_mobility_carowner_group_order_status_change

driver.ready.topic=tc_shared_mobility_carowner_topic_driver_ready
driver.ready.group=tc_shared_mobility_carowner_group_driver_ready

order.settlement.group=tc_shared_mobility_carowner_group_order_settlement_t

order.complete.risk.topic=tc_shared_mobility_carowner_topic_order_complete_risk
order.complete.risk.group=tc_shared_mobility_carowner_group_order_complete_risk_t

order.risk.update.freeze.time.topic=car_risk_topic_real_car_owner_ht
order.risk.update.freeze.time.group=tc_shared_mobility_carowner_group_order_risk_update_freeze_time_t

#redis
redis.groupName=carowner

#http client
http_read_timeout=5000
connect_timeout=5000

# yeePay
yeePay.callBackUrl=http://tcmobileapi.17usoft.com/carownermobile/yeePay/test/callback

short.create.url=http://sapi.17usoft.com/tcsa-api/services/wsc/create
short.search.url=http://sapi.17usoft.com/tcsa-api/services/wsc/search

# order callback
orderPayAmount.callBackUrl=http://tcwireless.t.17usoft.com/carowneropenapi

# tc domain name
tc.domain.name=https://carsupplier-travel.t.17u.cn/supply/callback

#risk
risk.url=http://tcwireless.t.17usoft.com/carriskapi

#lbs
http.lbs.root.url=http://inner-api.travel.t.17usoft.com/car/lbs/baseapi/v1/

wx.app.url=https://groundtravel.t.17u.cn/mdriver/web/transferMini?schemeUrl=

#coupon
coupon.recharge.topic=shared_mobility_carowner_coupon_recharge_topic_stage
coupon.recharge.group=shared_mobility_carowner_coupon_recharge_group_stage
coupon.expired.topic=shared_mobility_carowner_coupon_expired_topic_stage
coupon.expired.group=shared_mobility_carowner_coupon_expired_group_stage

finance.settlement.group=shared_mobility_carowner_finance_group_stage
finance.settlement.topic=travel_usecar_sfc_finance_clear_topic_stage
