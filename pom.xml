<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.ly.travel.car.carowner</groupId>
    <artifactId>shared-mobility-carowner-core</artifactId>
    <version>1.0-SNAPSHOT</version>

    <name>LY shared-mobility-carowner-core parent</name>
    <url>http://www.ly.com</url>
    <description>LY shared-mobility-carowner-core Application Parent</description>
    <packaging>pom</packaging>

    <properties>
        <sof.version>*******</sof.version>
        <spring.version>5.3.27</spring.version>
        <spring.boot.version>2.1.13.RELEASE</spring.boot.version>

        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>

        <jacoco.skip>true</jacoco.skip>
        <jacoco.path>${project.build.directory}/jacoco-ut.exec</jacoco.path>

        <sonar.core.codeCoveragePlugin>jacoco</sonar.core.codeCoveragePlugin>
        <sonar.dynamicAnalysis>reuseReports</sonar.dynamicAnalysis>
        <sonar.jacoco.itReportPath>target/jacoco-it.exec</sonar.jacoco.itReportPath>
        <sonar.jacoco.reportPath>target/jacoco-ut.exec</sonar.jacoco.reportPath>

        <cglib.version>2.2</cglib.version>
        <asm.version>3.1</asm.version>
        <aspectj.version>1.8.14</aspectj.version>
        <velocity.version>1.7</velocity.version>
        <velocity.tools.version>2.0</velocity.tools.version>
        <dbcp.version>1.4</dbcp.version>
        <ibatis.version>2.3.4.726</ibatis.version>
        <ibatis-spring.version>1.1.0</ibatis-spring.version>
        <mysql.version>5.1.38</mysql.version>
        <mariadb.version>1.3.7</mariadb.version>
        <jaxb.version>2.3.0</jaxb.version>
        <slf4j.version>1.7.30</slf4j.version>
        <log4j.version>1-LY-EMPTY</log4j.version>
        <log4j2.version>2.17.2</log4j2.version>
        <junit.version>4.12</junit.version>
        <mockito.version>1.9.5</mockito.version>
        <easymock.version>3.1</easymock.version>
        <powermock.version>1.5</powermock.version>
        <easymock.powermock.version>1.4.10</easymock.powermock.version>
        <dal-new.version>3.6.6</dal-new.version>
        <lombok.version>1.16.20</lombok.version>
        <mapstruct.version>1.2.0.LY.Final</mapstruct.version>

        <!-- tcbase cache -->
        <tcbase-cache.version>3.6.8</tcbase-cache.version>
        <!-- toolkit -->
        <flight.toolkit.version>1.0.3.4.RELEASE</flight.toolkit.version>
        <!-- excel 导入 导出-->
        <easyexcel.version>3.2.1</easyexcel.version>
    </properties>

    <modules>

        <module>app/test</module>
        <module>app/facade</module>
        <module>app/facade-impl</module>
        <module>app/integration</module>
        <module>app/biz</module>
        <module>app/dal</module>
        <module>assembly</module>
        <module>app/common</module>
        <module>webdocs</module>

    </modules>

    <dependencyManagement>
        <dependencies>
<!--            <dependency>-->
<!--                <groupId>org.springframework.boot</groupId>-->
<!--                <artifactId>spring-boot-dependencies</artifactId>-->
<!--                <version>${spring.boot.version}</version>-->
<!--                <type>pom</type>-->
<!--                <scope>import</scope>-->
<!--            </dependency>-->
            <!-- project dependency -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.12.0</version>
            </dependency>
            <dependency>
                <groupId>com.ly.travel.car.carowner</groupId>
                <artifactId>shared-mobility-carowner-core-biz</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ly.travel.car.carowner</groupId>
                <artifactId>shared-mobility-carowner-core-dal</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ly.travel.car.carowner</groupId>
                <artifactId>shared-mobility-carowner-core-facade-impl</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ly.travel.car.carowner</groupId>
                <artifactId>shared-mobility-carowner-core-integration</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ly.travel.car.carowner</groupId>
                <artifactId>shared-mobility-carowner-core-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ly.travel.car.carowner</groupId>
                <artifactId>shared-mobility-carowner-core-facade</artifactId>
                <version>1.0.8-${versionSuffix}</version>
            </dependency>
            <dependency>
                <groupId>com.ly.travel.car.carowner</groupId>
                <artifactId>shared-mobility-carowner-core-test</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ly.travel.car.carowner</groupId>
                <artifactId>shared-mobility-carowner-core-assembly</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- SOF bom import -->
            <dependency>
                <groupId>com.ly.sof</groupId>
                <artifactId>sof-bom</artifactId>
                <version>${sof.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- BEGIN: Spring -->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-beans</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-core</artifactId>
                <version>${spring.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-tx</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-webmvc</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-aop</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context-support</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-jdbc</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-orm</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-aspects</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <!-- END: Spring -->

            <!-- Spring dependecy -->
            <dependency>
                <groupId>org.aspectj</groupId>
                <artifactId>aspectjweaver</artifactId>
                <version>${aspectj.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>org.aspectj</groupId>
                <artifactId>aspectjrt</artifactId>
                <version>${aspectj.version}</version>
            </dependency>
            <dependency>
                <groupId>cglib</groupId>
                <artifactId>cglib</artifactId>
                <version>${cglib.version}</version>
            </dependency>
            <dependency>
                <groupId>cglib</groupId>
                <artifactId>cglib-nodep</artifactId>
                <version>${cglib.version}</version>
            </dependency>
            <dependency>
                <groupId>asm</groupId>
                <artifactId>asm</artifactId>
                <version>${asm.version}</version>
            </dependency>

            <!-- ibatis -->
            <dependency>
                <groupId>com.ibatis</groupId>
                <artifactId>ibatis</artifactId>
                <version>${ibatis.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-2-spring</artifactId>
                <version>${ibatis-spring.version}</version>
            </dependency>

            <!-- tools -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity</artifactId>
                <version>${velocity.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-tools</artifactId>
                <version>${velocity.tools.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>servlet-api</artifactId>
                <version>2.5</version>
            </dependency>
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>jstl</artifactId>
                <version>1.2</version>
            </dependency>
            <dependency>
                <groupId>commons-logging</groupId>
                <artifactId>commons-logging</artifactId>
                <version>1.1.1</version>
            </dependency>

            <dependency>
                <groupId>commons-dbcp</groupId>
                <artifactId>commons-dbcp</artifactId>
                <version>${dbcp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ly.flight.toolkit</groupId>
                <artifactId>object-diff</artifactId>
                <version>${flight.toolkit.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ly.flight.toolkit</groupId>
                <artifactId>deploy-static-resource</artifactId>
                <version>${flight.toolkit.version}</version>
            </dependency>
            <dependency>
                <artifactId>sof-batis-gen-dependency</artifactId>
                <groupId>com.ly.flight.toolkit</groupId>
                <version>${flight.toolkit.version}</version>
            </dependency>

            <!--jaxb-->
            <dependency>
                <groupId>javax.xml.bind</groupId>
                <artifactId>jaxb-api</artifactId>
                <version>${jaxb.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-core</artifactId>
                <version>${jaxb.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-impl</artifactId>
                <version>${jaxb.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.activation</groupId>
                <artifactId>activation</artifactId>
                <version>1.1.1</version>
            </dependency>

            <!-- JDBC Driver -->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mariadb.jdbc</groupId>
                <artifactId>mariadb-java-client</artifactId>
                <version>${mariadb.version}</version>
            </dependency>

            <!-- log dependecies -->
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-log4j12</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>log4j</groupId>
                <artifactId>log4j</artifactId>
                <version>${log4j.version}</version>
            </dependency>

            <!-- log4j2 -->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${log4j2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-slf4j-impl</artifactId>
                <version>${log4j2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>jcl-over-slf4j</artifactId>
                <version>${slf4j.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-web</artifactId>
                <version>${log4j2.version}</version>
            </dependency>

            <!-- Test dependecies -->
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-all</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.easymock</groupId>
                <artifactId>easymock</artifactId>
                <version>${easymock.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-junit4</artifactId>
                <version>${powermock.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-api-easymock</artifactId>
                <version>${easymock.powermock.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-test</artifactId>
                <version>${spring.version}</version>
            </dependency>

            <!-- tcdbms -->
            <dependency>
                <groupId>com.ly.dal</groupId>
                <artifactId>dal-new</artifactId>
                <version>${dal-new.version}</version>
            </dependency>


            <dependency>
                <groupId>com.ly.tcbase</groupId>
                <artifactId>cache</artifactId>
                <version>${tcbase-cache.version}</version>
            </dependency>


            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.spring</groupId>
                <artifactId>spring-webmvc-velocity</artifactId>
                <version>1.4.3.18.RELEASE</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus</artifactId>
                <version>3.5.5</version>
            </dependency>

            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-spring</artifactId>
                <version>2.1.1</version>
            </dependency>
            <dependency>
                <groupId>io.lettuce</groupId>
                <artifactId>lettuce-core</artifactId>
                <version>5.2.1.RELEASE</version>
            </dependency>

            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>4.1.43.Final</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-buffer</artifactId>
                <version>4.1.43.Final</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec</artifactId>
                <version>4.1.43.Final</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-common</artifactId>
                <version>4.1.43.Final</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-handler</artifactId>
                <version>4.1.43.Final</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-transport</artifactId>
                <version>4.1.43.Final</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-resolver</artifactId>
                <version>4.1.43.Final</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-resolver-dns</artifactId>
                <version>4.1.43.Final</version>
            </dependency>
            <dependency>
                <groupId>org.apache.shiro</groupId>
                <artifactId>shiro-spring</artifactId>
                <version>1.4.0</version>
            </dependency>

            <dependency>
                <groupId>org.crazycake</groupId>
                <artifactId>shiro-redis</artifactId>
                <version>3.2.1</version>
            </dependency>


            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-expression</artifactId>
                <version>${spring.version}</version>
            </dependency>



            <dependency>
                <groupId>com.ly.tcbase</groupId>
                <artifactId>configcenterclient</artifactId>
                <version>6.2.8</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-autoconfigure</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>6.1.0.Alpha5</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>3.24.3</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>3.10.0</version>
            </dependency>
        </dependencies>


    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-scm-plugin</artifactId>
                <configuration>
                    <connectionType>connection</connectionType>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
            </plugin>
        </plugins>

        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>2.4</version>
                    <!-- <configuration> <archive> <manifestFile>src/main/resources/META-INF/MANIFEST.MF</manifestFile> </archive> </configuration> -->
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                    <configuration>
                        <source>${jdk.version}</source>
                        <target>${jdk.version}</target>
                        <encoding>UTF-8</encoding>
                        <showWarnings>true</showWarnings>
                        <fork>true</fork>
                        <compilerVersion>${jdk.version}</compilerVersion>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${mapstruct.version}</version>
                            </path>

                        </annotationProcessorPaths>
                        <compilerArgs>
                            <compilerArg>
                                -Amapstruct.defaultComponentModel=spring
                            </compilerArg>
                            <compilerArg>
                                -Amapstruct.unmappedTargetPolicy=IGNORE
                            </compilerArg>
                            <compilerArg>
                                -Amapstruct.suppressGeneratorTimestamp=true
                            </compilerArg>
                        </compilerArgs>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-assembly-plugin</artifactId>
                    <version>2.5.4</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>2.6</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>build-helper-maven-plugin</artifactId>
                    <version>1.9.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>2.7</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-release-plugin</artifactId>
                    <version>2.4.2</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-scm-plugin</artifactId>
                    <version>1.8.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>2.2</version>
                    <executions>
                        <execution>
                            <id>attach-sources</id>
                            <goals>
                                <goal>jar</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>2.8.1</version>
                    <configuration>
                        <argLine>-Xms512m -Xmx1024m</argLine>
                        <fork>true</fork>
                        <testFailureIgnore>true</testFailureIgnore>
                        <includes>
                            <include>**/*Test.java</include>
                        </includes>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>0.6.0.201210061924</version>
                    <configuration>
                        <skip>${jacoco.skip}</skip>
                        <destFile>${jacoco.path}</destFile>
                        <dataFile>${jacoco.path}</dataFile>
                        <sessionId>jacoco_coverage</sessionId>
                    </configuration>
                    <executions>
                        <execution>
                            <id>pre-test</id>
                            <phase>process-classes</phase>
                            <goals>
                                <goal>prepare-agent</goal>
                            </goals>
                            <configuration>
                                <propertyName>coverageAgent</propertyName>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>exec-maven-plugin</artifactId>
                    <version>1.2.1</version>
                </plugin>

                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>versions-maven-plugin</artifactId>
                    <version>2.0</version>
                    <dependencies>
                        <dependency>
                            <groupId>org.apache.maven.doxia</groupId>
                            <artifactId>doxia-core</artifactId>
                            <version>1.2</version>
                        </dependency>
                        <dependency>
                            <groupId>org.apache.maven.doxia</groupId>
                            <artifactId>doxia-site-renderer</artifactId>
                            <version>1.2</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-war-plugin</artifactId>
                    <version>2.4</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <reporting>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>findbugs-maven-plugin</artifactId>
                <version>3.0.5</version>
                <configuration>
                    <xmlOutput>true</xmlOutput>
                    <threshold>High</threshold>
                    <effort>Default</effort>
                    <relaxed>true</relaxed>
                    <findbugsXmlOutput>true</findbugsXmlOutput>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>taglist-maven-plugin</artifactId>
                <version>2.4</version>
                <configuration>
                    <tags>
                        <tag>TODO</tag>
                        <tag>@todo</tag>
                        <tag>FIXME</tag>
                        <tag>@fixme</tag>
                        <tag>@deprecated</tag>
                    </tags>
                </configuration>
            </plugin>
        </plugins>
    </reporting>

    <profiles>

        <profile>
            <id>dev</id>
            <properties>
                <versionSuffix>SNAPSHOT</versionSuffix>
            </properties>
        </profile>
        <profile>
            <id>qa</id>
            <properties>
                <versionSuffix>SNAPSHOT</versionSuffix>
            </properties>
        </profile>
        <profile>
            <id>uat</id>
            <properties>
                <versionSuffix>SNAPSHOT</versionSuffix>
            </properties>
        </profile>
        <profile>
            <id>stage</id>
            <properties>
                <versionSuffix>RELEASE</versionSuffix>
            </properties>
        </profile>
        <profile>
            <id>product</id>
            <properties>
                <versionSuffix>RELEASE</versionSuffix>
            </properties>
        </profile>

        <profile>
            <id>jdk8</id>
            <activation>
                <jdk>1.8</jdk>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <jdk.version>1.8</jdk.version>
                <maven.compiler.source>1.8</maven.compiler.source>
                <maven.compiler.target>1.8</maven.compiler.target>
                <maven.compiler.compilerVersion>1.8</maven.compiler.compilerVersion>
            </properties>
        </profile>



        <profile>
            <id>external</id>
            <repositories>
                <repository>
                    <id>aliyun</id>
                    <name>Aliyun Repository</name>
                    <url>https://maven.aliyun.com/nexus/content/groups/public/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                </repository>
            </repositories>
            <pluginRepositories>
                <pluginRepository>
                    <id>aliyun</id>
                    <name>Aliyun Repository</name>
                    <url>https://maven.aliyun.com/nexus/content/groups/public/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </pluginRepository>
            </pluginRepositories>
        </profile>
    </profiles>
    <repositories>
        <repository>
            <id>17usoft</id>
            <name>LY Share Repository</name>
            <url>http://nexus.17usoft.com/repository/mvn-all/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <url>http://nexus.17usoft.com/repository/mvn-all/</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>
    <distributionManagement>
        <snapshotRepository>
            <id>car-snapshot</id>
            <url>http://nexus.17usoft.com/repository/maven-intelcar-snapshot/</url>
            <uniqueVersion>false</uniqueVersion>
        </snapshotRepository>
        <repository>
            <id>car-release</id>
            <url>http://nexus.17usoft.com/repository/maven-intelcar-release/</url>
        </repository>
    </distributionManagement>
</project>
