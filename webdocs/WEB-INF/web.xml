<!DOCTYPE web-app PUBLIC
		"-//Sun Microsystems, Inc.//DTD Web Application 2.3//EN"
		"http://java.sun.com/dtd/web-app_2_3.dtd" >

<web-app id="Assets" xmlns="http://java.sun.com/xml/ns/javaee"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://java.sun.com/xml/ns/javaee
     http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd"
		 version="3.0" metadata-complete="true">
	<display-name>shared-mobility-pricing-rule</display-name>

	<!-- listener -->
	<listener>
		<listener-class>com.ly.sof.core.spring.web.SOFResourceFileListener</listener-class>
	</listener>
	<listener>
		<listener-class>com.ly.sof.core.spring.web.SOFContextLoaderListener</listener-class>
	</listener>
	<listener>
		<listener-class>org.springframework.web.context.request.RequestContextListener</listener-class>
	</listener>
	<context-param>
		<param-name>contextConfigLocation</param-name>
		<param-value>classpath*:META-INF/spring/*.xml</param-value>
	</context-param>
	<context-param>
		<param-name>sofConfLocation</param-name>
		<param-value>WEB-INF/dubbo.properties</param-value>
	</context-param>
	<context-param>
		<param-name>webAppRootKey</param-name>
		<param-value>shared-mobility-pricing-rule.root</param-value>
	</context-param>

</web-app>
